import {
  Activity,
  Anchor,
  Aperture,
  Archive,
  ArrowRight,
  Award,
  Banknote,
  BarChart3,
  Battery,
  Bell,
  Bookmark,
  Book,
  BookOpen,
  Box,
  Brain,
  Briefcase,
  Bug,
  Building,
  Calculator,
  Calendar,
  Camera,
  Car,
  CheckCircle,
  ChevronRight,
  Circle,
  Clock,
  Cloud,
  Code,
  Coffee,
  Cog,
  Compass,
  Computer,
  Copy,
  CreditCard,
  Crown,
  Database,
  Diamond,
  DollarSign,
  Download,
  Droplet,
  Earth,
  Edit,
  Eye,
  Facebook,
  FileText,
  Filter,
  Flame,
  Flag,
  Folder,
  Gamepad2,
  Gem,
  Gift,
  Github,
  Globe,
  GraduationCap,
  Hand,
  Hash,
  Headphones,
  Heart,
  HelpCircle,
  Home,
  Image,
  Inbox,
  Instagram,
  Key,
  Laptop,
  Layers,
  Leaf,
  Lightbulb,
  Link,
  Lock,
  Mail,
  MapPin,
  Medal,
  Megaphone,
  MessageCircle,
  Mic,
  Monitor,
  Moon,
  Mountain,
  Music,
  Navigation,
  Palette,
  Paperclip,
  PenTool,
  Phone,
  PieChart,
  Play,
  Plus,
  Printer,
  Puzzle,
  QrCode,
  Rocket,
  Save,
  Search,
  Send,
  Server,
  Settings,
  Share,
  Shield,
  ShoppingBag,
  Smartphone,
  Smile,
  Speaker,
  Square,
  Star,
  Sun,
  Tag,
  Target,
  Terminal,
  ThumbsUp,
  Timer,
  Trash,
  TrendingUp,
  Trophy,
  Truck,
  Twitter,
  Umbrella,
  Upload,
  User,
  Users,
  Video,
  Wallet,
  Wifi,
  Wind,
  Zap,
} from "lucide-react";
import React from "react";
import { Button } from "./ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "./ui/dialog";

// Define available icons with their names
const availableIcons = {
  activity: Activity,
  anchor: Anchor,
  aperture: Aperture,
  archive: Archive,
  arrowRight: ArrowRight,
  award: Award,
  banknote: Banknote,
  barChart: BarChart3,
  battery: Battery,
  bell: Bell,
  bookmark: Bookmark,
  book: Book,
  bookOpen: BookOpen,
  box: Box,
  brain: Brain,
  briefcase: Briefcase,
  bug: Bug,
  building: Building,
  calculator: Calculator,
  calendar: Calendar,
  camera: Camera,
  car: Car,
  checkCircle: CheckCircle,
  chevronRight: ChevronRight,
  circle: Circle,
  clock: Clock,
  cloud: Cloud,
  code: Code,
  coffee: Coffee,
  cog: Cog,
  compass: Compass,
  computer: Computer,
  copy: Copy,
  creditCard: CreditCard,
  crown: Crown,
  database: Database,
  diamond: Diamond,
  dollarSign: DollarSign,
  download: Download,
  droplet: Droplet,
  earth: Earth,
  edit: Edit,
  eye: Eye,
  facebook: Facebook,
  fileText: FileText,
  filter: Filter,
  fire: Flame,
  flag: Flag,
  folder: Folder,
  gamepad: Gamepad2,
  gem: Gem,
  gift: Gift,
  github: Github,
  globe: Globe,
  graduation: GraduationCap,
  hand: Hand,
  hash: Hash,
  headphones: Headphones,
  heart: Heart,
  helpCircle: HelpCircle,
  home: Home,
  image: Image,
  inbox: Inbox,
  instagram: Instagram,
  key: Key,
  laptop: Laptop,
  layers: Layers,
  leaf: Leaf,
  lightbulb: Lightbulb,
  link: Link,
  lock: Lock,
  mail: Mail,
  map: MapPin,
  medal: Medal,
  megaphone: Megaphone,
  messageCircle: MessageCircle,
  mic: Mic,
  monitor: Monitor,
  moon: Moon,
  mountain: Mountain,
  music: Music,
  navigation: Navigation,
  palette: Palette,
  paperclip: Paperclip,
  penTool: PenTool,
  phone: Phone,
  pieChart: PieChart,
  play: Play,
  plus: Plus,
  printer: Printer,
  puzzle: Puzzle,
  qrCode: QrCode,
  rocket: Rocket,
  save: Save,
  search: Search,
  send: Send,
  server: Server,
  settings: Settings,
  share: Share,
  shield: Shield,
  shoppingBag: ShoppingBag,
  smartphone: Smartphone,
  smile: Smile,
  speaker: Speaker,
  square: Square,
  star: Star,
  sun: Sun,
  tag: Tag,
  target: Target,
  terminal: Terminal,
  thumbsUp: ThumbsUp,
  timer: Timer,
  trash: Trash,
  trendingUp: TrendingUp,
  trophy: Trophy,
  truck: Truck,
  twitter: Twitter,
  umbrella: Umbrella,
  upload: Upload,
  user: User,
  users: Users,
  video: Video,
  wallet: Wallet,
  wifi: Wifi,
  wind: Wind,
  zap: Zap,
};

export type IconName = keyof typeof availableIcons;

interface IconSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (iconName: IconName) => void;
  selectedIcon?: IconName;
}

export const IconSelector = ({
  isOpen,
  onClose,
  onSelect,
  selectedIcon,
}: IconSelectorProps) => {
  const handleSelect = (iconName: IconName) => {
    onSelect(iconName);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Chọn Icon</DialogTitle>
        </DialogHeader>
        <div className="max-h-[600px] overflow-y-auto">
          <div className="grid grid-cols-10 gap-2 p-4">
            {Object.entries(availableIcons).map(([name, IconComponent]) => (
              <Button
                key={name}
                variant={selectedIcon === name ? "default" : "outline"}
                size="sm"
                onClick={() => handleSelect(name as IconName)}
                className="aspect-square h-10 w-10 p-1.5"
                title={name}
              >
                <IconComponent size={18} />
              </Button>
            ))}
          </div>
        </div>
        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={onClose}>
            Hủy
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper function to get icon component by name
export const getIconByName = (iconName: IconName) => {
  return availableIcons[iconName];
};

// Helper function to render icon
export const renderIcon = (iconName: IconName, size: number = 32) => {
  const IconComponent = availableIcons[iconName];
  return IconComponent ? <IconComponent size={size} /> : null;
};
