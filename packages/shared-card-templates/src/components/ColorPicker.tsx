import React, { useState } from "react";
import { HexColorPicker } from "react-colorful";
import { Button } from "./ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "./ui/dialog";
import { Label } from "./ui/label";

// Beautiful gradient presets with suggested text colors
export const gradientPresets = [
  {
    name: "Ocean Breeze",
    value: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
    textColor: "#ffffff",
    headerColor: "#f1f5f9",
  },
  {
    name: "Sunset Glow",
    value: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
    textColor: "#ffffff",
    headerColor: "#fef7ff",
  },
  {
    name: "Forest Dawn",
    value: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
    textColor: "#ffffff",
    headerColor: "#f0f9ff",
  },
  {
    name: "Purple Dream",
    value: "linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)",
    textColor: "#374151",
    headerColor: "#1f2937",
  },
  {
    name: "Golden Hour",
    value: "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)",
    textColor: "#374151",
    headerColor: "#1f2937",
  },
  {
    name: "Mint Fresh",
    value: "linear-gradient(135deg, #d1f5ee 0%, #a7f3d0 100%)",
    textColor: "#374151",
    headerColor: "#1f2937",
  },
  {
    name: "Rose Quartz",
    value: "linear-gradient(135deg, #fecaca 0%, #fda4af 100%)",
    textColor: "#374151",
    headerColor: "#1f2937",
  },
  {
    name: "Sky Blue",
    value: "linear-gradient(135deg, #dbeafe 0%, #93c5fd 100%)",
    textColor: "#374151",
    headerColor: "#1f2937",
  },
  {
    name: "Lavender Mist",
    value: "linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%)",
    textColor: "#374151",
    headerColor: "#1f2937",
  },
  {
    name: "Peach Sunrise",
    value: "linear-gradient(135deg, #fed7aa 0%, #fdba74 100%)",
    textColor: "#374151",
    headerColor: "#1f2937",
  },
  {
    name: "Emerald Sea",
    value: "linear-gradient(135deg, #a7f3d0 0%, #34d399 100%)",
    textColor: "#ffffff",
    headerColor: "#f0fdf4",
  },
  {
    name: "Cosmic Purple",
    value: "linear-gradient(135deg, #c084fc 0%, #8b5cf6 100%)",
    textColor: "#ffffff",
    headerColor: "#faf5ff",
  },
] as const;

interface ColorPickerProps {
  isOpen: boolean;
  onClose: () => void;
  onColorChange: (color: string) => void;
  currentColor: string;
  showGradientPresets?: boolean;
  onPresetSelect?: (preset: {
    value: string;
    textColor: string;
    headerColor: string;
  }) => void;
}

export const ColorPicker = ({
  isOpen,
  onClose,
  onColorChange,
  currentColor,
  showGradientPresets = true,
  onPresetSelect,
}: ColorPickerProps) => {
  const [selectedTab, setSelectedTab] = useState<"presets" | "custom">(
    showGradientPresets ? "presets" : "custom"
  );
  const [customColor, setCustomColor] = useState("#ffffff");

  const handlePresetSelect = (preset: (typeof gradientPresets)[number]) => {
    if (onPresetSelect) {
      // Use special callback for preset selection with text colors
      onPresetSelect({
        value: preset.value,
        textColor: preset.textColor,
        headerColor: preset.headerColor,
      });
    } else {
      // Fallback to regular color change
      onColorChange(preset.value);
    }
    onClose();
  };

  const handleCustomColorSelect = () => {
    onColorChange(customColor);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Chọn màu nền</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Tabs */}
          {showGradientPresets && (
            <div className="flex gap-2">
              <Button
                variant={selectedTab === "presets" ? "default" : "outline"}
                onClick={() => setSelectedTab("presets")}
                size="sm"
              >
                Gradient có sẵn
              </Button>
              <Button
                variant={selectedTab === "custom" ? "default" : "outline"}
                onClick={() => setSelectedTab("custom")}
                size="sm"
              >
                Màu tùy chỉnh
              </Button>
            </div>
          )}

          {/* Preset Gradients */}
          {showGradientPresets && selectedTab === "presets" && (
            <div className="grid grid-cols-3 gap-3">
              {gradientPresets.map((preset) => (
                <div
                  key={preset.name}
                  onClick={() => handlePresetSelect(preset)}
                  className="cursor-pointer rounded-lg border-2 border-transparent p-3 text-center transition-all hover:scale-105 hover:border-blue-500"
                  style={{ background: preset.value }}
                >
                  <div className="font-medium text-white text-xs drop-shadow-md">
                    {preset.name}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Custom Color Picker */}
          {(!showGradientPresets || selectedTab === "custom") && (
            <div className="space-y-4">
              <div className="flex justify-center">
                <HexColorPicker color={customColor} onChange={setCustomColor} />
              </div>
              <div className="text-center">
                <div className="mb-2 text-gray-600 text-sm">Màu đã chọn:</div>
                <div
                  className="mx-auto h-16 w-32 rounded border border-gray-300"
                  style={{ backgroundColor: customColor }}
                />
                <div className="mt-2 font-mono text-gray-500 text-sm">
                  {customColor}
                </div>
              </div>
              <div className="flex justify-center">
                <Button onClick={handleCustomColorSelect}>Áp dụng màu</Button>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={onClose}>
            Hủy
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
