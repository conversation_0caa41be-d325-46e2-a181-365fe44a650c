import React, { useState } from "react";
import { CardSelector } from "./CardSelector";
import { Card<PERSON>enderer } from "./CardRenderer";
import { CardProps, CardTemplate } from "./types";

export const Demo = () => {
  const [selectedCard, setSelectedCard] = useState<CardProps | null>(null);
  const [showSelector, setShowSelector] = useState(true);

  const handleCardSelect = (card: CardProps) => {
    setSelectedCard(card);
    setShowSelector(false);
  };

  const handleEdit = () => {
    setShowSelector(true);
  };

  const handleCancel = () => {
    setShowSelector(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="mx-auto max-w-6xl">
        <h1 className="mb-8 text-center font-bold text-3xl">
          Card Template Demo
        </h1>

        {showSelector ? (
          <CardSelector
            onCardSelect={handleCardSelect}
            initialParams={selectedCard || undefined}
            onCancel={selectedCard ? handleCancel : undefined}
          />
        ) : selectedCard ? (
          <div className="text-center">
            <div className="mb-8">
              <CardRenderer {...selectedCard} />
            </div>
            <div className="space-x-4">
              <button
                type="button"
                onClick={handleEdit}
                className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
              >
                Chỉnh sửa
              </button>
              <button
                type="button"
                onClick={() => {
                  setSelectedCard(null);
                  setShowSelector(true);
                }}
                className="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
              >
                Tạo card mới
              </button>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
};
