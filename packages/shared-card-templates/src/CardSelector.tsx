import React, { useState } from "react";
import { ColorPicker, gradientPresets } from "./components/ColorPicker";
import { IconName, IconSelector, renderIcon } from "./components/IconSelector";
import { Button } from "./components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "./components/ui/card";
import { Checkbox } from "./components/ui/checkbox";
import { Input } from "./components/ui/input";
import { Label } from "./components/ui/label";
import { Textarea } from "./components/ui/textarea";
import { CardProps, CardTemplate } from "./types";

interface CardSelectorProps {
  onCardSelect: (card: CardProps) => void;
  initialParams?: CardProps;
  onCancel?: () => void;
}

export const CardSelector: React.FC<CardSelectorProps> = ({
  onCardSelect,
  initialParams,
  onCancel,
}) => {
  const [title, setTitle] = useState(initialParams?.title || "Card tiêu đề");
  const [content, setContent] = useState(
    initialParams?.content || "Nội dung mẫu"
  );
  const [icon, setIcon] = useState<IconName>(
    (initialParams?.icon as IconName) || "book"
  );
  const [bg, setBg] = useState(initialParams?.bg || gradientPresets[0].value);
  const [textColor, setTextColor] = useState(
    initialParams?.textColor || "#374151"
  );
  const [headerColor, setHeaderColor] = useState(
    initialParams?.headerColor || "#1f2937"
  );
  const [header, setHeader] = useState(initialParams?.header || "");
  const [imageUrl, setImageUrl] = useState(initialParams?.imageUrl || "");
  const [showHeader, setShowHeader] = useState(!!initialParams?.header);
  const [showImage, setShowImage] = useState(!!initialParams?.imageUrl);

  const [isIconSelectorOpen, setIsIconSelectorOpen] = useState(false);
  const [isColorPickerOpen, setIsColorPickerOpen] = useState(false);
  const [isTextColorPickerOpen, setIsTextColorPickerOpen] = useState(false);
  const [isHeaderColorPickerOpen, setIsHeaderColorPickerOpen] = useState(false);

  const handleSubmit = () => {
    const cardData: CardProps = {
      template: CardTemplate.Single,
      title,
      content,
      icon,
      bg,
      textColor,
      headerColor,
      ...(showHeader && header && { header }),
      ...(showImage && imageUrl && { imageUrl }),
    };
    onCardSelect(cardData);
  };

  return (
    <div className="p-4">
      <div className="mx-auto max-w-4xl">
        <h3 className="mb-4 text-center font-semibold text-xl">Tạo Card</h3>

        <div className="flex flex-row-reverse items-start gap-5">
          {/* Preview */}
          <div
            className="flex min-w-[240px] max-w-sm flex-col items-center rounded-lg border border-gray-300 p-4 text-center"
            style={{ background: bg }}
          >
            {showHeader && header && (
              <div
                className="mb-2 w-full border-gray-300 border-b pb-2 font-bold text-sm"
                style={{ color: headerColor }}
              >
                {header}
              </div>
            )}

            {showImage && imageUrl && (
              <img
                src={imageUrl}
                alt={title}
                className="mb-2 max-w-full rounded"
              />
            )}

            <div className="mb-2">{renderIcon(icon)}</div>
            <div
              className="mb-2 font-bold text-lg"
              style={{ color: textColor }}
            >
              {title}
            </div>
            <div className="text-sm" style={{ color: textColor }}>
              {content}
            </div>
          </div>

          {/* Form */}
          <Card className="w-full max-w-4xl">
            <CardHeader>
              <CardTitle className="text-lg">Tùy chỉnh Card</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Icon</Label>
                <Button
                  variant="outline"
                  onClick={() => setIsIconSelectorOpen(true)}
                  className="w-full justify-start"
                >
                  <div className="flex items-center gap-2">
                    {renderIcon(icon, 16)}
                    <span>Chọn icon</span>
                  </div>
                </Button>
              </div>

              <div className="space-y-2">
                <Label>Tiêu đề</Label>
                <Input
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Nhập tiêu đề"
                />
              </div>

              <div className="space-y-2">
                <Label>Nội dung</Label>
                <Textarea
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="Nhập nội dung"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={showHeader}
                    onCheckedChange={(checked: boolean) =>
                      setShowHeader(!!checked)
                    }
                  />
                  <Label>Thêm header</Label>
                </div>
                {showHeader && (
                  <Input
                    value={header}
                    onChange={(e) => setHeader(e.target.value)}
                    placeholder="Nhập header"
                  />
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={showImage}
                    onCheckedChange={(checked: boolean) =>
                      setShowImage(!!checked)
                    }
                  />
                  <Label>Thêm hình ảnh</Label>
                </div>
                {showImage && (
                  <Input
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                    placeholder="URL hình ảnh"
                  />
                )}
              </div>

              <div className="space-y-2">
                <Label>Màu nền</Label>
                <Button
                  variant="outline"
                  onClick={() => setIsColorPickerOpen(true)}
                  className="w-full justify-start"
                >
                  <div className="flex items-center gap-2">
                    <div
                      className="h-4 w-8 rounded border"
                      style={{ background: bg }}
                    />
                    <span>Chọn màu nền</span>
                  </div>
                </Button>
              </div>

              <div className="space-y-2">
                <Label>Màu chữ</Label>
                <Button
                  variant="outline"
                  onClick={() => setIsTextColorPickerOpen(true)}
                  className="w-full justify-start"
                >
                  <div className="flex items-center gap-2">
                    <div
                      className="h-4 w-8 rounded border"
                      style={{ backgroundColor: textColor }}
                    />
                    <span>Chọn màu chữ</span>
                  </div>
                </Button>
              </div>

              <div className="space-y-2">
                <Label>Màu header</Label>
                <Button
                  variant="outline"
                  onClick={() => setIsHeaderColorPickerOpen(true)}
                  className="w-full justify-start"
                >
                  <div className="flex items-center gap-2">
                    <div
                      className="h-4 w-8 rounded border"
                      style={{ backgroundColor: headerColor }}
                    />
                    <span>Chọn màu header</span>
                  </div>
                </Button>
              </div>

              <div className="flex justify-between pt-4">
                {onCancel && (
                  <Button variant="outline" onClick={onCancel}>
                    Hủy
                  </Button>
                )}
                <Button onClick={handleSubmit} className="ml-auto">
                  {initialParams ? "Cập nhật" : "Tạo card"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <IconSelector
        isOpen={isIconSelectorOpen}
        onClose={() => setIsIconSelectorOpen(false)}
        onSelect={setIcon}
        selectedIcon={icon}
      />

      <ColorPicker
        isOpen={isColorPickerOpen}
        onClose={() => setIsColorPickerOpen(false)}
        onColorChange={setBg}
        currentColor={bg}
        onPresetSelect={(preset) => {
          setBg(preset.value);
          setTextColor(preset.textColor);
          setHeaderColor(preset.headerColor);
        }}
      />

      <ColorPicker
        isOpen={isTextColorPickerOpen}
        onClose={() => setIsTextColorPickerOpen(false)}
        onColorChange={setTextColor}
        currentColor={textColor}
        showGradientPresets={false}
      />

      <ColorPicker
        isOpen={isHeaderColorPickerOpen}
        onClose={() => setIsHeaderColorPickerOpen(false)}
        onColorChange={setHeaderColor}
        currentColor={headerColor}
        showGradientPresets={false}
      />
    </div>
  );
};
