import React from "react";
import { CardProps } from "../types";
import { renderIcon, IconName } from "../components/IconSelector";

export const SingleCard = ({
  title,
  content,
  icon = "book",
  bg = "linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)",
  textColor = "#374151",
  headerColor = "#1f2937",
  header,
  imageUrl,
  ...props
}: CardProps) => {
  // Parse content if it's a string with commas (from CardSelector)
  const contentArray =
    typeof content === "string" ? content.split(", ") : [content];

  return (
    <div
      className="w-full min-w-[200px] max-w-[320px] rounded-lg border border-gray-300"
      style={{ background: bg }}
    >
      {/* Optional Header */}
      {header && (
        <div
          className="border-b border-gray-300 font-bold p-4 text-center text-sm"
          style={{ color: headerColor }}
        >
          {header}
        </div>
      )}

      <div className="p-4 text-center">
        {/* Optional Image */}
        {imageUrl && (
          <img src={imageUrl} alt={title} className="mb-2 max-w-full rounded" />
        )}

        {/* Icon */}
        <div className="mb-2">
          {renderIcon((icon || "book") as IconName, 32)}
        </div>

        {/* Title */}
        <div className="font-bold mb-2 text-lg" style={{ color: textColor }}>
          {title}
        </div>

        {/* Content */}
        <ul className="list-none m-0 p-0">
          {contentArray.map((c, i) => (
            <li key={i} className="mb-1 text-sm" style={{ color: textColor }}>
              {c}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};
