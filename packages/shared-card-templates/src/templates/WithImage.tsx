import React from "react";
import { CardProps } from "../types";
import { renderIcon, IconName } from "../components/IconSelector";

export const WithImageCard = ({
  title,
  content,
  imageUrl,
  icon = "star",
  bg = "linear-gradient(135deg, #fff7ae 0%, #ffd6e0 100%)",
  ...props
}: CardProps) => {
  // Parse content if it's a string with commas (from CardSelector)
  const contentArray =
    typeof content === "string" ? content.split(", ") : [content];

  return (
    <div
      className="w-full min-w-[200px] max-w-[320px] rounded-lg border border-gray-300 p-4 text-center"
      style={{ background: bg }}
    >
      {imageUrl && (
        <img src={imageUrl} alt={title} className="mb-2 max-w-full" />
      )}
      <div className="mb-2">{renderIcon((icon || "star") as IconName, 32)}</div>
      <div className="font-bold mb-2 text-lg">{title}</div>
      <ul className="list-none m-0 p-0">
        {contentArray.map((c, i) => (
          <li key={i} className="mb-1 text-gray-500 text-sm">
            {c}
          </li>
        ))}
      </ul>
    </div>
  );
};
