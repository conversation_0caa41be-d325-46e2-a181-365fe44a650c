import React from "react";
import { CardProps } from "../types";
import { renderIcon, IconName } from "../components/IconSelector";

export const WithHeaderCard = ({
  title,
  content,
  header,
  icon = "palette",
  bg = "linear-gradient(135deg, #d1f5ee 0%, #f0e7ff 100%)",
}: CardProps) => {
  // Parse content if it's a string with commas (from CardSelector)
  const contentArray =
    typeof content === "string" ? content.split(", ") : [content];

  return (
    <div
      className="w-full min-w-[200px] max-w-[320px] rounded-lg border border-gray-300"
      style={{ background: bg }}
    >
      {header && (
        <div className="border-b border-gray-300 p-4 text-center text-sm font-bold text-gray-700">
          {header}
        </div>
      )}
      <div className="p-4 text-center">
        <div className="mb-2">
          {renderIcon((icon || "palette") as IconName, 32)}
        </div>
        <div className="font-bold mb-2 text-lg">{title}</div>
        <ul className="list-none m-0 p-0">
          {contentArray.map((c, i) => (
            <li key={i} className="mb-1 text-gray-500 text-sm">
              {c}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};
