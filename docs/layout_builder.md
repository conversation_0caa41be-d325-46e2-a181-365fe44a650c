# Layout Builder System Documentation

## Overview

The Layout Builder System is a comprehensive drag-and-drop lesson editor that allows instructors to create interactive lessons with multiple slides, sections, and components. It's built using <PERSON>act, Jotai for state management, and TypeScript for type safety.

## Architecture

### 📁 Directory Structure
```
src/
├── store/
│   └── layout-builder.ts          # Jotai atoms for state management
├── components/lesson-editor/
│   ├── lesson-editor.tsx          # Main editor component
│   ├── types.ts                   # TypeScript interfaces
│   ├── utils.ts                   # Helper functions
│   ├── hooks/
│   │   └── useJotaiLessonState.ts # Custom hook for state access
│   └── components/
│       ├── LayoutChangeDialog.tsx  # Section layout change dialog
│       ├── ColorPicker.tsx        # Color selection component
│       └── ...                    # Other UI components
└── packages/shared-card-templates/ # Reusable card components
```

### 🏗️ Core Concepts

#### **Lesson Structure Hierarchy**
```
Lesson
├── Slide 1
│   ├── Section A (e.g., Header layout)
│   │   ├── Zone 1 (Title area)
│   │   │   └── Component (Text, Image, Video)
│   │   └── Zone 2 (Subtitle area)
│   │       └── Component (Text, Image, Video)
│   └── Section B (e.g., Two-column layout)
│       ├── Zone 1 (Left column)
│       │   └── Components...
│       └── Zone 2 (Right column)
│           └── Components...
├── Slide 2
└── ...
```

## State Management (Jotai Atoms)

### 📊 Data Atoms (State Storage)

#### Core Lesson Data
```typescript
// Main lesson data structure
export const lessonAtom = atom<Lesson>({
  id: "lesson-1",
  title: "New Lesson",
  description: "Create your lesson content",
  slides: [createDefaultSlide(1)],
});

// Current slide index tracker
export const currentSlideIndexAtom = atom<number>(0);

// Computed current slide
export const currentSlideAtom = atom((get) => {
  const lesson = get(lessonAtom);
  const currentIndex = get(currentSlideIndexAtom);
  return lesson.slides[currentIndex] || lesson.slides[0];
});
```

#### UI State Management
```typescript
// Component being edited
export const editingComponentAtom = atom<string | null>(null);

// Preview mode state
export const previewModeAtom = atom<PreviewMode>(false);

// Drag & Drop state
export const draggedComponentAtom = atom<DraggedComponent | null>(null);
export const dragOverZoneAtom = atom<DragOverZone | null>(null);

// Zone selection for floating picker
export const selectedZoneAtom = atom<SelectedZone | null>(null);
export const showFloatingPickerAtom = atom<boolean>(false);
export const pickerPositionAtom = atom<PickerPosition>({ x: 0, y: 0 });
```

### ⚡ Action Atoms (Operations)

#### Lesson Management
```typescript
// Update entire lesson
export const updateLessonAtom = atom(null, (_get, set, newLesson: Lesson) => {
  set(lessonAtom, newLesson);
});

// Add new slide
export const addSlideAtom = atom(null, (get, set) => {
  // Creates new slide and updates current slide index
});

// Remove slide with index adjustment
export const removeSlideAtom = atom(null, (get, set, slideIndex: number) => {
  // Removes slide and adjusts current index if needed
});
```

#### Section Operations
```typescript
// Add section to current slide
export const addSectionToSlideAtom = atom(null, (get, set, template: LayoutTemplate) => {
  // Creates section with zones based on template
});

// Remove section from slide
export const removeSectionFromSlideAtom = atom(null, (get, set, sectionId: string) => {
  // Removes section and all its components
});

// Change section layout with data migration
export const changeSectionLayoutAtom = atom(null, (get, set, params) => {
  // Changes layout and migrates existing components based on migration option
});
```

#### Component Operations
```typescript
// Add component to zone
export const addComponentToZoneAtom = atom(null, (get, set, componentData) => {
  // Creates new component and adds to specified zone
});

// Update component content
export const updateComponentContentAtom = atom(null, (get, set, updateData) => {
  // Updates component content (text, image, video, etc.)
});

// Remove component from zone
export const removeComponentFromZoneAtom = atom(null, (get, set, componentData) => {
  // Removes component from zone
});
```

#### UI Interactions
```typescript
// Zone selection for component picker
export const selectZoneAtom = atom(null, (get, set, zoneData) => {
  // Shows/hides floating picker, handles zone selection toggle
});

// Close floating picker
export const closePickerAtom = atom(null, (_get, set) => {
  // Closes picker and clears selection
});

// Drag & Drop operations
export const startDragAtom = atom(null, (_get, set, dragData: DraggedComponent) => {
  // Initiates drag operation
});

export const performDragDropAtom = atom(null, (get, set, dropData) => {
  // Complex drag & drop with position adjustment logic
});
```

## Component Architecture

### 🎯 Main Components

#### `LessonEditor` (Main Container)
- **Purpose**: Main editor interface
- **Responsibilities**: 
  - Render slides, sections, and zones
  - Handle drag & drop interactions
  - Manage component picker
  - Coordinate between different UI elements

#### `ColorPicker`
- **Purpose**: Color selection with gradient presets
- **Features**:
  - 12 predefined gradient presets
  - Automatic text/header color suggestions
  - `onPresetSelect` callback for applying colors

#### `LayoutChangeDialog`
- **Purpose**: Handle section layout changes
- **Migration Options**:
  - `discard`: Remove all existing components
  - `first-zone`: Move all components to first zone
  - `distribute`: Distribute components across zones

### 🔧 Custom Hooks

#### `useJotaiLessonState`
```typescript
export const useJotaiLessonState = () => {
  const [lesson, setLesson] = useAtom(lessonAtom);
  const [currentSlideIndex, setCurrentSlideIndex] = useAtom(currentSlideIndexAtom);
  const currentSlide = useAtomValue(currentSlideAtom);
  
  // Action functions
  const addComponent = useSetAtom(addComponentToZoneAtom);
  const removeComponent = useSetAtom(removeComponentFromZoneAtom);
  const updateContent = useSetAtom(updateComponentContentAtom);
  // ... other actions
  
  return {
    // State
    lesson,
    currentSlide,
    currentSlideIndex,
    
    // Actions
    addComponent,
    removeComponent,
    updateContent,
    // ... other actions
  };
};
```

## Type Definitions

### 📝 Core Types

```typescript
interface Lesson {
  id: string;
  title: string;
  description: string;
  slides: Slide[];
}

interface Slide {
  id: string;
  title: string;
  sections: LayoutSection[];
}

interface LayoutSection {
  id: string;
  name: string;
  template: LayoutTemplate;
  zones: Zone[];
}

interface Zone {
  id: string;
  components: LayoutComponent[];
}

interface LayoutComponent {
  id: string;
  type: 'text' | 'image' | 'video' | 'quiz' | 'card';
  content: string | object;
}

interface LayoutTemplate {
  name: string;
  icon: React.ComponentType;
  zones: ZoneTemplate[];
}
```

### 🎮 Interaction Types

```typescript
interface DraggedComponent {
  sectionId: string;
  zoneId: string;
  index: number;
}

interface DragOverZone {
  sectionId: string;
  zoneId: string;
  index: number;
}

interface SelectedZone {
  sectionId: string;
  zoneId: string;
}

type DataMigrationOption = 'discard' | 'first-zone' | 'distribute';
type PreviewMode = boolean | 'mobile' | 'tablet' | 'desktop';
```

## Key Features

### ✨ Drag & Drop System
- **Source**: Any component within any zone
- **Target**: Any zone in any section
- **Logic**: 
  - Prevents dropping in same position
  - Handles index adjustment for same-zone moves
  - Maintains component references during transfer

### 🎨 Layout Templates
- **Predefined layouts**: Header, Two-column, Three-column, etc.
- **Dynamic zones**: Each template defines zone structure
- **Migration support**: When changing layouts, existing components can be:
  - Discarded (removed)
  - Moved to first zone
  - Distributed across available zones

### 🎯 Component Picker
- **Floating UI**: Appears when zone is selected
- **Position tracking**: Follows zone selection
- **Component types**: Text, Image, Video, Quiz, Card

### 🔄 State Synchronization
- **Single source of truth**: All state in Jotai atoms
- **Immutable updates**: All operations create new state objects
- **Optimized renders**: Components only re-render when relevant atoms change

## Usage Examples

### Adding a Component
```typescript
const { addComponent } = useJotaiLessonState();

// Add text component to specific zone
addComponent({
  sectionId: "section-1",
  zoneId: "zone-1", 
  type: "text",
  content: "Hello World"
});
```

### Changing Section Layout
```typescript
const changeSectionLayout = useSetAtom(changeSectionLayoutAtom);

// Change to two-column layout, distribute existing components
changeSectionLayout({
  slideIndex: 0,
  sectionIndex: 1,
  newTemplate: twoColumnTemplate,
  migrationOption: "distribute"
});
```

### Drag & Drop Implementation
```typescript
// Start drag
const startDrag = useSetAtom(startDragAtom);
startDrag({
  sectionId: "section-1",
  zoneId: "zone-1",
  index: 0
});

// Perform drop
const performDrop = useSetAtom(performDragDropAtom);
performDrop({
  targetSectionId: "section-2",
  targetZoneId: "zone-1", 
  targetIndex: 1
});
```

## Best Practices

### 🏗️ State Management
1. **Use action atoms**: Never directly mutate lesson data
2. **Batch operations**: Combine related updates in single atom
3. **Type safety**: Always use TypeScript interfaces
4. **Error handling**: Check for null/undefined before operations

### 🎨 UI Components
1. **Separation of concerns**: UI components focus on rendering, atoms handle logic
2. **Prop drilling**: Use Jotai atoms instead of passing props deep
3. **Performance**: Use `useAtomValue` for read-only access
4. **Cleanup**: Clear drag state and selections appropriately

### 🔧 Development
1. **ID generation**: Use `generateId()` utility for unique IDs
2. **Immutability**: Always spread objects when creating updates
3. **Testing**: Test atoms independently from UI components
4. **Documentation**: Keep this doc updated when adding features

## Troubleshooting

### Common Issues

#### Drag & Drop Not Working
- Check if `startDragAtom` is called on drag start
- Verify `endDragAtom` is called on drag end
- Ensure drag data contains valid section/zone/index

#### Components Not Updating
- Verify atoms are being set, not mutated
- Check if component is using correct atom subscription
- Ensure immutable updates in action atoms

#### Layout Changes Lost
- Confirm `updateLessonAtom` is called after changes
- Check migration option is correctly applied
- Verify section/zone IDs are properly generated

### Debugging Tools
- **React DevTools**: Monitor component re-renders
- **Jotai DevTools**: Track atom state changes  
- **Browser DevTools**: Check console for errors
- **TypeScript**: Use strict mode for better error catching

## Migration Guide

### From Legacy State Management
If migrating from `useLessonState` hook:

1. Replace hook calls:
   ```typescript
   // Old
   const { lesson, updateLesson } = useLessonState();
   
   // New  
   const { lesson } = useJotaiLessonState();
   const updateLesson = useSetAtom(updateLessonAtom);
   ```

2. Update action calls:
   ```typescript
   // Old - direct state mutation
   updateLesson({ ...lesson, title: "New Title" });
   
   // New - through atoms
   const newLesson = { ...lesson, title: "New Title" };
   updateLesson(newLesson);
   ```

3. Replace component operations:
   ```typescript
   // Old - manual state updates
   const newComponents = [...zone.components, newComponent];
   updateZone(sectionId, zoneId, { components: newComponents });
   
   // New - action atoms
   addComponent({ sectionId, zoneId, type: "text", content: "..." });
   ```

---

*Last updated: August 29, 2025*
*Version: 1.0.0*
