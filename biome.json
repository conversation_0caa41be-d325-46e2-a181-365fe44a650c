{"$schema": "https://biomejs.dev/schemas/2.0.0-beta.1/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["**/*", "!**/src/routeTree.gen.ts", "!**/src/components/ui/*"]}, "formatter": {"enabled": true, "indentStyle": "space"}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"a11y": {"useFocusableInteractive": "off", "noSvgWithoutTitle": "off", "useKeyWithClickEvents": "off", "noLabelWithoutControl": "off"}, "correctness": {"noChildrenProp": "off"}, "nursery": {"useSortedClasses": {"level": "error", "fix": "safe", "options": {"attributes": ["classList"], "functions": ["clsx", "cva", "tw"]}}, "noImgElement": "off", "noHeadElement": "off"}, "suspicious": {"noArrayIndexKey": "off", "noGlobalIsNan": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}