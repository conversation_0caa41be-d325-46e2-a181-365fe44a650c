import { useMemo, useState } from "react";

export type SortDirection = "asc" | "desc" | null;

export interface SortState {
  column: string | null;
  direction: SortDirection;
}

type SortableValue = string | number | boolean | Date | null | undefined;

export interface UseSortableTableOptions<
  T extends Record<string, SortableValue | string[]>,
> {
  data: T[];
  defaultSort?: {
    column: keyof T;
    direction: "asc" | "desc";
  };
}

export function useSortableTable<
  T extends Record<string, SortableValue | string[]>,
>({ data, defaultSort }: UseSortableTableOptions<T>) {
  const [sortState, setSortState] = useState<SortState>({
    column: (defaultSort?.column as string) || null,
    direction: defaultSort?.direction || null,
  });

  const sortedData = useMemo(() => {
    if (!sortState.column || !sortState.direction) {
      return data;
    }

    return [...data].sort((a, b) => {
      const aValue = a[sortState.column!];
      const bValue = b[sortState.column!];

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return sortState.direction === "asc" ? 1 : -1;
      if (bValue == null) return sortState.direction === "asc" ? -1 : 1;

      // Handle dates
      if (aValue instanceof Date && bValue instanceof Date) {
        const result = aValue.getTime() - bValue.getTime();
        return sortState.direction === "asc" ? result : -result;
      }

      // Handle date strings (common format: DD/MM/YYYY or YYYY-MM-DD)
      if (typeof aValue === "string" && typeof bValue === "string") {
        const datePattern = /^\d{1,2}\/\d{1,2}\/\d{4}$|^\d{4}-\d{2}-\d{2}$/;
        if (datePattern.test(aValue) && datePattern.test(bValue)) {
          const aDate = new Date(
            aValue.includes("/")
              ? aValue.split("/").reverse().join("-")
              : aValue,
          );
          const bDate = new Date(
            bValue.includes("/")
              ? bValue.split("/").reverse().join("-")
              : bValue,
          );
          const result = aDate.getTime() - bDate.getTime();
          return sortState.direction === "asc" ? result : -result;
        }
      }

      // Handle numbers (including strings that represent numbers)
      const aNum =
        typeof aValue === "number"
          ? aValue
          : typeof aValue === "string"
            ? parseFloat(aValue)
            : NaN;
      const bNum =
        typeof bValue === "number"
          ? bValue
          : typeof bValue === "string"
            ? parseFloat(bValue)
            : NaN;

      if (!isNaN(aNum) && !isNaN(bNum)) {
        const result = aNum - bNum;
        return sortState.direction === "asc" ? result : -result;
      }

      // Handle strings (case-insensitive)
      const aStr = String(aValue).toLowerCase();
      const bStr = String(bValue).toLowerCase();
      const result = aStr.localeCompare(bStr);

      return sortState.direction === "asc" ? result : -result;
    });
  }, [data, sortState]);

  const handleSort = (column: string) => {
    setSortState((prev) => {
      if (prev.column === column) {
        // Cycle through: asc -> desc -> none
        if (prev.direction === "asc") {
          return { column, direction: "desc" };
        } else if (prev.direction === "desc") {
          return { column: null, direction: null };
        } else {
          return { column, direction: "asc" };
        }
      } else {
        // New column, start with asc
        return { column, direction: "asc" };
      }
    });
  };

  const getSortIcon = (column: string): "asc" | "desc" | "none" => {
    if (sortState.column === column) {
      return sortState.direction || "none";
    }
    return "none";
  };

  return {
    sortedData,
    sortState,
    handleSort,
    getSortIcon,
  };
}
