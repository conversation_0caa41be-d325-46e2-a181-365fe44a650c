import { QueryClient } from "@tanstack/react-query";
import { createRouter as createTanStackRouter } from "@tanstack/react-router";
import { routerWithQueryClient } from "@tanstack/react-router-with-query";
import { routeTree } from "./routeTree.gen";
import { getRouterBasepath } from "./utils/resolve-basepath";

export function createRouter(pathname?: string) {
  const queryClient = new QueryClient();

  const router = createTanStackRouter({
    routeTree,
    scrollRestoration: true,
    defaultPreload: "intent",
    defaultErrorComponent: () => <div>Something went wrong</div>,
    defaultNotFoundComponent: () => <div>Not Found</div>,
    context: {
      queryClient,
    },
    basepath: getRouterBasepath(pathname),
  });

  return routerWithQueryClient(router, queryClient);
}

declare module "@tanstack/react-router" {
  interface Register {
    router: ReturnType<typeof createRouter>;
  }
}
