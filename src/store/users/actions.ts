import { atom } from "jotai";
import type { UserFilterType } from "@/types/users";
import {
  currentPageAtom,
  currentPageUsersAtom,
  itemsPerPageAtom,
  selectedUsersAtom,
  userFiltersAtom,
} from "./values";

// ============================================================================
// Action Helper Atoms - Write-only atoms for common operations
// ============================================================================

// Filter change action atom
export const updateFilterAtom = atom(
  null,
  (get, set, { type, value }: { type: UserFilterType; value: string }) => {
    set(currentPageAtom, 1); // Reset to page 1 when filter changes

    const currentFilters = get(userFiltersAtom);
    set(userFiltersAtom, {
      ...currentFilters,
      [type]: value,
    });
  },
);

// Select all users action atom
export const selectAllUsersAtom = atom(null, (get, set, checked: boolean) => {
  if (checked) {
    const currentUsers = get(currentPageUsersAtom);
    set(
      selectedUsersAtom,
      currentUsers.map((user) => user.id.toString()),
    );
  } else {
    set(selectedUsersAtom, []);
  }
});

// Select single user action atom
export const selectUserAtom = atom(
  null,
  (get, set, { userId, checked }: { userId: string; checked: boolean }) => {
    const selectedUsers = get(selectedUsersAtom);

    if (checked) {
      set(selectedUsersAtom, [...selectedUsers, userId]);
    } else {
      set(
        selectedUsersAtom,
        selectedUsers.filter((id) => id !== userId),
      );
    }
  },
);

// Page change action atom
export const changePageAtom = atom(null, (get, set, page: number) => {
  set(currentPageAtom, page);
});

// Items per page change action atom
export const changeItemsPerPageAtom = atom(null, (get, set, items: number) => {
  set(itemsPerPageAtom, items);
  set(currentPageAtom, 1); // Reset to page 1 when items per page changes
});

// Bulk action handler atom
export const bulkActionAtom = atom(null, (get, set, action: string) => {
  const selectedUsers = get(selectedUsersAtom);
  console.log(`Bulk action ${action} for users:`, selectedUsers);
  // TODO: Implement bulk actions logic
  set(selectedUsersAtom, []); // Clear selection after action
});
