import type { User } from "@/types/users";

// ============================================================================
// Dialog State Types
// ============================================================================

export type BulkActionType =
  | "change-status"
  | "change-department"
  | "reset-password"
  | "delete";

export interface DialogState {
  open: boolean;
  userId?: string;
  user?: User;
}

export interface BulkActionDialogState {
  open: boolean;
  action: BulkActionType;
  selectedCount: number;
}
