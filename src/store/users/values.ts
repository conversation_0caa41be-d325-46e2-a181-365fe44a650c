import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { departments, importHistory, users } from "@/components/users/mockData";
import type {
  RoleInfo,
  User,
  UserActionsType,
  UserFiltersState,
} from "@/types/users";

// ============================================================================
// Base Data Atoms
// ============================================================================

// Users data atom - contains all users from mock data
export const usersDataAtom = atom<User[]>(users);

// Departments data atom - contains all departments
export const departmentsDataAtom = atom<string[]>(departments);

// Import history data atom
export const importHistoryDataAtom = atom(importHistory);

// ============================================================================
// Filter State Atoms
// ============================================================================

// User filters state atom - handles search and filter criteria
export const userFiltersAtom = atomWithReset<UserFiltersState>({
  searchTerm: "",
  filterStatus: "all",
  filterDepartment: "all",
  filterRole: "all",
});

// ============================================================================
// Pagination State Atoms
// ============================================================================

// Current page number atom
export const currentPageAtom = atomWithReset(1);

// Items per page atom
export const itemsPerPageAtom = atomWithReset(10);

// ============================================================================
// Selection State Atoms
// ============================================================================

// Selected users atom - stores IDs of selected users
export const selectedUsersAtom = atomWithReset<string[]>([]);

// ============================================================================
// Dialog State Atoms - Moved to dialogs.ts
// ============================================================================

// Dialog atoms have been moved to dialogs.ts for better organization

// ============================================================================
// Computed/Derived Atoms
// ============================================================================

// Filtered users atom - applies filters to users data
export const filteredUsersAtom = atom((get) => {
  const users = get(usersDataAtom);
  const filters = get(userFiltersAtom);

  return users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(filters.searchTerm.toLowerCase());
    const matchesStatus =
      filters.filterStatus === "all" || user.status === filters.filterStatus;
    const matchesDepartment =
      filters.filterDepartment === "all" ||
      user.department === filters.filterDepartment;
    const matchesRole =
      filters.filterRole === "all" || user.role === filters.filterRole;

    return matchesSearch && matchesStatus && matchesDepartment && matchesRole;
  });
});

// Pagination info atom - calculates pagination values
export const paginationInfoAtom = atom((get) => {
  const filteredUsers = get(filteredUsersAtom);
  const currentPage = get(currentPageAtom);
  const itemsPerPage = get(itemsPerPageAtom);

  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  return {
    totalUsers: filteredUsers.length,
    totalPages,
    startIndex,
    endIndex,
  };
});

// Current page users atom - gets the current page of users based on pagination
export const currentPageUsersAtom = atom((get) => {
  const filteredUsers = get(filteredUsersAtom);
  const { startIndex, endIndex } = get(paginationInfoAtom);
  return filteredUsers.slice(startIndex, endIndex);
});

// Get user by ID atom - retrieves a specific user by their ID
export const getUserByIdAtom = atom((get) => {
  const users = get(usersDataAtom);
  return (userId: string | number): User | undefined => {
    const stringId = typeof userId === "number" ? userId.toString() : userId;
    return users.find((user) => user.id === stringId);
  };
});
