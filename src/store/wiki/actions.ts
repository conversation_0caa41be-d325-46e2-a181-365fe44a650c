import { atom } from "jotai";
import { toast } from "sonner";
import { updateWikiStructure } from "@/services/wiki";
import type { WikiPage, WikiSection } from "@/types/wiki";
import {
  creatingPageAtom,
  creatingSectionAtom,
  deletePageErrorAtom,
  deleteSectionErrorAtom,
  editingPageAtom,
  editingSectionAtom,
  editPageNameAtom,
  editSectionNameAtom,
  isCreatePageDialogOpenAtom,
  isDeletingPageAtom,
  isDeletingSectionAtom,
  isUpdatingPageAtom,
  isUpdatingSectionAtom,
  pageParentInfoAtom,
  sectionsErrorAtom,
  selectedSectionIdAtom,
  updatePageErrorAtom,
  updateSectionErrorAtom,
  wikiSectionsAtom,
} from "./values";

// ============================================================================
// Wiki Data Management Actions
// ============================================================================

// Initialize wiki sections from data
export const initializeWikiSectionsAtom = atom(
  null,
  (get, set, wikiData: string) => {
    try {
      const parsedSections = JSON.parse(wikiData || "[]");
      set(wikiSectionsAtom, parsedSections);
      set(sectionsErrorAtom, null);
    } catch (error) {
      console.error("Failed to parse wiki data:", error);
      set(wikiSectionsAtom, []);
      set(sectionsErrorAtom, "Failed to parse wiki data");
    }
  },
);

// ============================================================================
// Section Management Actions
// ============================================================================

// Start editing a section
export const startEditSectionAtom = atom(
  null,
  (get, set, sectionIndex: number) => {
    const sections = get(wikiSectionsAtom);
    if (sectionIndex >= 0 && sectionIndex < sections.length) {
      set(editingSectionAtom, sectionIndex);
      set(editSectionNameAtom, sections[sectionIndex].title);
    }
  },
);

// Save edited section
export const saveEditSectionAtom = atom(
  null,
  async (get, set, refetchCallback?: () => void) => {
    const editingSectionIndex = get(editingSectionAtom);
    const newName = get(editSectionNameAtom);

    if (editingSectionIndex === null || !newName.trim()) return;

    set(isUpdatingSectionAtom, true);
    set(updateSectionErrorAtom, null);

    try {
      const sections = get(wikiSectionsAtom);
      const updatedSections = [...sections];
      updatedSections[editingSectionIndex].title = newName.trim();

      const res = await updateWikiStructure({ wikiStructure: updatedSections });
      if (res) {
        set(wikiSectionsAtom, JSON.parse(res?.wiki || "[]"));
        toast.success("Section updated successfully");
        refetchCallback?.();
      }
    } catch (error) {
      set(updateSectionErrorAtom, "Failed to update section");
      toast.error("Failed to update section");
    } finally {
      set(isUpdatingSectionAtom, false);
      set(editingSectionAtom, null);
      set(editSectionNameAtom, "");
    }
  },
);

// Cancel editing section
export const cancelEditSectionAtom = atom(null, (get, set) => {
  set(editingSectionAtom, null);
  set(editSectionNameAtom, "");
});

// Delete a section
export const deleteSectionAtom = atom(
  null,
  async (get, set, sectionIndex: number, refetchCallback?: () => void) => {
    set(isDeletingSectionAtom, true);
    set(deleteSectionErrorAtom, null);

    try {
      const sections = get(wikiSectionsAtom);
      const updatedSections = [...sections];
      updatedSections.splice(sectionIndex, 1);

      const res = await updateWikiStructure({ wikiStructure: updatedSections });
      if (res) {
        set(wikiSectionsAtom, JSON.parse(res?.wiki || "[]"));
        toast.success("Section deleted successfully");
        refetchCallback?.();
      }
    } catch (error) {
      set(deleteSectionErrorAtom, "Failed to delete section");
      toast.error("Failed to delete section");
    } finally {
      set(isDeletingSectionAtom, false);
    }
  },
);

// ============================================================================
// Page Management Actions
// ============================================================================

// Start editing a page
export const startEditPageAtom = atom(
  null,
  (get, set, pageId: string, currentTitle: string) => {
    set(editingPageAtom, pageId);
    set(editPageNameAtom, currentTitle);
  },
);

// Save edited page
export const saveEditPageAtom = atom(
  null,
  async (get, set, refetchCallback?: () => void) => {
    const editingPageId = get(editingPageAtom);
    const newName = get(editPageNameAtom);

    if (!editingPageId || !newName.trim()) return;

    set(isUpdatingPageAtom, true);
    set(updatePageErrorAtom, null);

    try {
      const sections = get(wikiSectionsAtom);
      const updatedSections = [...sections];

      const findAndUpdatePage = (pages: WikiPage[]): boolean => {
        for (let i = 0; i < pages.length; i++) {
          if (pages[i].wiki_section_id === editingPageId) {
            pages[i].title = newName.trim();
            return true;
          }
          if (pages[i].pages && findAndUpdatePage(pages[i].pages!)) {
            return true;
          }
        }
        return false;
      };

      updatedSections.forEach((section) => {
        findAndUpdatePage(section.pages);
      });

      const res = await updateWikiStructure({ wikiStructure: updatedSections });
      if (res) {
        set(wikiSectionsAtom, JSON.parse(res?.wiki || "[]"));
        toast.success("Page updated successfully");
        refetchCallback?.();
      }
    } catch (error) {
      set(updatePageErrorAtom, "Failed to update page");
      toast.error("Failed to update page");
    } finally {
      set(isUpdatingPageAtom, false);
      set(editingPageAtom, null);
      set(editPageNameAtom, "");
    }
  },
);

// Cancel editing page
export const cancelEditPageAtom = atom(null, (get, set) => {
  set(editingPageAtom, null);
  set(editPageNameAtom, "");
});

// Delete a page
export const deletePageAtom = atom(
  null,
  async (
    get,
    set,
    sectionIndex: number,
    pageId: string,
    refetchCallback?: () => void,
  ) => {
    set(isDeletingPageAtom, true);
    set(deletePageErrorAtom, null);

    try {
      const sections = get(wikiSectionsAtom);
      const updatedSections = [...sections];

      const findAndDeletePage = (pages: WikiPage[]): boolean => {
        for (let i = pages.length - 1; i >= 0; i--) {
          if (pages[i].wiki_section_id === pageId) {
            pages.splice(i, 1);
            return true;
          }
          if (pages[i].pages && findAndDeletePage(pages[i].pages!)) {
            return true;
          }
        }
        return false;
      };

      findAndDeletePage(updatedSections[sectionIndex].pages);

      const res = await updateWikiStructure({ wikiStructure: updatedSections });
      if (res) {
        set(wikiSectionsAtom, JSON.parse(res?.wiki || "[]"));
        toast.success("Page deleted successfully");
        refetchCallback?.();
      }
    } catch (error) {
      set(deletePageErrorAtom, "Failed to delete page");
      toast.error("Failed to delete page");
    } finally {
      set(isDeletingPageAtom, false);
    }
  },
);

// ============================================================================
// Dialog Management Actions
// ============================================================================

// Open create page dialog
export const openCreatePageDialogAtom = atom(
  null,
  (
    get,
    set,
    parentId: string,
    parentName: string,
    parentType: "section" | "page",
    sectionIndex?: number,
  ) => {
    set(creatingPageAtom, parentId);
    set(pageParentInfoAtom, {
      name: parentName,
      type: parentType,
      index: sectionIndex,
    });
    set(isCreatePageDialogOpenAtom, true);
  },
);

// Close create page dialog
export const closeCreatePageDialogAtom = atom(null, (get, set) => {
  set(isCreatePageDialogOpenAtom, false);
  set(creatingPageAtom, null);
  set(pageParentInfoAtom, null);
});

// ============================================================================
// UI State Management Actions
// ============================================================================

// Select a section/page
export const selectSectionAtom = atom(
  null,
  (get, set, sectionId: string | null) => {
    set(selectedSectionIdAtom, sectionId);
  },
);

// Start creating section
export const startCreatingSectionAtom = atom(null, (get, set) => {
  set(creatingSectionAtom, true);
});

// Stop creating section
export const stopCreatingSectionAtom = atom(null, (get, set) => {
  set(creatingSectionAtom, false);
});

// ============================================================================
// Bulk Operations
// ============================================================================

// Update entire wiki structure
export const updateWikiStructureAtom = atom(
  null,
  async (
    get,
    set,
    newStructure: WikiSection[],
    refetchCallback?: () => void,
  ) => {
    try {
      const res = await updateWikiStructure({ wikiStructure: newStructure });
      if (res) {
        set(wikiSectionsAtom, JSON.parse(res?.wiki || "[]"));
        toast.success("Wiki structure updated successfully");
        refetchCallback?.();
      }
    } catch (error) {
      toast.error("Failed to update wiki structure");
      throw error;
    }
  },
);
