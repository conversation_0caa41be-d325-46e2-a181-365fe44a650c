import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import type { WikiPage, WikiSection } from "@/types/wiki";

// ============================================================================
// Base Data Atoms
// ============================================================================

// Wiki sections data atom - stores all wiki sections and pages
export const wikiSectionsAtom = atomWithReset<WikiSection[]>([]);

// ============================================================================
// UI State Atoms
// ============================================================================

// Selected section/page ID atom
export const selectedSectionIdAtom = atomWithReset<string | null>(null);

// Current editing state atoms
export const editingSectionAtom = atomWithReset<number | null>(null);
export const editingPageAtom = atomWithReset<string | null>(null);

// Edit form state atoms
export const editSectionNameAtom = atomWithReset<string>("");
export const editPageNameAtom = atomWithReset<string>("");

// ============================================================================
// Dialog State Atoms
// ============================================================================

// Section creation dialog state
export const creatingSectionAtom = atomWithReset<boolean>(false);

// Page creation dialog state
export const isCreatePageDialogOpenAtom = atomWithReset<boolean>(false);
export const creatingPageAtom = atomWithReset<string | null>(null);

// Page creation parent info
export const pageParentInfoAtom = atomWithReset<{
  name: string;
  type: "section" | "page";
  index?: number;
} | null>(null);

// ============================================================================
// Computed/Derived Atoms
// ============================================================================

// Find page by ID helper atom
export const findPageByIdAtom = atom((get) => {
  const sections = get(wikiSectionsAtom);
  return (pageId: string): { page: WikiPage; sectionIndex: number } | null => {
    for (let sectionIndex = 0; sectionIndex < sections.length; sectionIndex++) {
      const findInPages = (
        pages: WikiPage[],
      ): { page: WikiPage; sectionIndex: number } | null => {
        for (const page of pages) {
          if (page.wiki_section_id === pageId) {
            return { page, sectionIndex };
          }
          if (page.pages) {
            const found = findInPages(page.pages);
            if (found) return found;
          }
        }
        return null;
      };

      const found = findInPages(sections[sectionIndex].pages);
      if (found) return found;
    }
    return null;
  };
});

// Get current editing page atom
export const currentEditingPageAtom = atom((get) => {
  const editingPageId = get(editingPageAtom);
  if (!editingPageId) return null;

  const findPage = get(findPageByIdAtom);
  return findPage(editingPageId)?.page || null;
});

// Get current editing section atom
export const currentEditingSectionAtom = atom((get) => {
  const editingSectionIndex = get(editingSectionAtom);
  const sections = get(wikiSectionsAtom);
  if (editingSectionIndex === null || editingSectionIndex >= sections.length)
    return null;
  return sections[editingSectionIndex];
});

// ============================================================================
// Validation Atoms
// ============================================================================

// Check if section name is valid atom
export const isValidSectionNameAtom = atom((get) => {
  const name = get(editSectionNameAtom);
  return name.trim().length > 0;
});

// Check if page name is valid atom
export const isValidPageNameAtom = atom((get) => {
  const name = get(editPageNameAtom);
  return name.trim().length > 0;
});

// ============================================================================
// Loading and Error State Atoms
// ============================================================================

// Loading states
export const isLoadingSectionsAtom = atomWithReset<boolean>(false);
export const isUpdatingSectionAtom = atomWithReset<boolean>(false);
export const isUpdatingPageAtom = atomWithReset<boolean>(false);
export const isDeletingSectionAtom = atomWithReset<boolean>(false);
export const isDeletingPageAtom = atomWithReset<boolean>(false);

// Error states
export const sectionsErrorAtom = atomWithReset<string | null>(null);
export const updateSectionErrorAtom = atomWithReset<string | null>(null);
export const updatePageErrorAtom = atomWithReset<string | null>(null);
export const deleteSectionErrorAtom = atomWithReset<string | null>(null);
export const deletePageErrorAtom = atomWithReset<string | null>(null);
