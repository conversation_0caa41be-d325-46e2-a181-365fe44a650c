import { parse, serialize } from "cookie-es";
import { atom, getDefaultStore } from "jotai";
import { atomWithStorage, RESET } from "jotai/utils";
import type { AuthHint } from "@/server/authHint";
import type { AuthOK } from "@/types/auth";

export const authStore = getDefaultStore();
export const atomAuth = atomWithStorage<AuthOK | null>("auth", null);
export const atomAuthHint = atom<AuthHint | undefined>(undefined);

export const atomAuthRefDismissed = atomWithStorage<boolean>(
  "auth:ref:dismissed",
  false,
);

const COOKIE_AUTH_HINT = "auth:hint";

export function syncAuthState() {
  if (typeof window === "undefined") return;

  const auth = authStore.get(atomAuth);
  const cookies = parse(document.cookie);
  const hintValue = auth ? `${auth.claims.sub}:${auth.claims.exp}` : "";

  if (!auth) {
    if (!cookies[COOKIE_AUTH_HINT]) {
      return;
    }

    document.cookie = serialize(COOKIE_AUTH_HINT, "", {
      path: "/",
      sameSite: "lax",
      maxAge: 0,
    });
    window.location.reload();
    return;
  }

  authStore.set(atomAuthHint, [auth.claims.sub, auth.claims.exp]);
  authStore.set(atomAuthRefDismissed, RESET);

  if (cookies[COOKIE_AUTH_HINT] !== hintValue) {
    document.cookie = serialize(COOKIE_AUTH_HINT, hintValue, {
      path: "/",
      sameSite: "lax",
      expires: new Date(auth.claims.exp * 1000),
    });
  }
}

export function logOut() {
  authStore.set(atomAuth, RESET);
}

atomAuth.onMount = syncAuthState;
authStore.sub(atomAuth, syncAuthState);
