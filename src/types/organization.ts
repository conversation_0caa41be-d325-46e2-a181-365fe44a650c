/**
 * Department represents a company department with its users and permissions
 */

export enum PERMISSION {
  ROLE_MANAGE = "role_manage",
  DEPARTMENT_MANAGE = "department_manage",
  USER_MANAGE = "user_manage",
  WIKI_MANAGE = "wiki_manage",
  BRANDING_MANAGE = "branding_manage",
}

export interface PermissionInfo {
  id: string;
  name: string;
  description: string;
}

export const PERMISSION_INFO: Record<PERMISSION, PermissionInfo> = {
  [PERMISSION.ROLE_MANAGE]: {
    id: PERMISSION.ROLE_MANAGE,
    name: "Quản lý vai trò",
    description: "<PERSON><PERSON> thể tạo, sửa, xóa vai trò và phân quyền",
  },
  [PERMISSION.DEPARTMENT_MANAGE]: {
    id: PERMISSION.DEPARTMENT_MANAGE,
    name: "Quản lý phòng ban",
    description: "<PERSON><PERSON> thể tạo, sửa, xóa phòng ban và phân quyền",
  },
  [PERMISSION.USER_MANAGE]: {
    id: PERMISSION.USER_MANAGE,
    name: "<PERSON>u<PERSON>n lý người dùng",
    description: "<PERSON><PERSON> thể thêm, sửa, xóa người dùng",
  },
  [PERMISSION.WIKI_MANAGE]: {
    id: PERMISSION.WIKI_MANAGE,
    name: "Quản lý Wiki",
    description: "Có thể tạo, sửa, xóa nội dung Wiki",
  },
  [PERMISSION.BRANDING_MANAGE]: {
    id: PERMISSION.BRANDING_MANAGE,
    name: "Quản lý thương hiệu",
    description: "Có thể thay đổi logo, màu sắc thương hiệu của tổ chức",
  },
};

export enum COURSE_PERMISSION {
  READ = "read",
  WRITE = "write",
  REPORT = "report",
}

export interface Department {
  id: string;
  name: string;
  description: string;
  // custom fields
  users: number;
}

export interface GroupPermission {
  id: string;
  name: string;
  description: string;
  permissions: PERMISSION[];
}

/**
 * Brand color configuration for the organization
 */
export interface BrandColors {
  primary: string;
  secondary: string;
  accent: string;
}
