import { UUID } from "@/types/app";
import { User } from "./auth";
import { Course } from "./courses";

export interface RatingValues {
  general: number;
  course_quality: number;
  learning_engagement: number;
  ai_mentor: number;
}

export interface UserVoting {
  ai_mentor: number;
  course_quality: number;
  general: number;
  learning_engagement: number;
  comment: string;
  course_id: UUID;
  user_id: UUID;
  course?: Course;
  created_at: string;
  id: UUID;
  user?: User;
}
