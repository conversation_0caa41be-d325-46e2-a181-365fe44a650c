// Analytics data types for dashboard analytics page

export interface MetricData {
  date: string;
  value: number;
}

export interface LearningTrendData {
  date: string;
  completed: number;
  enrolled: number;
  active: number;
  assigned: number;
}

export interface CourseData {
  id: string;
  name: string;
  students: number;
  tags: string;
  numberOfLessons: number;
}

export interface TopPerformer {
  name: string;
  department: string;
  xp: number;
  courses: number;
  avatar: string;
  id: number;
}

export interface TopPerformerChart {
  name: string;
  score: number;
  courses: number;
  xp: number;
}

export interface LearningProgressData {
  month: string;
  completed: number;
  enrolled: number;
  score: number;
}

export interface SkillDistribution {
  name: string;
  value: number;
  color: string;
}

export interface DepartmentPerformance {
  department: string;
  completion: number;
  avgScore: number;
  users: number;
}

export interface DepartmentStats {
  name: string;
  employees: number;
  completion: number;
  avgScore: number;
  avgXP: number;
  avgTime: number;
}

export interface SkillRadarData {
  skill: string;
  current: number;
  target: number;
}

export interface CourseDistribution {
  department: string;
  courses: number;
  percentage: number;
  color: string;
}

export interface DailyActivity {
  day: number;
  users: number;
}

export interface DepartmentActivity {
  department: string;
  users: number;
}

export interface RecentActivity {
  id: number;
  type: "completion" | "enrollment" | "certificate";
  userName: string;
  userId: string;
  courseName: string;
  courseId: string;
  time: string;
  action: string;
}

export interface SkillAnalysis {
  name: string;
  department: string;
  leadership: number;
  communication: number;
  technical: number;
  analysis: number;
  creativity: number;
  teamwork: number;
}

export interface SkillCategory {
  key: string;
  name: string;
  color: string;
}

// Filter types
export type TimeFilter = "7days" | "30days" | "90days";
export type PeriodFilter = "6months" | "30days" | "7days";
export type StatsTab = "total" | "active" | "new";

// Chart configuration
export interface ChartConfig {
  completed: {
    label: string;
    color: string;
  };
  enrolled: {
    label: string;
    color: string;
  };
  score: {
    label: string;
    color: string;
  };
}
