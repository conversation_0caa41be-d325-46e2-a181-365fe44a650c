export type WikiPage = {
  title: string;
  description?: string;
  wiki_section_id: string;
  pages?: WikiPage[];
};

export type WikiSection = {
  title: string;
  pages: WikiPage[];
};

export type OrganizationInformation = {
  name: string;
  logo_url?: string;
  description?: string;
  updated_at?: string;
  wiki?: string;
};

export type WikiSectionDetail = {
  id: string;
  content: string;
  creator: string;
  created_at: string;
  updated_at?: string;
};
