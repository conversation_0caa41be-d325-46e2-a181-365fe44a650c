import type { PropsWithChildren } from "react";

export type PropsWithClassName = {
  className?: string;
};

export type PropsWithClassNameAndChildren = PropsWithChildren &
  PropsWithClassName;

export enum CategoryEnum {
  AI = "ai",
  ENGLISH_LEARNING = "english-learning",
  CYBER_SECURITY = "cyber-security",
  CANVA = "canva",
  SECURITY = "security",
  ALL = "all",
  BLOCKCHAIN = "blockchain",
}

export interface Category {
  id: UUID | "all";
  name: string;
  slug: CategoryEnum;
  description: string;
  ordinal_index: number;
  tags?: string[];
}

export interface PaginationFilter {
  offset?: number;
  limit?: number;
}

export type UUID = `${string}-${string}-${string}-${string}-${string}`;

export enum BadgeEnum {
  FIRSTMILESTONE = "first-milestone",
  ALPHACREW = "alpha-crew",
  THEINVITER5 = "the-inviter-5",
}

export type BadgesInfo = {
  id: BadgeEnum;
  congratulation: string;
  title: string;
  description: string;
  img: string;
};
