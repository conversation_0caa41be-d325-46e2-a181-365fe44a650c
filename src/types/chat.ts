import { UUID } from "./app";

export enum ChatbotAction {
  EXPLAIN = "explain",
  EXAMPLES = "examples",
  SUMMARY = "summary",
  TRANSLATE = "translate",
  CHAT = "text",
}

export type ChatMessage = {
  id: UUID;
  role: "user" | "assistant";
  timestamp: string;
  content: string;
  quote?: string;
  type: "text" | "markdown";

  // custom fields
  isMock?: boolean;
  streamChunks?: string[];
  streamStatus?: ChatStreamingStatus | null;
};

export interface Conversation {
  id: UUID;
  user_id: UUID;
  lesson_id: UUID;
  section_id: UUID;
  course_id: UUID;
  created_at: string;
  updated_at: string;
  recent_messages?: ChatMessage[];
}

export interface ChatFAQ {
  id: UUID;
  section_id: UUID;
  question: string;
  created_at: string;
  updated_at: string;
  language: string;
}

export enum FeedbackType {
  NEGATIVE = -1,
  POSITIVE = 1,
  NONE = 0,
}

export interface MessageFeedback {
  message_id: string;
  lesson_id: string;
  feedback: FeedbackType;
  comment: string;
}

export interface ChatProfile {
  id: string;
  name: string;
  preferences: ChatPreferences;
  updated_at: string;
}

export const ChatLanguage = {
  en: "English",
  vi: "Tiếng Việt",
} as const;

export const ChatTone = {
  casual: "Casual",
  neutral: "Neutral",
  formal: "Formal",
} as const;

export const ChatVerbosity = {
  concise: "Concise",
  balanced: "Balanced",
  detailed: "Detailed",
} as const;

export interface ChatPreferences {
  display_name: string | null;
  preferred_name: string | null;
  language: keyof typeof ChatLanguage;
  tone: keyof typeof ChatTone;
  verbosity: keyof typeof ChatVerbosity;
}

export enum ChatStreamingStatus {
  PROCESSING = "processing",
  STREAM_CHUNK = "stream_chunk",
  PAUSED = "paused",
  STREAM_ERROR = "stream_error",
}

export type ChatResponse =
  | ChatProcessingResponse
  | ChatStreamResponse
  | ChatDoneResponse
  | ChatStreamErrorResponse;

export type ChatProcessingResponse = {
  type: ChatStreamingStatus.PROCESSING;
  data: { message: string; conversation_id: UUID; input_id: UUID };
};

export type ChatStreamResponse = {
  type: ChatStreamingStatus.STREAM_CHUNK;
  data: { chunk: string; conversation_id: UUID; output_id: UUID };
};

export type ChatStreamErrorResponse = {
  type: ChatStreamingStatus.STREAM_ERROR;
  data: {
    error: string;
    conversation_id: UUID;
    output_id: UUID;
    content: string;
  };
};

export type ChatDoneResponse = {
  data: {
    output: ChatMessage;
    input: ChatMessage;
    preferences?: Partial<ChatPreferences>;
  };
};
