export interface Leaderboard {
  user_id: string;
  name: string;
  avatar_url?: string;
  points: number;
  rank: number;
}

export enum LeaderboardType {
  LEARNER = "learner",
  EXPLORER = "explorer",
  THINKER = "thinker",
  STRATEGIST = "strategist",
  MASTERMIND = "mastermind",
  AILUMINATI = "alluminati",
}

export interface MyLeaderboard {
  user_id: string;
  points: number;
  rank: number;
  type: LeaderboardType;
}
