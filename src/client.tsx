import { StrictMode, startTransition } from "react";
import { hydrateRoot } from "react-dom/client";
import { loginWithGoogle } from "@/services/auth";
import { StartClientPromise } from "./components/StartClientPromise";
import { createRouter } from "./router";
import { atomAuthRefDismissed, authStore } from "./store/auth";

const router = createRouter(window.location.pathname);

import {
  getLocale,
  overwriteGetLocale,
  strategy,
} from "./paraglide/runtime.js";

if (strategy.includes("cookie")) {
  const inMemoryLocale = getLocale();
  overwriteGetLocale(() => inMemoryLocale);
}
startTransition(() => {
  hydrateRoot(
    document,
    <StrictMode>
      <StartClientPromise
        router={router}
        onResolved={() => {
          const clientID =
            import.meta.env.VITE_GOOGLE_CLIENT_ID ||
            "************-j721kjuaqkpecgev8ql0hl4aurpn02c5.apps.googleusercontent.com";
          console.debug("GSI client", clientID);
          if (!clientID || !window.google?.accounts) {
            return;
          }

          window.google.accounts.id.initialize({
            client_id: clientID,
            callback: (resp) => {
              loginWithGoogle({
                credential: resp.credential,
              });
            },
          });
        }}
      />
    </StrictMode>,
  );
});
