import { useMutation, useQuery } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
  defaultContent,
  loadingContent,
  updateWikiSection,
  wikiSectionDetailQueryOptions,
} from "@/services/wiki";
import { selectedSectionIdAtom } from "@/store/wiki";
import BeautyEditor from "../lesson-editor/tiptap-editor";

const WikiEditor = () => {
  const [selectedSectionId] = useAtom(selectedSectionIdAtom);
  const [content, setContent] = useState(defaultContent);
  const { data: sectionDetail, isLoading: isSectionDetailLoading } = useQuery(
    wikiSectionDetailQueryOptions(selectedSectionId || ""),
  );

  const { mutate: updateSectionContent, isPending } = useMutation({
    mutationFn: updateWikiSection,
    onSuccess: () => {
      toast.success("Saved");
    },
    onError: () => {
      toast.error("Failed to save");
    },
  });

  useEffect(() => {
    if (sectionDetail) {
      setContent(sectionDetail.content);
    }
  }, [sectionDetail]);

  useEffect(() => {
    if (isSectionDetailLoading) {
      toast.loading("Getting content... Please wait...");
      return;
    }
    toast.dismiss();
  }, [isSectionDetailLoading]);

  const updateSectionContentCallback = useCallback(() => {
    updateSectionContent({
      sectionId: selectedSectionId || "",
      sectionContent: JSON.stringify(content),
    });
  }, [selectedSectionId, content, updateSectionContent]);

  return (
    <div className="h-full w-full">
      <div className="sticky top-0 mb-4 flex w-full justify-end">
        <Button
          onClick={() => {
            updateSectionContentCallback();
          }}
          disabled={isPending || isSectionDetailLoading}
        >
          {isPending ? "Saving..." : "Save"}
        </Button>
      </div>
      <BeautyEditor
        initialContent={sectionDetail?.content}
        content={content}
        setContent={(e) => {
          setContent(e);
        }}
      />
    </div>
  );
};

export default WikiEditor;
