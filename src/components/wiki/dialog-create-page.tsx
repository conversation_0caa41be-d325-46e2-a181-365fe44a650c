import { useQuery } from "@tanstack/react-query";
import { useAtom, useSet<PERSON>tom } from "jotai";
import { useCallback, useMemo, useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  createWikiSection,
  defaultContent,
  organizationInformationQueryOptions,
  updateWikiStructure,
} from "@/services/wiki";
import {
  closeCreatePageDialogAtom,
  creatingPageAtom,
  isCreatePageDialogOpenAtom,
  pageParentInfoAtom,
  wikiSectionsAtom,
} from "@/store/wiki";
import { WikiPage } from "@/types/wiki";

const CreatePageDialog = () => {
  const { refetch: refetchOrganizationInformation } = useQuery(
    organizationInformationQueryOptions(),
  );
  const [newPageName, setNewPageName] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  const [wikiSectionsState, setWikiSectionsState] = useAtom(wikiSectionsAtom);
  const [isCreatePageDialogOpen] = useAtom(isCreatePageDialogOpenAtom);
  const [pageParentInfo] = useAtom(pageParentInfoAtom);
  const [creatingPage] = useAtom(creatingPageAtom);
  const closeCreatePageDialog = useSetAtom(closeCreatePageDialogAtom);

  const parentPageId = useMemo(() => {
    return pageParentInfo?.type === "page"
      ? creatingPage || undefined
      : undefined;
  }, [creatingPage, pageParentInfo?.type]);

  // Function to create a new page
  const handleCreatePage = useCallback(async () => {
    if (!newPageName.trim()) return;

    setIsCreating(true);
    try {
      const updatedSections = [...wikiSectionsState];
      const newWikiSection = await createWikiSection({
        sectionContent: defaultContent,
      });

      if (!newWikiSection?.id) {
        throw new Error("Failed to create page");
      }

      const newPage: WikiPage = {
        title: newPageName.trim(),
        wiki_section_id: newWikiSection.id,
        pages: [],
      };

      if (parentPageId) {
        // Find and add nested page
        const findAndAddPage = (pages: WikiPage[]): boolean => {
          for (let i = 0; i < pages.length; i++) {
            if (pages[i].wiki_section_id === parentPageId) {
              if (!pages[i].pages) pages[i].pages = [];
              pages[i].pages!.push(newPage);
              return true;
            }
            if (pages[i].pages && findAndAddPage(pages[i].pages!)) {
              return true;
            }
          }
          return false;
        };
        findAndAddPage(updatedSections[pageParentInfo?.index || 0].pages);
      } else {
        // Add to section root
        updatedSections[pageParentInfo?.index || 0].pages.push(newPage);
      }

      const res = await updateWikiStructure({ wikiStructure: updatedSections });
      if (res) {
        setWikiSectionsState(JSON.parse(res?.wiki || "[]"));
        refetchOrganizationInformation();
        toast.success("Page created successfully");
        refetchOrganizationInformation();
        handleClose();
      }
    } catch (error) {
      toast.error("Failed to create page");
    } finally {
      setIsCreating(false);
    }
  }, [
    newPageName,
    wikiSectionsState,
    pageParentInfo,
    setWikiSectionsState,
    refetchOrganizationInformation,
    parentPageId,
  ]);

  const handleClose = useCallback(() => {
    setNewPageName("");
    closeCreatePageDialog();
  }, [closeCreatePageDialog]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleCreatePage();
    } else if (e.key === "Escape") {
      handleClose();
    }
  };

  return (
    <Dialog open={isCreatePageDialogOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            Create New Page{" "}
            {pageParentInfo &&
              `in ${pageParentInfo.type === "section" ? "Section" : "Page"}: "${pageParentInfo.name}"`}
          </DialogTitle>
          <DialogDescription>
            Enter the name for your new{" "}
            {pageParentInfo?.type === "section" ? "page" : "subpage"}.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Input
            placeholder={`Enter ${pageParentInfo?.type === "section" ? "page" : "subpage"} name...`}
            value={newPageName}
            onChange={(e) => setNewPageName(e.target.value)}
            onKeyDown={handleKeyDown}
            autoFocus
            disabled={isCreating}
          />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isCreating}>
            Cancel
          </Button>
          <Button
            onClick={handleCreatePage}
            disabled={!newPageName.trim() || isCreating}
          >
            {isCreating
              ? "Creating..."
              : `Create ${pageParentInfo?.type === "section" ? "Page" : "Subpage"}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreatePageDialog;
