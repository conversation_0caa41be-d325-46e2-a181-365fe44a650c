import { useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { updateWikiStructure } from "@/services/wiki";
import { WikiPage, WikiSection } from "@/types/wiki";

export interface PageParentInfo {
  name: string;
  type: "section" | "page";
  index?: number;
}

export interface UseWikiSidebarReturn {
  // State
  selectedSectionId: string | null;
  creatingSection: boolean;
  isCreatePageDialogOpen: boolean;
  creatingPage: string | null;
  pageParentInfo: PageParentInfo | null;
  editingSection: number | null;
  editingPage: string | null;
  editSectionName: string;
  editPageName: string;
  wikiSectionsState: WikiSection[];

  // State setters
  setSelectedSectionId: (id: string | null) => void;
  setCreatingSection: (creating: boolean) => void;
  setIsCreatePageDialogOpen: (open: boolean) => void;
  setWikiSectionsState: (sections: WikiSection[]) => void;
  setEditSectionName: (name: string) => void;
  setEditPageName: (name: string) => void;

  // Actions
  handleDeletePage: (sectionIndex: number, pageId: string) => Promise<void>;
  handleDeleteSection: (sectionIndex: number) => Promise<void>;
  handleStartEditSection: (sectionIndex: number) => void;
  handleSaveEditSection: () => Promise<void>;
  handleCancelEditSection: () => void;
  handleStartEditPage: (pageId: string, currentTitle: string) => void;
  handleOpenCreatePageDialog: (
    parentId: string,
    parentName: string,
    parentType: "section" | "page",
    sectionIndex?: number,
  ) => void;
  handleSaveEditPage: () => Promise<void>;
  handleCancelEditPage: () => void;
  updateWikiSectionsFromData: (wikiData: string) => void;
}

export const useWikiSidebar = (
  wikiData?: string,
  refetchOrganizationInformation?: () => void,
): UseWikiSidebarReturn => {
  const [selectedSectionId, setSelectedSectionId] = useState<string | null>(
    null,
  );
  const [creatingSection, setCreatingSection] = useState(false);
  const [isCreatePageDialogOpen, setIsCreatePageDialogOpen] = useState(false);
  const [creatingPage, setCreatingPage] = useState<string | null>(null);
  const [pageParentInfo, setPageParentInfo] = useState<PageParentInfo | null>(
    null,
  );
  const [editingSection, setEditingSection] = useState<number | null>(null);
  const [editingPage, setEditingPage] = useState<string | null>(null);
  const [editSectionName, setEditSectionName] = useState("");
  const [editPageName, setEditPageName] = useState("");
  const [wikiSectionsState, setWikiSectionsState] = useState<WikiSection[]>([]);

  // Update wiki sections from data
  const updateWikiSectionsFromData = useCallback((data: string) => {
    try {
      const parsedSections = JSON.parse(data || "[]");
      setWikiSectionsState(parsedSections);
    } catch (error) {
      console.error("Failed to parse wiki data:", error);
      setWikiSectionsState([]);
    }
  }, []);

  useEffect(() => {
    if (wikiData) {
      updateWikiSectionsFromData(wikiData);
    }
  }, [wikiData, updateWikiSectionsFromData]);

  // Function to delete a page
  const handleDeletePage = useCallback(
    async (sectionIndex: number, pageId: string) => {
      try {
        const updatedSections = [...wikiSectionsState];

        const findAndDeletePage = (pages: WikiPage[]): boolean => {
          for (let i = pages.length - 1; i >= 0; i--) {
            if (pages[i].wiki_section_id === pageId) {
              pages.splice(i, 1);
              return true;
            }
            if (pages[i].pages && findAndDeletePage(pages[i].pages!)) {
              return true;
            }
          }
          return false;
        };

        findAndDeletePage(updatedSections[sectionIndex].pages);
        const res = await updateWikiStructure({
          wikiStructure: updatedSections,
        });
        if (res) {
          updateWikiSectionsFromData(res?.wiki || "[]");
          toast.success("Page deleted successfully");
          refetchOrganizationInformation?.();
        }
      } catch (error) {
        setWikiSectionsState(wikiSectionsState);
        toast.error("Failed to delete page");
      }
    },
    [
      wikiSectionsState,
      updateWikiSectionsFromData,
      refetchOrganizationInformation,
    ],
  );

  // Function to delete a section
  const handleDeleteSection = useCallback(
    async (sectionIndex: number) => {
      try {
        const updatedSections = [...wikiSectionsState];
        updatedSections.splice(sectionIndex, 1);
        const res = await updateWikiStructure({
          wikiStructure: updatedSections,
        });
        if (res) {
          updateWikiSectionsFromData(res?.wiki || "[]");
          toast.success("Section deleted successfully");
          refetchOrganizationInformation?.();
        }
      } catch (error) {
        setWikiSectionsState(wikiSectionsState);
        toast.error("Failed to delete section");
      }
    },
    [
      wikiSectionsState,
      updateWikiSectionsFromData,
      refetchOrganizationInformation,
    ],
  );

  // Function to start editing a section
  const handleStartEditSection = useCallback(
    (sectionIndex: number) => {
      setEditingSection(sectionIndex);
      setEditSectionName(wikiSectionsState[sectionIndex].title);
    },
    [wikiSectionsState],
  );

  // Function to save edited section
  const handleSaveEditSection = useCallback(async () => {
    if (editingSection === null || !editSectionName.trim()) return;
    try {
      const updatedSections = [...wikiSectionsState];
      updatedSections[editingSection].title = editSectionName.trim();
      const res = await updateWikiStructure({ wikiStructure: updatedSections });
      if (res) {
        updateWikiSectionsFromData(res?.wiki || "[]");
        toast.success("Section updated successfully");
        refetchOrganizationInformation?.();
      }
    } catch (error) {
      setWikiSectionsState(wikiSectionsState);
      toast.error("Failed to update section");
    } finally {
      setEditingSection(null);
      setEditSectionName("");
    }
  }, [
    editingSection,
    editSectionName,
    wikiSectionsState,
    updateWikiSectionsFromData,
    refetchOrganizationInformation,
  ]);

  // Function to cancel editing section
  const handleCancelEditSection = useCallback(() => {
    setEditingSection(null);
    setEditSectionName("");
  }, []);

  // Function to start editing a page
  const handleStartEditPage = useCallback(
    (pageId: string, currentTitle: string) => {
      setEditingPage(pageId);
      setEditPageName(currentTitle);
    },
    [],
  );

  // Function to open create page dialog
  const handleOpenCreatePageDialog = useCallback(
    (
      parentId: string,
      parentName: string,
      parentType: "section" | "page",
      sectionIndex?: number,
    ) => {
      setCreatingPage(parentId);
      setPageParentInfo({
        name: parentName,
        type: parentType,
        index: sectionIndex,
      });
      setIsCreatePageDialogOpen(true);
    },
    [],
  );

  // Function to save edited page
  const handleSaveEditPage = useCallback(async () => {
    if (!editingPage || !editPageName.trim()) return;

    try {
      const updatedSections = [...wikiSectionsState];
      const findAndUpdatePage = (pages: WikiPage[]): boolean => {
        for (let i = 0; i < pages.length; i++) {
          if (pages[i].wiki_section_id === editingPage) {
            pages[i].title = editPageName.trim();
            return true;
          }
          if (pages[i].pages && findAndUpdatePage(pages[i].pages!)) {
            return true;
          }
        }
        return false;
      };

      updatedSections.forEach((section) => {
        findAndUpdatePage(section.pages);
      });

      const res = await updateWikiStructure({ wikiStructure: updatedSections });
      if (res) {
        updateWikiSectionsFromData(res?.wiki || "[]");
        toast.success("Page updated successfully");
        refetchOrganizationInformation?.();
      }
    } catch (error) {
      setWikiSectionsState(wikiSectionsState);
      toast.error("Failed to update page");
    } finally {
      setEditingPage(null);
      setEditPageName("");
    }
  }, [
    editingPage,
    editPageName,
    wikiSectionsState,
    updateWikiSectionsFromData,
    refetchOrganizationInformation,
  ]);

  // Function to cancel editing page
  const handleCancelEditPage = useCallback(() => {
    setEditingPage(null);
    setEditPageName("");
  }, []);

  return {
    // State
    selectedSectionId,
    creatingSection,
    isCreatePageDialogOpen,
    creatingPage,
    pageParentInfo,
    editingSection,
    editingPage,
    editSectionName,
    editPageName,
    wikiSectionsState,

    // State setters
    setSelectedSectionId,
    setCreatingSection,
    setIsCreatePageDialogOpen,
    setWikiSectionsState,
    setEditSectionName,
    setEditPageName,

    // Actions
    handleDeletePage,
    handleDeleteSection,
    handleStartEditSection,
    handleSaveEditSection,
    handleCancelEditSection,
    handleStartEditPage,
    handleOpenCreatePageDialog,
    handleSaveEditPage,
    handleCancelEditPage,
    updateWikiSectionsFromData,
  };
};
