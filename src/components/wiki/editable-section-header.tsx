import { use<PERSON><PERSON>, useSet<PERSON><PERSON> } from "jotai";
import { Edit, FilePlus, MoreHorizontal, Trash2 } from "lucide-react";
import { AccordionTrigger } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  cancelEditSectionAtom,
  deleteSectionAtom,
  editingSectionAtom,
  editSectionNameAtom,
  openCreatePageDialogAtom,
  saveEditSectionAtom,
  startEditSectionAtom,
} from "@/store/wiki";
import { WikiActions } from "./wiki-actions";

interface EditableSectionHeaderProps {
  sectionIndex: number;
  sectionTitle: string;
}

export const EditableSectionHeader = ({
  sectionIndex,
  sectionTitle,
}: EditableSectionHeaderProps) => {
  // Use store atoms directly
  const [editingSection] = useAtom(editingSection<PERSON>tom);
  const [editSectionName, setEditSectionName] = useAtom(editSectionNameAtom);
  const startEditSection = useSetAtom(startEditSectionAtom);
  const saveEditSection = useSetAtom(saveEditSectionAtom);
  const cancelEditSection = useSetAtom(cancelEditSectionAtom);
  const openCreatePageDialog = useSetAtom(openCreatePageDialogAtom);
  const deleteSection = useSetAtom(deleteSectionAtom);

  const isEditing = editingSection === sectionIndex;
  return (
    <div className="flex items-center justify-between">
      {isEditing ? (
        <div className="mb-3 flex-1 py-2">
          <Input
            value={editSectionName}
            onChange={(e) => setEditSectionName(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                saveEditSection();
              } else if (e.key === "Escape") {
                cancelEditSection();
              }
            }}
            className="mb-2 font-semibold text-[#3D3D3D] text-[14px] uppercase leading-7.5 tracking-wide"
            autoFocus
          />
        </div>
      ) : (
        <AccordionTrigger className="mb-3 flex-1 py-2 font-semibold text-[#3D3D3D] text-[14px] uppercase leading-7.5 tracking-wide hover:no-underline">
          {sectionTitle}
        </AccordionTrigger>
      )}
      {isEditing ? (
        <WikiActions onSave={saveEditSection} onCancel={cancelEditSection} />
      ) : (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="mr-2 h-6 w-6 p-0"
              onClick={(e) => e.stopPropagation()}
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                startEditSection(sectionIndex);
              }}
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit Section
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                openCreatePageDialog(
                  `section-${sectionIndex}`,
                  sectionTitle,
                  "section",
                  sectionIndex,
                );
              }}
            >
              <FilePlus className="mr-2 h-4 w-4" />
              Add Page
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                deleteSection(sectionIndex);
              }}
              className="text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Section
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
};
