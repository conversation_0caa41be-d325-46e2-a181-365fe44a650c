import { useQuery } from "@tanstack/react-query";
import { useAtom, useSetAtom } from "jotai";
import { Plus } from "lucide-react";
import { useCallback, useEffect } from "react";
import { Accordion } from "@/components/ui/accordion";
import { But<PERSON> } from "@/components/ui/button";
import { organizationInformationQueryOptions } from "@/services/wiki";
import {
  initializeWikiSectionsAtom,
  startCreatingSectionAtom,
  wikiSectionsAtom,
} from "@/store/wiki";
import { WikiPage } from "@/types/wiki";
import CreateSectionInput from "./create-section-input";
import CreatePageDialog from "./dialog-create-page";
import { EditablePageItem } from "./editable-page-item";
import SidebarLoading from "./sidebar-loading";
import { WikiSection } from "./wiki-section";

const SidebarMenu = () => {
  const {
    data: organizationInformation,
    isLoading: isLoadingOrganizationInformation,
  } = useQuery(organizationInformationQueryOptions());

  // State atoms
  const [wikiSectionsState] = useAtom(wikiSectionsAtom);

  // Action atoms
  const initializeWikiSections = useSetAtom(initializeWikiSectionsAtom);
  const startCreatingSection = useSetAtom(startCreatingSectionAtom);

  // Initialize wiki sections when data changes
  useEffect(() => {
    if (organizationInformation?.wiki) {
      initializeWikiSections(organizationInformation.wiki);
    }
  }, [organizationInformation?.wiki, initializeWikiSections]);

  const renderWikiPage = useCallback(
    (page: WikiPage, isNested = false, sectionIndex: number) => {
      return (
        <EditablePageItem
          key={page.wiki_section_id}
          page={page}
          isNested={isNested}
          sectionIndex={sectionIndex}
          renderNestedPage={renderWikiPage}
        />
      );
    },
    [],
  );

  return (
    <SidebarLoading isLoading={isLoadingOrganizationInformation}>
      <div className="mb-8 flex items-center justify-between">
        <h1 className="font-semibold text-gray-900 text-lg">
          {organizationInformation?.name
            ? `${organizationInformation.name} Wiki`
            : "Wiki"}
        </h1>
        <Button
          onClick={() => startCreatingSection()}
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Section
        </Button>
      </div>

      {/* Create section input */}
      <CreateSectionInput />

      <Accordion type="multiple" className="w-full">
        {wikiSectionsState?.map((section, sectionIndex) => (
          <WikiSection
            key={section.title}
            section={section}
            sectionIndex={sectionIndex}
            renderWikiPage={renderWikiPage}
          />
        ))}
      </Accordion>

      {/* Create Page Dialog */}
      <CreatePageDialog />
    </SidebarLoading>
  );
};

export default SidebarMenu;
