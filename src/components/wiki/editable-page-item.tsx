import { use<PERSON>tom, useSet<PERSON><PERSON> } from "jotai";
import { Edit, FilePlus, MoreHorizontal, Trash2 } from "lucide-react";
import React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  cancelEditPageAtom,
  deletePageAtom,
  editingPageAtom,
  editPageNameAtom,
  openCreatePageDialogAtom,
  saveEditPageAtom,
  selectedSectionIdAtom,
  selectSectionAtom,
  startEditPageAtom,
} from "@/store/wiki";
import { WikiPage } from "@/types/wiki";
import { WikiActions } from "./wiki-actions";

interface EditablePageItemProps {
  page: WikiPage;
  isNested?: boolean;
  sectionIndex: number;
  renderNestedPage?: (
    page: WikiPage,
    isNested: boolean,
    sectionIndex: number,
  ) => React.ReactElement;
}

export const EditablePageItem = ({
  page,
  isNested = false,
  sectionIndex,
  renderNestedPage,
}: EditablePageItemProps) => {
  // Use store atoms directly
  const [selectedSectionId] = useAtom(selectedSectionIdAtom);
  const [editingPage] = useAtom(editingPageAtom);
  const [editPageName, setEditPageName] = useAtom(editPageNameAtom);
  const selectPage = useSetAtom(selectSectionAtom);
  const saveEditPage = useSetAtom(saveEditPageAtom);
  const cancelEditPage = useSetAtom(cancelEditPageAtom);
  const startEditPage = useSetAtom(startEditPageAtom);
  const openCreatePageDialog = useSetAtom(openCreatePageDialogAtom);
  const deletePage = useSetAtom(deletePageAtom);

  const isActive = selectedSectionId === page.wiki_section_id;
  const hasChildren = page.pages && page.pages.length > 0;
  const isEditing = editingPage === page.wiki_section_id;

  // If page has children, render as collapsible accordion
  if (hasChildren) {
    return (
      <div key={page.wiki_section_id} className={`${isNested ? "ml-4" : ""}`}>
        <Accordion type="multiple" className="w-full">
          <AccordionItem value={page.wiki_section_id} className="border-none">
            {/* Page title as accordion trigger with dropdown menu */}
            <div className="flex items-center justify-between">
              {isEditing ? (
                <div className="flex-1 px-7 py-1">
                  <Input
                    value={editPageName}
                    onChange={(e) => setEditPageName(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        saveEditPage();
                      } else if (e.key === "Escape") {
                        cancelEditPage();
                      }
                    }}
                    className="h-8 text-base"
                    autoFocus
                  />
                </div>
              ) : (
                <AccordionTrigger
                  className={`flex-1 px-7 py-1 text-base leading-7.5 hover:no-underline ${
                    isActive
                      ? "border-green-500 border-l-4 bg-green-100 text-green-800"
                      : "text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  {page.title}
                </AccordionTrigger>
              )}
              {isEditing ? (
                <WikiActions onSave={saveEditPage} onCancel={cancelEditPage} />
              ) : (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="mr-2 h-6 w-6 p-0"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        startEditPage(page.wiki_section_id, page.title);
                      }}
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        openCreatePageDialog(
                          page.wiki_section_id,
                          page.title,
                          "page",
                          sectionIndex,
                        );
                      }}
                    >
                      <FilePlus className="mr-2 h-4 w-4" />
                      Add Subpage
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        deletePage(sectionIndex, page.wiki_section_id);
                      }}
                      className="text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>

            {/* Nested pages as accordion content */}
            <AccordionContent className="pt-2">
              <div className="ml-2 space-y-3">
                {page.pages!.map((nestedPage) =>
                  renderNestedPage
                    ? renderNestedPage(nestedPage, true, sectionIndex)
                    : null,
                )}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    );
  }

  // Regular page without children - render as simple link with dropdown
  return (
    <div key={page.wiki_section_id} className={`${isNested ? "ml-4" : ""}`}>
      <div className="group flex items-center justify-between">
        {isEditing ? (
          <div className="flex-1 px-7 py-1">
            <Input
              value={editPageName}
              onChange={(e) => setEditPageName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  saveEditPage();
                } else if (e.key === "Escape") {
                  cancelEditPage();
                }
              }}
              className="h-8 text-base"
              autoFocus
            />
          </div>
        ) : (
          <Button
            onClick={() => selectPage(page.wiki_section_id)}
            className={`h-fit w-full flex-1 text-wrap bg-transparent px-7 py-1 text-base text-black leading-7.5 shadow-none hover:bg-gray-200 ${
              isActive
                ? "border-green-500 border-l-4 bg-green-100"
                : "text-gray-700 hover:bg-gray-200"
            }`}
          >
            <p className="w-full text-wrap text-left">{page.title}</p>
          </Button>
        )}
        {isEditing ? (
          <WikiActions onSave={saveEditPage} onCancel={cancelEditPage} />
        ) : (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="mr-2 h-6 w-6 p-0 opacity-0 transition-opacity group-hover:opacity-100"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  startEditPage(page.wiki_section_id, page.title);
                }}
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  openCreatePageDialog(
                    page.wiki_section_id,
                    page.title,
                    "page",
                    sectionIndex,
                  );
                }}
              >
                <FilePlus className="mr-2 h-4 w-4" />
                Add Subpage
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  deletePage(sectionIndex, page.wiki_section_id);
                }}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  );
};
