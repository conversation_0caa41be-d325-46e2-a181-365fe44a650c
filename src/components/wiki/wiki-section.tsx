import { AccordionContent, AccordionItem } from "@/components/ui/accordion";
import { WikiPage, WikiSection as WikiSectionType } from "@/types/wiki";
import { EditableSectionHeader } from "./editable-section-header";

interface WikiSectionProps {
  section: WikiSectionType;
  sectionIndex: number;
  renderWikiPage: (
    page: WikiPage,
    isNested: boolean,
    sectionIndex: number,
  ) => React.ReactElement;
}

export const WikiSection = ({
  section,
  sectionIndex,
  renderWikiPage,
}: WikiSectionProps) => {
  return (
    <AccordionItem
      key={section.title}
      value={section.title}
      className="border-none"
    >
      <EditableSectionHeader
        sectionIndex={sectionIndex}
        sectionTitle={section.title}
      />

      <AccordionContent className="pb-6">
        <div className="space-y-3">
          {section.pages.map((page) =>
            renderWikiPage(page, false, sectionIndex),
          )}
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};
