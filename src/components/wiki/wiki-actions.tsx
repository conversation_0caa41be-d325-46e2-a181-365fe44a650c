import { Button } from "@/components/ui/button";

interface WikiActionsProps {
  onSave: () => void;
  onCancel: () => void;
  className?: string;
}

export const WikiActions = ({
  onSave,
  onCancel,
  className = "mr-2 flex gap-1",
}: WikiActionsProps) => {
  return (
    <div className={className}>
      <Button
        variant="ghost"
        size="sm"
        onClick={(e) => {
          e.stopPropagation();
          onSave();
        }}
        className="h-6 w-6 p-0"
      >
        ✓
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={(e) => {
          e.stopPropagation();
          onCancel();
        }}
        className="h-6 w-6 p-0"
      >
        ✕
      </Button>
    </div>
  );
};
