import React from "react";
import { Button } from "@/components/ui/button";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
}: PaginationProps) {
  if (totalPages <= 1) return null;

  const renderPageNumbers = () => {
    const pages: React.ReactNode[] = [];
    const showEllipsis = totalPages > 7;

    if (!showEllipsis) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(
          <Button
            key={i}
            variant={currentPage === i ? "default" : "outline"}
            size="sm"
            className={`h-10 w-10 p-0 ${
              currentPage === i
                ? "bg-blue-500 text-white hover:bg-blue-600"
                : "hover:bg-gray-50"
            }`}
            onClick={() => onPageChange(i)}
          >
            {i}
          </Button>,
        );
      }
    } else {
      pages.push(
        <Button
          key={1}
          variant={currentPage === 1 ? "default" : "outline"}
          size="sm"
          className={`h-10 w-10 p-0 ${
            currentPage === 1
              ? "bg-blue-500 text-white hover:bg-blue-600"
              : "hover:bg-gray-50"
          }`}
          onClick={() => onPageChange(1)}
        >
          1
        </Button>,
      );

      if (currentPage > 4) {
        pages.push(
          <span key="ellipsis1" className="px-2 text-muted-foreground">
            ...
          </span>,
        );
      }

      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);

      for (let i = start; i <= end; i++) {
        if (i !== 1 && i !== totalPages) {
          pages.push(
            <Button
              key={i}
              variant={currentPage === i ? "default" : "outline"}
              size="sm"
              className={`h-10 w-10 p-0 ${
                currentPage === i
                  ? "bg-blue-500 text-white hover:bg-blue-600"
                  : "hover:bg-gray-50"
              }`}
              onClick={() => onPageChange(i)}
            >
              {i}
            </Button>,
          );
        }
      }

      if (currentPage < totalPages - 3) {
        pages.push(
          <span key="ellipsis2" className="px-2 text-muted-foreground">
            ...
          </span>,
        );
      }

      if (totalPages > 1) {
        pages.push(
          <Button
            key={totalPages}
            variant={currentPage === totalPages ? "default" : "outline"}
            size="sm"
            className={`h-10 w-10 p-0 ${
              currentPage === totalPages
                ? "bg-blue-500 text-white hover:bg-blue-600"
                : "hover:bg-gray-50"
            }`}
            onClick={() => onPageChange(totalPages)}
          >
            {totalPages}
          </Button>,
        );
      }
    }

    return pages;
  };

  return (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        className="h-10 w-10 bg-transparent p-0"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        <span className="text-lg">‹</span>
      </Button>

      <div className="flex items-center gap-1">{renderPageNumbers()}</div>

      <Button
        variant="outline"
        size="sm"
        className="h-10 w-10 bg-transparent p-0"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        <span className="text-lg">›</span>
      </Button>
    </div>
  );
}
