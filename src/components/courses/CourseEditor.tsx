import { useForm } from "@tanstack/react-form";
import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { CheckIcon, Image, Upload, X } from "lucide-react";
import { useEffect, useState } from "react";
import convertToSlug from "slugify";
import { toast } from "sonner";
import MultiSelector from "@/components/MultiSelector";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  createCourse,
  UploadFile,
  updateCourse,
  useCategories,
} from "@/services/instructor";
import {
  Course,
  CourseFormData,
  CourseLanguage,
  CourseLevel,
} from "@/types/courses";
import { cn } from "@/utils/cn";

// Course Editor specific constants
const COURSE_LEVEL_LABELS = {
  [CourseLevel.BEGINNER]: "Beginner",
  [CourseLevel.INTERMEDIATE]: "Intermediate",
} as const;

const COURSE_LANGUAGE_LABELS = {
  [CourseLanguage.VI]: "Vietnamese",
  [CourseLanguage.EN]: "English",
} as const;

// Form schema for TanStack Form
interface CourseFormSchema {
  name: string;
  slug: string;
  level: string;
  language: string;
  image_url: string;
  categories: string[];
  overview: string;
  goals: string[];
  published_at?: string;
}

import { m } from "@/paraglide/messages.js";

const COURSE_EDITOR_CONFIG = {
  DEFAULTS: {
    ORDINAL_INDEX: 1,
    DURATION: 1000,
    EMPTY_GOAL: "",
    TEXTAREA_ROWS: 5,
  },
} as const;

export function CourseEditor(props: {
  selectedCourse?: CourseFormData;
  onSuccess?: () => void;
}) {
  const { selectedCourse, onSuccess } = props;
  const navigate = useNavigate();
  const { data: categories } = useQuery(useCategories());
  const [categoryOptions, setCategoryOptions] = useState<
    Array<{ value: string; label: string }>
  >([]);
  const [uploading, setUploading] = useState(false);
  const [autoRenderSlug, setAutoRenderSlug] = useState(true);

  // Parse existing description
  const parsedDescription = selectedCourse?.description
    ? JSON.parse(selectedCourse.description)
    : { overview: "", goals: [COURSE_EDITOR_CONFIG.DEFAULTS.EMPTY_GOAL] };

  // Initialize TanStack Form
  const form = useForm({
    defaultValues: {
      name: selectedCourse?.name || "",
      slug: selectedCourse?.slug || "",
      level: selectedCourse?.level || "",
      language: selectedCourse?.language || "",
      image_url:
        selectedCourse?.image_url ||
        "https://files.aicademy.org/MDE5NmQzYmYtNGUyZC03MTBhLWE2ZDItZmU1MTYyYTE4ZmU4OmltYWdlczpU_RYx_W79Gf39_Xn9_Uz9O_1d_UP9_f39_U0Sb_0",
      categories: selectedCourse?.categories?.map((c) => c.id) || [],
      overview: parsedDescription.overview || "",
      goals: parsedDescription.goals || [
        COURSE_EDITOR_CONFIG.DEFAULTS.EMPTY_GOAL,
      ],
      published_at: selectedCourse?.published_at,
    } as CourseFormSchema,
    onSubmit: async ({ value }) => {
      await saveCourse(value);
    },
    validators: {
      onSubmit: ({ value }) => {
        const errors: Record<string, string> = {};

        if (!value.name?.trim()) {
          errors.name =
            m["courseEditor.validation.nameRequired"]?.() ??
            "Course name is required";
        }

        if (!value.slug?.trim()) {
          errors.slug =
            m["courseEditor.validation.slugRequired"]?.() ??
            "URL slug is required";
        }

        if (!value.level?.trim()) {
          errors.level =
            m["courseEditor.validation.levelRequired"]?.() ??
            "Course level is required";
        }

        if (!value.language?.trim()) {
          errors.language =
            m["courseEditor.validation.languageRequired"]?.() ??
            "Language is required";
        }

        if (!value.overview?.trim()) {
          errors.overview =
            m["courseEditor.validation.overviewRequired"]?.() ??
            "Course overview is required";
        }

        const validGoals = value.goals?.filter((goal) => goal.trim() !== "");
        if (!validGoals || validGoals.length === 0) {
          errors.goals =
            m["courseEditor.validation.goalsRequired"]?.() ??
            "At least one meaningful course goal is required";
        }

        if (!value.categories || value.categories.length === 0) {
          errors.categories =
            m["courseEditor.validation.invalidCategories"]?.() ??
            "Please select at least one category";
        }

        return Object.keys(errors).length > 0 ? errors : undefined;
      },
    },
  });

  useEffect(() => {
    if (!categories || "code" in categories) return;
    const options =
      categories?.map((c) => {
        return { value: c.id, label: c.name };
      }) || [];

    if (selectedCourse) {
      setAutoRenderSlug(false);
    }

    setCategoryOptions(options);
  }, [categories, selectedCourse]);

  const convertToCourse = (formData: CourseFormData) => {
    // Create course object for API submission
    const course = {
      name: formData.name,
      title: formData.name, // Use name as title since form doesn't have separate title field
      slug: formData.slug,
      description: formData.description,
      level: formData.level,
      language: formData.language,
      duration: formData.duration || COURSE_EDITOR_CONFIG.DEFAULTS.DURATION,
      ordinal_index:
        formData.ordinal_index || COURSE_EDITOR_CONFIG.DEFAULTS.ORDINAL_INDEX,
    } as Record<string, unknown>;

    // Only add image_url if it's not empty
    if (formData.image_url?.trim()) {
      course.image_url = formData.image_url;
    }

    // Add ID only when updating
    if (formData.id) {
      course.id = formData.id;
    }

    // Add published_at only if it exists
    if (formData.published_at) {
      course.published_at = formData.published_at;
    }

    return course;
  };

  const saveCourse = async (formData: CourseFormSchema) => {
    // Create clean course data with only necessary fields
    const courseToSave: CourseFormData = {
      name: formData.name,
      slug: formData.slug,
      level: formData.level,
      language: formData.language,
      description: JSON.stringify({
        overview: formData.overview.trim(),
        goals: formData.goals.filter((goal) => goal.trim() !== ""),
      }),
      duration: COURSE_EDITOR_CONFIG.DEFAULTS.DURATION,
      published_at: formData.published_at,
    };

    // Only add image_url if it's not empty
    if (formData.image_url?.trim()) {
      courseToSave.image_url = formData.image_url;
    }

    // Add ID only when updating existing course
    if (selectedCourse?.id) {
      courseToSave.id = selectedCourse.id;
    }

    // Add title for completeness
    courseToSave.title = formData.name;

    const courseData = convertToCourse(courseToSave);

    try {
      if (!selectedCourse?.id) {
        await createCourse({
          course: courseData as unknown as Course,
          categories: formData.categories,
        });
      } else {
        await updateCourse({
          id: selectedCourse.id,
          course: courseData as unknown as Course,
          categories: formData.categories,
        });
      }
      navigate({
        to: "/dashboard/courses/$courseSlug",
        params: { courseSlug: formData.slug },
      });
      toast.success(
        m["courseEditor.validation.saveSuccess"]?.() ??
          "Course saved successfully!",
      );
      onSuccess?.();
    } catch (e) {
      toast.error(
        `${
          m["courseEditor.validation.saveFailed"]?.() ??
          "Failed to save course:"
        } ${e?.toString()}`,
      );
    }
  };

  const handleUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
    field: keyof CourseFormSchema,
  ) => {
    setUploading(true);
    const selectedFile = event.target.files?.[0];
    if (!selectedFile) {
      return toast.warning(
        m["courseEditor.validation.uploadFileWarning"]?.() ??
          "Please select a file before uploading.",
      );
    }
    try {
      const imgUrl = await UploadFile({ file: selectedFile });
      if (field === "image_url") {
        form.setFieldValue("image_url", imgUrl);
      }
    } catch (error) {
      toast.error("Failed to upload image");
    } finally {
      setUploading(false);
    }
  };

  return (
    <div
      className={cn("mx-auto max-w-7xl p-6", !selectedCourse && "space-y-8")}
    >
      {/* Header */}
      <div className="space-y-2">
        <h1 className="font-bold text-3xl tracking-tight">
          {!selectedCourse &&
            (m["courseEditor.labels.createTitle"]?.() ?? "Create a new course")}
        </h1>
        <p className="text-muted-foreground">
          {!selectedCourse &&
            (m["courseEditor.labels.createDescription"]?.() ??
              "Fill in the information below to create a new course.")}
        </p>
      </div>

      <form
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
      >
        <div className="grid gap-8 lg:grid-cols-2">
          {/* Left Column - Basic Information */}
          <div className="space-y-6">
            <div className="rounded-lg border bg-card p-6 shadow-sm">
              <h2 className="mb-4 font-semibold text-xl">Basic Information</h2>
              <div className="space-y-4">
                <form.Field
                  name="name"
                  validators={{
                    onChange: ({ value }) =>
                      !value || value.trim().length < 3
                        ? (m["courseEditor.validation.nameRequired"]?.() ??
                          "Course name is required (minimum 3 characters)")
                        : undefined,
                  }}
                  children={(field) => (
                    <div className="space-y-2">
                      <label className="font-medium text-sm">Course Name</label>
                      <Input
                        placeholder={
                          m["courseEditor.labels.namePlaceholder"]?.() ?? "Name"
                        }
                        value={field.state.value}
                        onChange={(e) => {
                          field.handleChange(e.target.value);

                          // Auto-generate slug when name changes
                          if (autoRenderSlug) {
                            form.setFieldValue(
                              "slug",
                              convertToSlug(e.target.value, {
                                locale: "vi",
                                lower: true,
                              }),
                            );
                          }
                        }}
                        className={
                          field.state.meta.errors.length > 0
                            ? "border-destructive"
                            : ""
                        }
                      />
                      {field.state.meta.errors.length > 0 && (
                        <p className="text-destructive text-sm">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                    </div>
                  )}
                />

                <form.Field
                  name="slug"
                  validators={{
                    onChange: ({ value }) =>
                      !value || value.trim().length < 3
                        ? (m["courseEditor.validation.slugRequired"]?.() ??
                          "URL slug is required (minimum 3 characters)")
                        : undefined,
                  }}
                  children={(field) => (
                    <div className="space-y-2">
                      <label className="font-medium text-sm">URL Slug</label>
                      <Input
                        placeholder={
                          m["courseEditor.labels.slugPlaceholder"]?.() ?? "Slug"
                        }
                        value={field.state.value}
                        onChange={(e) => {
                          field.handleChange(e.target.value);
                          setAutoRenderSlug(false); // Disable auto-slug when manually edited
                        }}
                        className={
                          field.state.meta.errors.length > 0
                            ? "border-destructive"
                            : ""
                        }
                      />
                      {field.state.meta.errors.length > 0 && (
                        <p className="text-destructive text-sm">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                    </div>
                  )}
                />

                <form.Field
                  name="image_url"
                  children={(field) => (
                    <div className="space-y-3">
                      <label className="font-medium text-sm">
                        Course Image
                      </label>

                      {/* Compact Image Input */}
                      <div className="flex gap-2">
                        <Input
                          placeholder="Enter image URL"
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          className="flex-1"
                        />

                        {/* Compact Upload Button */}
                        <div className="relative w-1/4">
                          <input
                            id="image-upload-input"
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleUpload(e, "image_url")}
                            className="hidden"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            disabled={uploading}
                            onClick={() =>
                              document
                                .getElementById("image-upload-input")
                                ?.click()
                            }
                            className={`w-full px-3 ${
                              uploading ? "opacity-50" : ""
                            }`}
                          >
                            Upload
                            {uploading ? (
                              <div className="h-4 w-4 animate-spin rounded-full border-2 border-slate-400 border-t-transparent" />
                            ) : (
                              <Upload className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>

                      <p className="text-slate-500 text-xs">
                        Enter URL or upload JPG, PNG, GIF up to 10MB
                      </p>

                      {/* Compact Image Preview */}
                      {field.state.value && !uploading && (
                        <div className="mt-3">
                          <div className="group relative inline-block">
                            <img
                              src={field.state.value}
                              alt="Course preview"
                              className="h-20 w-32 rounded-md border border-slate-200 object-cover shadow-sm transition-all duration-200 group-hover:scale-105"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = "none";
                                target.parentElement?.insertAdjacentHTML(
                                  "afterend",
                                  '<div class="flex h-20 w-32 items-center justify-center rounded-md border border-red-200 bg-red-50 text-xs text-red-500">Failed to load</div>',
                                );
                              }}
                              onLoad={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = "block";
                                target.parentElement?.parentElement
                                  ?.querySelector(".text-red-500")
                                  ?.remove();
                              }}
                            />

                            {/* Compact Remove button */}
                            <button
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation();
                                field.handleChange("");
                              }}
                              className="-right-1 -top-1 absolute rounded-full bg-red-500 p-0.5 text-white shadow-md transition-all duration-200 hover:scale-110 hover:bg-red-600"
                            >
                              <X className="h-2.5 w-2.5" />
                            </button>

                            {/* Compact badge */}
                            <div className="absolute bottom-1 left-1 rounded bg-black/70 px-1.5 py-0.5 text-white text-xs">
                              {field.state.value.includes("aicademy.org")
                                ? "📤"
                                : "🔗"}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                />

                <form.Field
                  name="categories"
                  validators={{
                    onChange: ({ value }) => {
                      if (!value || value.length === 0) {
                        return "Please select at least one category";
                      }
                      return undefined;
                    },
                  }}
                  children={(field) => (
                    <div className="space-y-2">
                      <label className="font-medium text-sm">Categories</label>
                      <MultiSelector
                        options={categoryOptions || categories}
                        selectedValues={
                          (field.state.value
                            ?.map((id) => {
                              const category = categories?.find(
                                (c) => c.id === id,
                              );
                              return category
                                ? {
                                    value: category.id as string,
                                    label: category.name,
                                  }
                                : undefined;
                            })
                            .filter((item) => item !== undefined) as {
                            value: string;
                            label: string;
                          }[]) || []
                        }
                        onSelect={(selected) => {
                          const categoryIds = selected.map((s) => s.value);
                          field.handleChange(categoryIds);
                        }}
                        placeholder={
                          m["courseEditor.labels.categoriesPlaceholder"]?.() ??
                          "Select categories..."
                        }
                      />
                      {field.state.meta.errors.length > 0 && (
                        <p className="text-destructive text-sm">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                    </div>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <form.Field
                    name="level"
                    validators={{
                      onChange: ({ value }) => {
                        if (!value || value.trim() === "") {
                          return "Course level is required";
                        }
                        return undefined;
                      },
                    }}
                    children={(field) => (
                      <div className="space-y-2">
                        <label className="font-medium text-sm">Level</label>
                        <Select
                          onValueChange={(value) => {
                            field.handleChange(value);
                          }}
                          value={field.state.value}
                        >
                          <SelectTrigger
                            className={`w-full ${
                              field.state.meta.errors.length > 0
                                ? "border-destructive"
                                : ""
                            }`}
                          >
                            <SelectValue
                              placeholder={
                                m[
                                  "courseEditor.labels.studentLevelPlaceholder"
                                ]?.() ?? "Student level"
                              }
                            />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={CourseLevel.BEGINNER}>
                              {COURSE_LEVEL_LABELS[CourseLevel.BEGINNER]}
                            </SelectItem>
                            <SelectItem value={CourseLevel.INTERMEDIATE}>
                              {COURSE_LEVEL_LABELS[CourseLevel.INTERMEDIATE]}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        {field.state.meta.errors.length > 0 && (
                          <p className="text-destructive text-sm">
                            {field.state.meta.errors[0]}
                          </p>
                        )}
                      </div>
                    )}
                  />

                  <form.Field
                    name="language"
                    validators={{
                      onChange: ({ value }) => {
                        if (!value || value.trim() === "") {
                          return "Language is required";
                        }
                        return undefined;
                      },
                    }}
                    children={(field) => (
                      <div className="space-y-2">
                        <label className="font-medium text-sm">Language</label>
                        <Select
                          onValueChange={(value) => {
                            field.handleChange(value);
                          }}
                          value={field.state.value}
                        >
                          <SelectTrigger
                            className={`w-full ${
                              field.state.meta.errors.length > 0
                                ? "border-destructive"
                                : ""
                            }`}
                          >
                            <SelectValue
                              placeholder={
                                m[
                                  "courseEditor.labels.languagePlaceholder"
                                ]?.() ?? "Language"
                              }
                            />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={CourseLanguage.VI}>
                              {COURSE_LANGUAGE_LABELS[CourseLanguage.VI]}
                            </SelectItem>
                            <SelectItem value={CourseLanguage.EN}>
                              {COURSE_LANGUAGE_LABELS[CourseLanguage.EN]}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        {field.state.meta.errors.length > 0 && (
                          <p className="text-destructive text-sm">
                            {field.state.meta.errors[0]}
                          </p>
                        )}
                      </div>
                    )}
                  />
                </div>

                <form.Field
                  name="published_at"
                  children={(field) => (
                    <div className="flex items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <label className="font-medium text-sm">
                          Publication Status
                        </label>
                        <p className="text-muted-foreground text-xs">
                          Control whether this course is visible to students
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={field.state.value != null}
                          onCheckedChange={(checked) => {
                            const value = checked
                              ? new Date().toISOString()
                              : undefined;
                            field.handleChange(value);
                          }}
                        />
                        <span className="font-medium text-sm">
                          {!field.state.value
                            ? (m["courseEditor.labels.unpublishedLabel"]?.() ??
                              "Unpublished")
                            : (m["courseEditor.labels.publishedLabel"]?.() ??
                              "Published")}
                        </span>
                      </div>
                    </div>
                  )}
                />
              </div>
            </div>
          </div>

          {/* Right Column - Content */}
          <div className="space-y-6">
            <div className="rounded-lg border bg-card p-6 shadow-sm">
              <h2 className="mb-4 font-semibold text-xl">Course Content</h2>

              <div className="space-y-6">
                <form.Field
                  name="overview"
                  validators={{
                    onChange: ({ value }) => {
                      if (!value || value.trim().length < 10) {
                        return "Course overview is required (minimum 10 characters)";
                      }
                      return undefined;
                    },
                  }}
                  children={(field) => (
                    <div className="space-y-2">
                      <label className="font-medium text-sm">
                        {m["courseEditor.labels.overviewLabel"]?.() ??
                          "What you'll learn:"}
                      </label>
                      <textarea
                        className={`min-h-32 w-full rounded-md border border-input bg-background px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring ${
                          field.state.meta.errors.length > 0
                            ? "border-destructive"
                            : ""
                        }`}
                        value={field.state.value}
                        placeholder={
                          m["courseEditor.labels.overviewPlaceholder"]?.() ??
                          "Overview..."
                        }
                        name="description"
                        rows={COURSE_EDITOR_CONFIG.DEFAULTS.TEXTAREA_ROWS}
                        onChange={(e) => {
                          field.handleChange(e.target.value);
                        }}
                      />
                      {field.state.meta.errors.length > 0 && (
                        <p className="text-destructive text-sm">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                    </div>
                  )}
                />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <label className="font-medium text-sm">
                      {m["courseEditor.labels.goalsLabel"]?.() ??
                        "Course goals"}
                    </label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const currentGoals = form.getFieldValue("goals");
                        form.setFieldValue("goals", [
                          ...currentGoals,
                          COURSE_EDITOR_CONFIG.DEFAULTS.EMPTY_GOAL,
                        ]);
                      }}
                      className="text-xs"
                      type="button"
                    >
                      {m["courseEditor.labels.addGoalButton"]?.() ??
                        "Add new goal"}
                    </Button>
                  </div>

                  <form.Field
                    name="goals"
                    validators={{
                      onChange: ({ value }) => {
                        if (!value || value.length === 0) {
                          return "At least one course goal is required";
                        }
                        const validGoals = value.filter(
                          (goal) => goal.trim() !== "",
                        );
                        if (validGoals.length === 0) {
                          return "At least one meaningful course goal is required";
                        }
                        return undefined;
                      },
                    }}
                    children={(field) => (
                      <div className="space-y-3">
                        {field.state.value?.map((goal, idx) => {
                          return (
                            <div
                              key={idx}
                              className="flex items-center gap-3 rounded-lg border p-3"
                            >
                              <CheckIcon className="h-4 w-4 flex-shrink-0 text-green-600" />
                              <Input
                                onChange={(e) => {
                                  const currentGoals = field.state.value;
                                  const newGoals = [...currentGoals];
                                  newGoals[idx] = e.target.value;
                                  field.handleChange(newGoals);
                                }}
                                placeholder={`${
                                  m[
                                    "courseEditor.labels.goalPlaceholder"
                                  ]?.() ?? "Goal"
                                } ${idx + 1}`}
                                value={goal}
                                className="flex-1"
                              />
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const currentGoals = field.state.value;
                                  const newGoals = currentGoals.filter(
                                    (_, index) => index !== idx,
                                  );
                                  field.handleChange(newGoals);
                                }}
                                className="h-8 w-8 p-0 text-destructive hover:bg-destructive/10"
                                type="button"
                              >
                                ×
                              </Button>
                            </div>
                          );
                        })}
                        {field.state.meta.errors.length > 0 && (
                          <p className="text-destructive text-sm">
                            {field.state.meta.errors[0]}
                          </p>
                        )}
                      </div>
                    )}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>

      {/* Footer Actions */}
      <div className="flex items-center justify-end gap-5 border-t pt-6">
        <Button
          variant="outline"
          onClick={() => navigate({ to: "/dashboard/courses" })}
        >
          {m["courseEditor.labels.cancelButton"]?.() ?? "Cancel"}
        </Button>
        <form.Subscribe
          selector={(state) => [state.canSubmit, state.isSubmitting]}
          children={([canSubmit, isSubmitting]) => (
            <Button
              type="submit"
              onClick={() => form.handleSubmit()}
              disabled={!canSubmit || uploading || isSubmitting}
              className="min-w-24"
            >
              {uploading || isSubmitting
                ? (m["courseEditor.labels.uploadingText"]?.() ?? "uploading...")
                : (m["courseEditor.labels.saveButton"]?.() ?? "Save")}
            </Button>
          )}
        />
      </div>
    </div>
  );
}
