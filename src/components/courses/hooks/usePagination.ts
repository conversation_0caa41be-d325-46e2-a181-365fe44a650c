import { useMemo, useState } from "react";
import { PaginationState } from "@/routes/dashboard/courses/types";

export function usePagination<T>(items: T[], initialItemsPerPage = 10) {
  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    itemsPerPage: initialItemsPerPage,
  });

  const totalPages = Math.ceil(items.length / pagination.itemsPerPage);
  const startIndex = (pagination.currentPage - 1) * pagination.itemsPerPage;
  const endIndex = startIndex + pagination.itemsPerPage;

  const currentItems = useMemo(() => {
    return items.slice(startIndex, endIndex);
  }, [items, startIndex, endIndex]);

  const setCurrentPage = (page: number) => {
    setPagination((prev) => ({ ...prev, currentPage: page }));
  };

  const setItemsPerPage = (itemsPerPage: number) => {
    setPagination({ currentPage: 1, itemsPerPage });
  };

  const resetToFirstPage = () => {
    setPagination((prev) => ({ ...prev, currentPage: 1 }));
  };

  return {
    currentItems,
    pagination,
    totalPages,
    startIndex,
    endIndex,
    setCurrentPage,
    setItemsPerPage,
    resetToFirstPage,
  };
}
