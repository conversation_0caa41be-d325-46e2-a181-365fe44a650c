import { useMemo, useState } from "react";
import { Course } from "@/types/courses";

// Đồng bộ với type global
interface CourseFilters {
  searchTerm: string;
  status: string;
  level: string;
  language: string;
}

export function useCourseFilters(courses: Course[]) {
  const [filters, setFilters] = useState<CourseFilters>({
    searchTerm: "",
    status: "all",
    level: "all",
    language: "all",
  });

  const filteredCourses = useMemo(() => {
    return courses.filter((course) => {
      const matchesSearch = (course.title || course.name || "")
        .toLowerCase()
        .includes(filters.searchTerm.toLowerCase());
      // published: published_at có dữ liệu hợp lệ, draft: không có
      let courseStatus: "published" | "draft" = course.published_at
        ? "published"
        : "draft";
      const matchesStatus =
        filters.status === "all" || courseStatus === filters.status;
      const matchesLevel =
        filters.level === "all" || course.level === filters.level;
      const matchesLanguage =
        filters.language === "all" || course.language === filters.language;
      return matchesSearch && matchesStatus && matchesLevel && matchesLanguage;
    });
  }, [courses, filters]);

  const updateFilter = (type: keyof CourseFilters, value: string) => {
    setFilters((prev) => ({ ...prev, [type]: value }));
  };

  return {
    filters,
    filteredCourses,
    updateFilter,
  };
}
