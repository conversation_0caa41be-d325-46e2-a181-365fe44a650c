import { BookOpen, Plus } from "lucide-react";
import { m } from "@/paraglide/messages.js";
import { Course } from "@/types/courses";
import { Button } from "../ui/button";

interface EmptyCourseProps {
  courses: Course[];
  setIsCreateDialogOpen: (open: boolean) => void;
}

export const EmptyCourse = ({
  courses,
  setIsCreateDialogOpen,
}: EmptyCourseProps) => {
  return (
    <tr>
      <td colSpan={7} className="p-12 text-center">
        <div className="flex flex-col items-center justify-center space-y-4">
          <div className="rounded-full bg-muted p-4">
            <BookOpen className="h-8 w-8 text-muted-foreground" />
          </div>
          <div className="space-y-2">
            {courses.length === 0 ? (
              <>
                <h3 className="font-semibold text-lg">
                  {m["courseList.empty.title"]?.() ?? "Chưa có khóa học nào"}
                </h3>
                <p className="max-w-sm text-muted-foreground">
                  {m["courseList.empty.description"]?.() ??
                    "Bắt đầu bằng cách tạo khóa học đầu tiên của bạn để quản lý nội dung học tập."}
                </p>
              </>
            ) : (
              <>
                <h3 className="font-semibold text-lg">
                  {m["courseList.empty.notFound"]?.() ??
                    "Không tìm thấy khóa học nào"}
                </h3>
                <p className="max-w-sm text-muted-foreground">
                  {m["courseList.empty.notFoundDesc"]?.() ??
                    "Không có khóa học nào phù hợp với bộ lọc hiện tại. Thử điều chỉnh bộ lọc của bạn."}
                </p>
              </>
            )}
          </div>
          {courses.length === 0 && (
            <Button
              onClick={() => setIsCreateDialogOpen(true)}
              className="mt-4"
            >
              <Plus className="mr-2 h-4 w-4" />
              {m["courseList.empty.createButton"]?.() ??
                "Tạo khóa học đầu tiên"}
            </Button>
          )}
        </div>
      </td>
    </tr>
  );
};
