import { Search } from "lucide-react";
import React from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { m } from "@/paraglide/messages.js";
import { CourseFilters as CourseFiltersType } from "@/types/courses";
import { CourseLanguage, CourseLevel } from "@/types/courses";

interface CourseFiltersProps {
  filters: CourseFiltersType;
  onFilterChange: (type: keyof CourseFiltersType, value: string) => void;
}

export function CourseFilters({ filters, onFilterChange }: CourseFiltersProps) {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <div className="relative">
        <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 transform text-muted-foreground" />
        <Input
          placeholder={m["sidebar.search"]() + "..."}
          value={filters.searchTerm}
          onChange={(e) => onFilterChange("searchTerm", e.target.value)}
          className="w-80 pl-10"
        />
      </div>

      <Select
        value={filters.status}
        onValueChange={(value) => onFilterChange("status", value)}
      >
        <SelectTrigger className="w-48">
          <SelectValue
            placeholder={
              m["courseFilters.statusPlaceholder"]
                ? m["courseFilters.statusPlaceholder"]()
                : "Chọn trạng thái"
            }
          />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">
            {m["courseFilters.statusAll"]
              ? m["courseFilters.statusAll"]()
              : "Tất cả trạng thái"}
          </SelectItem>
          <SelectItem value="published">{m["common.published"]()}</SelectItem>
          <SelectItem value="draft">{m["common.draft"]()}</SelectItem>
        </SelectContent>
      </Select>

      <Select
        value={filters.level}
        onValueChange={(value) => onFilterChange("level", value)}
      >
        <SelectTrigger className="w-48">
          <SelectValue
            placeholder={
              m["courseFilters.levelPlaceholder"]
                ? m["courseFilters.levelPlaceholder"]()
                : "Chọn cấp độ"
            }
          />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">
            {m["courseFilters.levelAll"]
              ? m["courseFilters.levelAll"]()
              : "Tất cả cấp độ"}
          </SelectItem>
          {Object.values(CourseLevel).map((level) => (
            <SelectItem key={level} value={level}>
              {level}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select
        value={filters.language}
        onValueChange={(value) => onFilterChange("language", value)}
      >
        <SelectTrigger className="w-48">
          <SelectValue
            placeholder={
              m["courseFilters.languagePlaceholder"]
                ? m["courseFilters.languagePlaceholder"]()
                : "Chọn ngôn ngữ"
            }
          />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">
            {m["courseFilters.languageAll"]
              ? m["courseFilters.languageAll"]()
              : "Tất cả ngôn ngữ"}
          </SelectItem>
          {Object.values(CourseLanguage).map((lang) => (
            <SelectItem key={lang} value={lang}>
              {lang}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
