import { Plus, Trash2, X } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import QuizSection from "@/components/QuizSection";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { updateSection } from "@/services/instructor";
import { Section } from "@/types/lessons";

interface GameSectionProps {
  section: Section;
}

type category = {
  label: string;
  items: string[];
};

type gameParams = {
  categories: category[];
  explanation: string;
};

type gameConfig = {
  type: string;
  game: string;
  description: string;
  reasoning: string;
  parameters: gameParams;
};

const games = [
  "categorizing",
  "sorting",
  "matching",
  "ordering",
  "sequencing",
  "true-false",
  "fill-in-the-blank",
  "touch",
  "escape-room",
  "roleplay",
  "bingo",
  "word-puzzle",
  "quiz",
];

export default function GameSection({ section }: GameSectionProps) {
  const [config, setConfig] = useState<gameConfig>();
  const [saving, setSaving] = useState(false);
  useEffect(() => {
    if (!section) return;
    try {
      if (!section.content || section.content === "") {
        return initNewConfig();
      }
      const cg = JSON.parse(section.content);
      if (!cg.type) initNewConfig();
      else setConfig(cg);
    } catch (e) {
      toast.error("Cannot parse game config. Reinit game config...");
      initNewConfig();
    }
  }, [section]);

  const initNewConfig = () => {
    setConfig({
      type: "",
      game: "",
      description: "",
      reasoning: "",
      parameters: {
        categories: [
          {
            label: "",
            items: ["", ""],
          },
        ],
        explanation: "",
      },
    });
  };

  const saveSection = (gameCfg: gameConfig) => {
    setSaving(true);
    updateSection({
      id: section.id,
      section: { ...section, content: JSON.stringify(gameCfg) },
    })
      .then(() => toast.success("Section saved successfully!"))
      .catch((e) => toast.error(`Section save failed: ${e.toString()}`))
      .finally(() => setSaving(false));
  };

  const onchangeConfig = (
    field: string,
    e: React.ChangeEvent<HTMLTextAreaElement | HTMLSelectElement> | string,
  ) => {
    if (!config) return;
    let buff: gameConfig = { ...config };

    const value = typeof e === "string" ? e : e.target.value;

    if (field === "explanation") {
      buff.parameters.explanation = value;
    } else {
      (buff as Record<string, unknown>)[field] = value;
    }
    setConfig(buff);
    if (field === "type" && value === "quizzes") {
      saveSection(buff);
    }
  };

  const addCategory = () => {
    if (!config) return;
    let buff: gameConfig = { ...config };
    buff.parameters.categories.push({ label: "", items: ["", ""] });
    setConfig(buff);
  };

  const addCategoryItems = (c_index: number) => {
    if (!config) return;
    let buff: gameConfig = { ...config };
    if (
      !buff.parameters ||
      !buff.parameters.categories ||
      !buff.parameters.categories[c_index]
    )
      return;
    buff.parameters.categories[c_index].items.push("");
    setConfig(buff);
  };

  const onchangeItems = (
    c_idx: number,
    i_idx: number,
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ) => {
    if (!config) return;
    let buff: gameConfig = { ...config };
    buff.parameters.categories[c_idx].items[i_idx] = e.target.value;
    setConfig(buff);
  };
  const onchangeCategoryLabel = (
    c_idx: number,
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    if (!config) return;
    let buff: gameConfig = { ...config };
    buff.parameters.categories[c_idx].label = e.target.value;
    setConfig(buff);
  };
  const removeCategory = (c_idx: number) => {
    const isConfirmed = window.confirm(
      `Are you sure you want to remove this category?`,
    );
    if (isConfirmed) {
      if (!config) return;
      let buff: gameConfig = { ...config };
      buff.parameters.categories = buff.parameters.categories.filter(
        (_, idx) => idx !== c_idx,
      );
      setConfig(buff);
    }
  };
  const removeItems = (c_idx: number, i_idx: number) => {
    if (!config) return;
    let buff: gameConfig = { ...config };
    buff.parameters.categories[c_idx].items = buff.parameters.categories[
      c_idx
    ].items.filter((_, idx) => idx !== i_idx);
    setConfig(buff);
  };

  const save = () => {
    if (!config || !config.description || !config.game || !config.type)
      return toast.warning("Invalid config");
    if (
      !config.parameters ||
      !config.parameters.explanation ||
      !config.parameters.categories ||
      config.parameters.categories.length === 0
    )
      return toast.warning("Invalid game parameters");
    for (let i = 0; i < config.parameters.categories.length; i++) {
      const cat = config.parameters.categories[i];
      if (!cat.label || !cat.items || cat.items.length === 0)
        return toast.warning(`Invalid category #${i + 1}`);
      for (let j = 0; j < cat.items.length; j++) {
        if (!cat.items[j])
          return toast.warning(`Invalid category #${i + 1}, item #${j + 1}`);
      }
    }
    saveSection(config);
  };

  return (
    <div className="w-full space-y-6">
      <Card className="mt-4">
        <CardHeader>
          <CardTitle>Game Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <Label htmlFor="game-type" className="font-medium text-sm">
                Type
              </Label>
              <Select
                value={config?.type || ""}
                onValueChange={(value) => onchangeConfig("type", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="quizzes">Quizzes Game</SelectItem>
                  <SelectItem value="category-based">Category Based</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {config?.type === "category-based" && (
              <div>
                <Label htmlFor="game-name" className="font-medium text-sm">
                  Game
                </Label>
                <Select
                  value={config?.game || ""}
                  onValueChange={(value) => onchangeConfig("game", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select game" />
                  </SelectTrigger>
                  <SelectContent>
                    {games.map((game, idx) => (
                      <SelectItem key={idx} value={game}>
                        {game}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      {config?.type === "category-based" && (
        <>
          <Card>
            <CardHeader>
              <CardTitle>Game Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="description" className="font-medium text-sm">
                  Description
                </Label>
                <Textarea
                  id="description"
                  placeholder="Game description"
                  defaultValue={config?.description ?? ""}
                  rows={2}
                  onChange={(e) => onchangeConfig("description", e)}
                />
              </div>
              <div>
                <Label htmlFor="reasoning" className="font-medium text-sm">
                  Reasoning (Optional)
                </Label>
                <Textarea
                  id="reasoning"
                  placeholder="Optional reasoning"
                  defaultValue={config?.reasoning ?? ""}
                  rows={2}
                  onChange={(e) => onchangeConfig("reasoning", e)}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Parameters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="mb-4 flex items-center justify-between">
                    <Label className="font-medium text-sm">Categories</Label>
                    <Button variant="outline" size="sm" onClick={addCategory}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add Category
                    </Button>
                  </div>
                  <div className="space-y-4">
                    {config?.parameters?.categories?.map(
                      (category, category_idx) => (
                        <Card key={category_idx} className="relative">
                          <CardContent className="p-4">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="absolute top-2 right-2 text-red-600 hover:text-red-700"
                              onClick={() => removeCategory(category_idx)}
                              title="Remove category"
                            >
                              <X className="h-4 w-4" />
                            </Button>

                            <div className="space-y-4">
                              <div>
                                <Label className="font-medium text-sm">
                                  Category #{category_idx + 1} Label
                                </Label>
                                <Input
                                  placeholder={`Label #${category_idx + 1}`}
                                  defaultValue={category.label ?? ""}
                                  onChange={(e) =>
                                    onchangeCategoryLabel(category_idx, e)
                                  }
                                />
                              </div>

                              <div>
                                <div className="mb-2 flex items-center justify-between">
                                  <Label className="font-medium text-sm">
                                    Items
                                  </Label>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() =>
                                      addCategoryItems(category_idx)
                                    }
                                  >
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add item
                                  </Button>
                                </div>
                                <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
                                  {category.items?.map((item, item_idx) => (
                                    <div
                                      className="flex items-center space-x-2"
                                      key={`${category_idx}-${item_idx}`}
                                    >
                                      <Textarea
                                        rows={1}
                                        placeholder={`Item #${item_idx + 1}`}
                                        defaultValue={item ?? ""}
                                        onChange={(e) =>
                                          onchangeItems(
                                            category_idx,
                                            item_idx,
                                            e,
                                          )
                                        }
                                        className="flex-1"
                                      />
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() =>
                                          removeItems(category_idx, item_idx)
                                        }
                                        title="Remove item"
                                        className="text-red-600 hover:text-red-700"
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ),
                    )}
                  </div>
                </div>

                <div className="mt-4">
                  <Label htmlFor="explanation" className="font-medium text-sm">
                    Explanation
                  </Label>
                  <Textarea
                    id="explanation"
                    placeholder="Explanation"
                    defaultValue={config?.parameters?.explanation ?? ""}
                    rows={2}
                    onChange={(e) => onchangeConfig("explanation", e)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button variant="default" disabled={saving} onClick={save}>
              {saving ? "Saving" : "Save"}
            </Button>
          </div>
        </>
      )}
      {config?.type === "quizzes" && <QuizSection sectionId={section.id} />}
    </div>
  );
}
