import { Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { DeleteQuiz, useQuizzes } from "@/services/instructor";
import QuizEditor from "./QuizEditor";

interface QuizSectionProps {
  sectionId: string;
}

export default function QuizSection(props: QuizSectionProps) {
  const { sectionId } = props;
  const [editingQuiz, setEditingQuiz] = useState(-2);
  const { data: quizzes, refetch } = useQuizzes(sectionId);
  const rmQuiz = (quiz) => {
    if (!quiz.id) return;
    const isConfirmed = window.confirm(
      `Are you sure you want to remove this quiz?`,
    );
    if (isConfirmed) {
      setEditingQuiz(-2);
    }

    DeleteQuiz({ id: quiz.id })
      .then(() => {
        toast.success("Removed quiz");
        refetch();
      })
      .catch((e) => toast.error(`Remove quiz failed. ${e.toString()}`));
  };

  const cancelEditor = () => {
    const isConfirmed = window.confirm(
      `Are you sure you want to cancel editing?`,
    );
    if (isConfirmed) {
      setEditingQuiz(-2);
    }
  };

  const onSave = () => {
    setEditingQuiz(-2);
    refetch();
  };

  return (
    <div className="mt-4 h-max-content w-full">
      {quizzes &&
        quizzes.length > 0 &&
        quizzes.map((quiz, idx) => {
          if (editingQuiz === idx)
            return (
              <Card className="mb-3" key={idx}>
                <CardContent className="p-4">
                  <QuizEditor
                    sectionId={sectionId}
                    quizIndex={idx}
                    selectedQuiz={quiz}
                    cancel={cancelEditor}
                    onSave={onSave}
                  />
                </CardContent>
              </Card>
            );

          return (
            <Card className="relative mb-3" key={idx}>
              <CardContent className="p-4">
                <p className="font-bold">
                  #{idx + 1}: {quiz.question}
                </p>
                <div className="mt-2 grid auto-rows-min gap-4 md:grid-cols-2 xl:grid-cols-2">
                  {quiz?.answers?.map((answer, idx) => (
                    <div key={idx} className="flex items-center">
                      <div className="me-3">
                        {quiz.correct_answer?.includes(idx) ? "✅" : "✖️"}
                      </div>
                      <p>{answer}</p>
                    </div>
                  ))}
                </div>
                <p className="mt-3 italic">
                  <b>Explanation:</b>{" "}
                  {quiz.explanation || "No explanation provided"}
                </p>
                <div className="absolute top-3 right-3 flex gap-2">
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => rmQuiz(quiz)}
                  >
                    Remove
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => setEditingQuiz(idx)}
                  >
                    Edit
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      {editingQuiz === -1 && (
        <Card className="mb-3">
          <CardContent className="p-4">
            <QuizEditor
              quizIndex={quizzes?.length || 0}
              sectionId={sectionId}
              cancel={cancelEditor}
              onSave={onSave}
            />
          </CardContent>
        </Card>
      )}
      <Button variant="default" onClick={() => setEditingQuiz(-1)}>
        <Plus className="mr-2 h-4 w-4" />
        Add new quiz
      </Button>
    </div>
  );
}
