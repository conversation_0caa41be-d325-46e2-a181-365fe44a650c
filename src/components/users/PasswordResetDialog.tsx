import { useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { User } from "@/types/users";

interface PasswordResetDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId?: string;
  user?: User;
}

export function PasswordResetDialog({
  open,
  onOpenChange,
  userId,
  user,
}: PasswordResetDialogProps) {
  // State for password reset type selection
  const [passwordResetType, setPasswordResetType] = useState("auto");
  const [isResetting, setIsResetting] = useState(false);

  // Handle form submission
  const handleConfirm = async () => {
    if (!userId) return;

    try {
      setIsResetting(true);
      console.log(
        `Resetting password for user ${userId} with type ${passwordResetType}`,
      );
      // Mock API call for password reset
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast.success(`Đã reset mật khẩu cho ${user?.name} thành công`);
      onOpenChange(false);
    } catch (error) {
      console.error("Error resetting password:", error);
      toast.error("Không thể reset mật khẩu", {
        description: "Đã xảy ra lỗi khi thực hiện thao tác này.",
      });
    } finally {
      setIsResetting(false);
    }
  };

  // Reset form when dialog opens
  const handleOpenChange = (isOpen: boolean) => {
    if (isOpen) {
      setPasswordResetType("auto");
    }
    onOpenChange(isOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Đặt lại mật khẩu cho {user?.name ?? ""}</DialogTitle>
          <DialogDescription>
            Chọn phương thức đặt lại mật khẩu
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-3">
            {/* Auto password generation option */}
            <div className="flex items-start space-x-3">
              <input
                type="radio"
                id="auto-password"
                name="password-type"
                value="auto"
                checked={passwordResetType === "auto"}
                onChange={(e) => setPasswordResetType(e.target.value)}
                className="mt-1"
              />
              <div className="flex-1">
                <label
                  htmlFor="auto-password"
                  className="cursor-pointer font-medium text-sm"
                >
                  Tự động tạo mật khẩu
                </label>
                <p className="text-muted-foreground text-sm">
                  Bạn sẽ có thể xem và sao chép mật khẩu ở bước tiếp theo
                </p>
              </div>
            </div>

            {/* Manual password option */}
            <div className="flex items-start space-x-3">
              <input
                type="radio"
                id="manual-password"
                name="password-type"
                value="manual"
                checked={passwordResetType === "manual"}
                onChange={(e) => setPasswordResetType(e.target.value)}
                className="mt-1"
              />
              <div className="flex-1">
                <label
                  htmlFor="manual-password"
                  className="cursor-pointer font-medium text-sm"
                >
                  Tạo mật khẩu thủ công
                </label>
                <p className="text-muted-foreground text-sm">
                  Bạn sẽ nhập mật khẩu mới cho người dùng
                </p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isResetting}
          >
            Hủy
          </Button>
          <Button onClick={handleConfirm} disabled={isResetting}>
            {isResetting ? "Đang xử lý..." : "Đặt lại"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
