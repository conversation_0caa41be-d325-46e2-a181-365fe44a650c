import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { deleteUser } from "@/services/admin";
import type { User } from "@/types/users";

interface DeleteUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId?: string;
  user?: User;
}

export function DeleteUserDialog({
  open,
  onOpenChange,
  userId,
  user,
}: DeleteUserDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const queryClient = useQueryClient();

  console.log("user", user);
  // Handle form submission
  const handleConfirm = async () => {
    if (!userId) return;

    try {
      setIsDeleting(true);
      await deleteUser([userId]);

      // Invalidate and refetch users
      queryClient.invalidateQueries({ queryKey: ["admin-users"] });

      toast.success(`Đã xóa người dùng ${user?.name} thành công`);
      onOpenChange(false);
    } catch (error) {
      console.error("Error deleting user:", error);
      toast.error("Không thể xóa người dùng", {
        description: "Đã xảy ra lỗi khi thực hiện thao tác này.",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Xóa người dùng</DialogTitle>
          <DialogDescription>
            Bạn có chắc chắn muốn xóa người dùng <strong>{user?.name}</strong>?
            Thao tác này không thể hoàn tác.
          </DialogDescription>
        </DialogHeader>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
          >
            Hủy
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={isDeleting}
          >
            {isDeleting ? "Đang xóa..." : "Xóa"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
