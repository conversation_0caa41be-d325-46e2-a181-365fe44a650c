import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { Button } from "@/components/ui/button";
import {
  changeItemsPerPageAtom,
  changePageAtom,
  currentPageAtom,
  itemsPerPageAtom,
  paginationInfoAtom,
} from "@/store/users";

export function UserPagination() {
  const currentPage = useAtomValue(currentPageAtom);
  const itemsPerPage = useAtomValue(itemsPerPageAtom);
  const {
    totalUsers: totalItems,
    totalPages,
    startIndex,
    endIndex,
  } = useAtomValue(paginationInfoAtom);

  // Get action dispatchers
  const changePage = useSetAtom(changePageAtom);
  const changeItemsPerPage = useSetAtom(changeItemsPerPageAtom);

  // Action handlers that dispatch to atoms
  const handlePageChange = (page: number) => {
    changePage(page);
  };

  const handleItemsPerPageChange = (items: number) => {
    changeItemsPerPage(items);
  };
  if (totalPages <= 1) return null;

  // Function to generate page numbers with ellipsis
  const generatePageNumbers = () => {
    const pages: React.ReactNode[] = [];
    const showEllipsis = totalPages > 7;

    if (!showEllipsis) {
      // Show all pages if 7 or fewer
      for (let i = 1; i <= totalPages; i++) {
        pages.push(
          <Button
            key={i}
            variant={currentPage === i ? "default" : "outline"}
            size="sm"
            className={`h-10 w-10 p-0 ${currentPage === i ? "bg-blue-500 text-white hover:bg-blue-600" : "hover:bg-gray-50"} cursor-pointer`}
            onClick={() => handlePageChange(i)}
          >
            {i}
          </Button>,
        );
      }
    } else {
      // Show first page
      pages.push(
        <Button
          key={1}
          variant={currentPage === 1 ? "default" : "outline"}
          size="sm"
          className={`h-10 w-10 p-0 ${currentPage === 1 ? "bg-blue-500 text-white hover:bg-blue-600" : "hover:bg-gray-50"}`}
          onClick={() => handlePageChange(1)}
        >
          1
        </Button>,
      );

      // Show ellipsis if current page is far from start
      if (currentPage > 4) {
        pages.push(
          <span key="ellipsis1" className="px-2 text-muted-foreground">
            ...
          </span>,
        );
      }

      // Show pages around current page
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);

      for (let i = start; i <= end; i++) {
        if (i !== 1 && i !== totalPages) {
          pages.push(
            <Button
              key={i}
              variant={currentPage === i ? "default" : "outline"}
              size="sm"
              className={`h-10 w-10 p-0 ${currentPage === i ? "bg-blue-500 text-white hover:bg-blue-600" : "hover:bg-gray-50"}`}
              onClick={() => handlePageChange(i)}
            >
              {i}
            </Button>,
          );
        }
      }

      // Show ellipsis if current page is far from end
      if (currentPage < totalPages - 3) {
        pages.push(
          <span key="ellipsis2" className="px-2 text-muted-foreground">
            ...
          </span>,
        );
      }

      // Show last page
      if (totalPages > 1) {
        pages.push(
          <Button
            key={totalPages}
            variant={currentPage === totalPages ? "default" : "outline"}
            size="sm"
            className={`h-10 w-10 p-0 ${currentPage === totalPages ? "bg-blue-500 text-white hover:bg-blue-600" : "hover:bg-gray-50"}`}
            onClick={() => handlePageChange(totalPages)}
          >
            {totalPages}
          </Button>,
        );
      }
    }

    return pages;
  };

  return (
    <div className="mt-6 flex items-center justify-between">
      <div className="text-muted-foreground text-sm">
        Results: {startIndex + 1} - {Math.min(endIndex, totalItems)} of{" "}
        {totalItems}
      </div>

      <div className="flex items-center gap-2">
        {/* Previous button */}
        <Button
          variant="outline"
          size="sm"
          className="h-10 w-10 cursor-pointer bg-transparent p-0 disabled:cursor-not-allowed disabled:opacity-50"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <span className="text-lg">‹</span>
        </Button>

        {/* Page numbers */}
        <div className="flex items-center gap-1">{generatePageNumbers()}</div>

        {/* Next button */}
        <Button
          variant="outline"
          size="sm"
          className="h-10 w-10 cursor-pointer bg-transparent p-0 disabled:cursor-not-allowed"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          <span className="text-lg">›</span>
        </Button>
      </div>

      {/* Items per page selector */}
      <div className="flex items-center gap-2">
        <span className="text-muted-foreground text-sm">Hiển thị:</span>
        <select
          value={itemsPerPage.toString()}
          onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
          className="rounded border px-2 py-1 text-sm"
        >
          <option value="5">5</option>
          <option value="10">10</option>
          <option value="20">20</option>
          <option value="50">50</option>
        </select>
        <span className="text-muted-foreground text-sm">mục</span>
      </div>
    </div>
  );
}
