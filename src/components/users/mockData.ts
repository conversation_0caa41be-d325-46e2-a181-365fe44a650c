import { ImportHistoryItem, User } from "@/types/users";

// Enhanced user templates with detailed information
// This merges data from mockData.ts and generateUserData function
const userTemplates = [
  {
    name: "<PERSON><PERSON><PERSON><PERSON>n <PERSON>",
    email: "nguy<PERSON><PERSON>@company.com",
    department: "<PERSON><PERSON> thuật",
    role: "Instructor",
    detailRole: "Developer",
  },
  {
    name: "Trần Thị B",
    email: "<EMAIL>",
    department: "Marketing",
    role: "Learner",
    detailRole: "Marketing Manager",
  },
  {
    name: "<PERSON><PERSON> <PERSON>",
    email: "<EMAIL>",
    department: "<PERSON><PERSON><PERSON> sự",
    role: "Org Admin",
    detailRole: "HR Specialist",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    department: "<PERSON><PERSON> hàng",
    role: "Learner",
    detailRole: "Sales Executive",
  },
  {
    name: "<PERSON><PERSON><PERSON> Văn <PERSON>",
    email: "hoang<PERSON><EMAIL>",
    department: "<PERSON><PERSON><PERSON>",
    role: "<PERSON><PERSON>",
    detailRole: "Senior Developer",
  },
  {
    name: "<PERSON><PERSON> <PERSON><PERSON>",
    email: "<EMAIL>",
    department: "<PERSON><PERSON> thuậ<PERSON>",
    role: "Instructor",
    detailRole: "Content Creator",
  },
  {
    name: "Vũ Minh G",
    email: "<EMAIL>",
    department: "Marketing",
    role: "Learner",
    detailRole: "Recruiter",
  },
  {
    name: "Đặng Thị H",
    email: "<EMAIL>",
    department: "Nhân sự",
    role: "Learner",
    detailRole: "Account Manager",
  },
  {
    name: "Bùi Văn I",
    email: "<EMAIL>",
    department: "Bán hàng",
    role: "Learner",
    detailRole: "DevOps Engineer",
  },
  {
    name: "Lý Thị K",
    email: "<EMAIL>",
    department: "Tài chính",
    role: "Org Admin",
    detailRole: "Digital Marketer",
  },
  {
    name: "Trương Văn L",
    email: "<EMAIL>",
    department: "Kỹ thuật",
    role: "Learner",
    detailRole: "Training Manager",
  },
  {
    name: "Phạm Thị M",
    email: "<EMAIL>",
    department: "Marketing",
    role: "Instructor",
    detailRole: "Sales Manager",
  },
  {
    name: "Nguyễn Minh N",
    email: "<EMAIL>",
    department: "Nhân sự",
    role: "Learner",
    detailRole: "Frontend Developer",
  },
  {
    name: "Võ Thị O",
    email: "<EMAIL>",
    department: "Bán hàng",
    role: "Learner",
    detailRole: "Brand Manager",
  },
  {
    name: "Đinh Văn P",
    email: "<EMAIL>",
    department: "Tài chính",
    role: "Learner",
    detailRole: "HR Director",
  },
];

// Function to generate comprehensive user data
function generateEnhancedUserData(index: number): User {
  const template = userTemplates[index];
  const userId = index + 1;

  return {
    id: userId,
    name: template.name,
    email: template.email,
    department: template.department,
    role: template.role,
    status: userId % 3 === 0 ? "inactive" : "active",
    lastLogin: getUserLastLogin(userId),
    joinDate: getUserJoinDate(userId),
    avatar: `/placeholder.svg?height=80&width=80`,
    // Enhanced properties from generateUserData
    phone: `012345${6789 + index}`,
    manager: index > 0 ? userTemplates[0].name : "Giám đốc",
    location: index % 2 === 0 ? "Hà Nội" : "TP.HCM",
    coursesEnrolled: 3 + (index % 5),
    coursesCompleted: 1 + (index % 4),
    averageScore: 75 + (index % 25),
    lastActivity: `${1 + (index % 24)} giờ trước`,
  };
}

// Helper function to generate last login dates
function getUserLastLogin(userId: number): string {
  const dates = [
    "2024-01-15",
    "2024-01-14",
    "2024-01-10",
    "2024-01-16",
    "2024-01-13",
    "2024-01-15",
    "2024-01-08",
    "2024-01-14",
    "2024-01-16",
    "2024-01-15",
    "2024-01-12",
    "2024-01-09",
    "2024-01-16",
    "2024-01-14",
    "2024-01-13",
  ];
  return dates[(userId - 1) % dates.length];
}

// Helper function to generate join dates
function getUserJoinDate(userId: number): string {
  const dates = [
    "2023-06-15",
    "2023-08-20",
    "2023-05-10",
    "2023-09-05",
    "2023-07-12",
    "2023-04-18",
    "2023-10-22",
    "2023-11-30",
    "2023-12-05",
    "2023-03-15",
    "2023-08-08",
    "2023-06-25",
    "2023-09-18",
    "2023-11-12",
    "2023-07-28",
  ];
  return dates[(userId - 1) % dates.length];
}

// Generate the enhanced user list - merging mockData.ts and generateUserData()
export const users: User[] = userTemplates.map((_, index) =>
  generateEnhancedUserData(index),
);

export const departments = [
  "Kỹ thuật",
  "Marketing",
  "Nhân sự",
  "Bán hàng",
  "Tài chính",
];

// Mock data for import history
export const importHistory: ImportHistoryItem[] = [
  {
    id: 1,
    fileName: "nhan_vien_q1_2024.xlsx",
    importedBy: "Nguyễn Văn Admin",
    timestamp: "2024-01-15 14:30",
    totalRows: 150,
    successRows: 145,
    errorRows: 5,
    status: "completed",
  },
  {
    id: 2,
    fileName: "new_employees_march.xlsx",
    importedBy: "Trần Thị HR",
    timestamp: "2024-03-10 09:15",
    totalRows: 25,
    successRows: 25,
    errorRows: 0,
    status: "completed",
  },
  {
    id: 3,
    fileName: "bulk_import_failed.xlsx",
    importedBy: "Lê Minh Manager",
    timestamp: "2024-02-28 16:45",
    totalRows: 80,
    successRows: 0,
    errorRows: 80,
    status: "failed",
  },
];
