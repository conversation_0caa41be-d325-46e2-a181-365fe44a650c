import { useAtomValue } from "jotai";
import { Clock, FileSpreadsheet } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { importHistoryDataAtom } from "@/store/users";

export function ImportHistory() {
  const importHistory = useAtomValue(importHistoryDataAtom);
  return (
    <Card>
      <CardHeader>
        <CardTitle>L<PERSON>ch sử import</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Tên file</TableHead>
              <TableHead>Người import</TableHead>
              <TableHead>Thời gian</TableHead>
              <TableHead>Trạng thái</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {importHistory.map((history) => (
              <TableRow key={history.id}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <FileSpreadsheet className="h-4 w-4 text-green-600" />
                    <span className="font-medium">{history.fileName}</span>
                  </div>
                </TableCell>
                <TableCell>{history.importedBy}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>{history.timestamp}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    variant={
                      history.status === "completed" ? "default" : "destructive"
                    }
                    className={
                      history.status === "completed" ? "bg-green-500" : ""
                    }
                  >
                    {history.status === "completed" ? "Thành công" : "Thất bại"}
                  </Badge>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
