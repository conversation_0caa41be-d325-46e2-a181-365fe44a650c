import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { User, UserStatus } from "@/types/users";

interface StatusChangeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId?: string;
  user?: User;
}

export function StatusChangeDialog({
  open,
  onOpenChange,
  userId,
  user,
}: StatusChangeDialogProps) {
  const [userStatus, setUserStatus] = useState<UserStatus>(
    user?.status as UserStatus,
  );

  // Action handlers for dialogs
  const handleStatusChange = (userId: number, newStatus: string) => {
    console.log(`Changing status for user ${userId} to ${newStatus}`);
    // TODO: Implement actual status change logic
    onOpenChange(false);
  };

  // Handle form submission
  const handleConfirm = () => {
    if (user?.id && userStatus) {
      handleStatusChange(user.id, userStatus);
    }
    onOpenChange(false);
  };

  useEffect(() => {
    if (user?.status) {
      setUserStatus(user.status as UserStatus);
    }
  }, [user]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Đổi trạng thái</DialogTitle>
          <DialogDescription>
            Thay đổi trạng thái cho {user?.name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-3">
            {/* Active status option */}
            <div className="flex items-start space-x-3">
              <input
                type="radio"
                id="active"
                name="status"
                value="active"
                checked={userStatus === "active"}
                onChange={(e) =>
                  setUserStatus(e.target.value as "active" | "inactive")
                }
                className="mt-1"
              />
              <div className="flex-1">
                <label
                  htmlFor="active"
                  className="cursor-pointer font-medium text-sm"
                >
                  Hoạt động
                </label>
                <p className="text-muted-foreground text-sm">
                  Nhân viên có thể đăng nhập và tham gia học.
                </p>
              </div>
            </div>

            {/* Inactive status option */}
            <div className="flex items-start space-x-3">
              <input
                type="radio"
                id="inactive"
                name="status"
                value="inactive"
                checked={userStatus === "inactive"}
                onChange={(e) =>
                  setUserStatus(e.target.value as "active" | "inactive")
                }
                className="mt-1"
              />
              <div className="flex-1">
                <label
                  htmlFor="inactive"
                  className="cursor-pointer font-medium text-sm"
                >
                  Không hoạt động
                </label>
                <p className="text-muted-foreground text-sm">
                  Nhân viên không thể đăng nhập, nhưng dữ liệu vẫn được giữ lại.
                </p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
          <Button onClick={handleConfirm}>Lưu</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
