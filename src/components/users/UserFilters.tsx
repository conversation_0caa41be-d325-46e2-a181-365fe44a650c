import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { UserFiltersState, UserFilterType } from "@/types/users";

interface UserFiltersProps {
  filters: UserFiltersState;
  departments: string[];
  onFilterChange: (type: UserFilterType, value: string) => void;
}

export function UserFilters({
  filters,
  departments,
  onFilterChange,
}: UserFiltersProps) {
  const handleFilterChange = (type: UserFilterType, value: string) => {
    onFilterChange(type, value);
  };
  return (
    <div className="flex items-center gap-4">
      {/* Search input */}
      <div className="relative">
        <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 transform text-gray-400" />
        <Input
          placeholder="Tì<PERSON> kiếm theo tên hoặc email..."
          value={filters.searchTerm}
          onChange={(e) => handleFilterChange("searchTerm", e.target.value)}
          className="w-80 pl-10"
        />
      </div>

      {/* Role filter */}
      <Select
        value={filters.filterRole}
        onValueChange={(value) => handleFilterChange("filterRole", value)}
      >
        <SelectTrigger className="w-48">
          <SelectValue placeholder="Lọc theo vai trò" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tất cả vai trò</SelectItem>
          <SelectItem value="student">Học viên</SelectItem>
          <SelectItem value="instructor">Người tạo khóa</SelectItem>
        </SelectContent>
      </Select>

      {/* Status filter */}
      <Select
        value={filters.filterStatus}
        onValueChange={(value) => handleFilterChange("filterStatus", value)}
      >
        <SelectTrigger className="w-48">
          <SelectValue placeholder="Lọc theo trạng thái" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tất cả trạng thái</SelectItem>
          <SelectItem value="active">Hoạt động</SelectItem>
          <SelectItem value="inactive">Không hoạt động</SelectItem>
        </SelectContent>
      </Select>

      {/* Department filter */}
      <Select
        value={filters.filterDepartment}
        onValueChange={(value) => handleFilterChange("filterDepartment", value)}
      >
        <SelectTrigger className="w-48">
          <SelectValue placeholder="Lọc theo phòng ban" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tất cả phòng ban</SelectItem>
          {departments.map((dept) => (
            <SelectItem key={dept} value={dept}>
              {dept}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
