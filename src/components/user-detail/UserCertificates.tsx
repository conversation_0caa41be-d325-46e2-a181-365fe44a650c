import { Award, GraduationCap } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import type { UserCertificate } from "@/types/users";

interface UserCertificatesProps {
  certificates: UserCertificate[];
}

export function UserCertificates({ certificates }: UserCertificatesProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Chứng chỉ đạt được</CardTitle>
        <CardDescription>
          Danh sách các chứng chỉ đã hoàn thành và đạt được
        </CardDescription>
      </CardHeader>
      <CardContent>
        {certificates.length > 0 ? (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {certificates.map((cert) => (
              <div key={cert.id} className="space-y-3 rounded-lg border p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <Award className="h-5 w-5 text-yellow-600" />
                    <div>
                      <h4 className="font-medium">{cert.name}</h4>
                      <p className="text-muted-foreground text-sm">
                        {cert.course}
                      </p>
                    </div>
                  </div>
                  <Badge
                    variant="outline"
                    className="border-yellow-600 text-yellow-600"
                  >
                    {cert.score} điểm
                  </Badge>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Ngày cấp</p>
                    <p className="font-medium">{cert.issueDate}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Có hiệu lực đến</p>
                    <p className="font-medium">{cert.validUntil}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="py-8 text-center text-muted-foreground">
            <GraduationCap className="mx-auto mb-4 h-12 w-12 opacity-50" />
            <p>Chưa có chứng chỉ nào được cấp</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
