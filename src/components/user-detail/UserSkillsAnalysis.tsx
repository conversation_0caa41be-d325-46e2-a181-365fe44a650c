import {
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
  Tooltip,
} from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import type { SkillData } from "@/types/users";

interface UserSkillsAnalysisProps {
  skillData: SkillData[];
}

export function UserSkillsAnalysis({ skillData }: UserSkillsAnalysisProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Phân tích kỹ năng chi tiết</CardTitle>
        <CardDescription>
          Đ<PERSON>h giá năng lực theo từng lĩnh vực dựa trên kết quả quiz và bài kiểm
          tra
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <div className="flex justify-center">
            <div className="h-[400px] w-full max-w-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <RadarChart data={skillData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="skill" fontSize={12} />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} fontSize={10} />
                  <Radar
                    name="Kỹ năng"
                    dataKey="value"
                    stroke="#06b6d4"
                    fill="#06b6d4"
                    fillOpacity={0.3}
                    strokeWidth={2}
                  />
                  <Tooltip
                    formatter={(value) => [`${value} điểm`, "Kỹ năng"]}
                  />
                </RadarChart>
              </ResponsiveContainer>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="font-semibold">Đề xuất cải thiện</h4>
            <div className="space-y-3">
              <div className="border-amber-500 border-l-4 pl-4">
                <h5 className="font-medium">Backend Development</h5>
                <p className="text-muted-foreground text-sm">
                  Nên tham gia khóa học "Node.js Fundamentals" và "Database
                  Design Patterns"
                </p>
              </div>
              <div className="border-blue-500 border-l-4 pl-4">
                <h5 className="font-medium">System Architecture</h5>
                <p className="text-muted-foreground text-sm">
                  Khuyến nghị học "Microservices Architecture" và "Cloud
                  Computing Basics"
                </p>
              </div>
              <div className="border-green-500 border-l-4 pl-4">
                <h5 className="font-medium">Điểm mạnh</h5>
                <p className="text-muted-foreground text-sm">
                  Tiếp tục phát triển kỹ năng React và Frontend để trở thành
                  chuyên gia
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
