import { Activity, Building, Calendar, Mail } from "lucide-react";
import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { User } from "@/types/users";
import { getRoleDisplay } from "@/utils/get-role-display";
import UserDeleteDialog from "./UserDeleteDialog";
import UserEditInfoDialog from "./UserEditInfoDialog";

interface UserProfileCardProps {
  userData: User;
}

export function UserProfileCard({ userData }: UserProfileCardProps) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const roleInfo = getRoleDisplay(userData.role);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Thông tin cá nhân</CardTitle>
          <div className="flex items-center gap-2">
            <UserEditInfoDialog
              isDialogOpen={isEditDialogOpen}
              setIsDialogOpen={setIsEditDialogOpen}
              userData={userData}
            />
            <UserDeleteDialog
              isDialogOpen={isDeleteDialogOpen}
              setIsDialogOpen={setIsDeleteDialogOpen}
              userData={userData}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-start gap-6">
          <Avatar className="h-20 w-20">
            <AvatarImage src={userData.avatar || "/placeholder.svg"} />
            <AvatarFallback className="text-lg">
              {userData.name
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 space-y-4">
            <div>
              <h2 className="font-bold text-2xl">{userData.name}</h2>
              <Badge className={`${roleInfo.color} border-0`}>
                {roleInfo.label}
              </Badge>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{userData.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <Building className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{userData.department}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Tham gia: {userData.joinDate}</span>
              </div>
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  Hoạt động: {userData.lastActivity}
                </span>
              </div>
            </div>
          </div>
          <Badge
            variant={userData.status === "active" ? "default" : "secondary"}
          >
            {userData.status === "active"
              ? "Đang hoạt động"
              : "Không hoạt động"}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
