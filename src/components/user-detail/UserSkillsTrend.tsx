import { Target } from "lucide-react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface UserSkillsTrendProps {
  averageScore: number;
}

export function UserSkillsTrend({ averageScore }: UserSkillsTrendProps) {
  const trendData = [
    { month: "Tháng 1", react: 70, javascript: 65, design: 60, backend: 45 },
    { month: "Tháng 2", react: 80, javascript: 75, design: 65, backend: 50 },
    {
      month: "Tháng 3",
      react: averageScore + 10,
      javascript: averageScore,
      design: averageScore - 10,
      backend: averageScore - 20,
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5" />
          <PERSON> hướng phát triển kỹ năng
        </CardTitle>
        <CardDescription>
          Tiến bộ của các kỹ năng theo thời gian
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={trendData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="react" fill="#10b981" radius={[2, 2, 0, 0]} />
              <Bar dataKey="javascript" fill="#3b82f6" radius={[2, 2, 0, 0]} />
              <Bar dataKey="design" fill="#8b5cf6" radius={[2, 2, 0, 0]} />
              <Bar dataKey="backend" fill="#f59e0b" radius={[2, 2, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
