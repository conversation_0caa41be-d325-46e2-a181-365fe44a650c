import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import type { UserCourse } from "@/types/users";

interface UserCoursesTableProps {
  courses: UserCourse[];
}

export function UserCoursesTable({ courses }: UserCoursesTableProps) {
  // Helper functions for status display
  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "in-progress":
        return "bg-blue-100 text-blue-800";
      case "not-started":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "<PERSON>àn thành";
      case "in-progress":
        return "Đang học";
      case "not-started":
        return "Chưa bắt đầu";
      default:
        return status;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle><PERSON>h sách tất cả các khóa được phân công</CardTitle>
        <CardDescription>
          Chi tiết về trạng thái, điểm số, XP và thời gian của từng khóa học
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Khóa học</TableHead>
              <TableHead>Trạng thái</TableHead>
              <TableHead>Tiến độ</TableHead>
              <TableHead>Điểm/XP</TableHead>
              <TableHead>Ngày phân công</TableHead>
              <TableHead>Ngày hoàn thành</TableHead>
              <TableHead>Thời gian học</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {courses.map((course) => (
              <TableRow key={course.id}>
                <TableCell>
                  <div className="font-medium">{course.title}</div>
                  <div className="text-muted-foreground text-sm">
                    {course.category}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge className={getStatusColor(course.status)}>
                    {getStatusText(course.status)}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-16 rounded-full bg-gray-200">
                      <div
                        className="h-2 rounded-full bg-cyan-600"
                        style={{ width: `${course.progress}%` }}
                      ></div>
                    </div>
                    <span className="font-medium text-sm">
                      {course.progress}%
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="font-medium">
                      {(course.score || 0) > 0 ? `${course.score} điểm` : "-"}
                    </div>
                    <div className="text-purple-600 text-sm">
                      {course.xp} XP
                    </div>
                  </div>
                </TableCell>
                <TableCell className="text-muted-foreground">
                  {course.enrollDate}
                </TableCell>
                <TableCell className="text-muted-foreground">
                  {course.completedDate || "-"}
                </TableCell>
                <TableCell className="text-muted-foreground">
                  {course.timeSpent}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
