import { Award, Clock, GraduationCap, TrendingUp } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

interface UserStatsCardsProps {
  totalCompletedCourses: number;
  totalXP: number;
  totalStudyTime: number;
  totalCertificates: number;
}

export function UserStatsCards({
  totalCompletedCourses,
  totalXP,
  totalStudyTime,
  totalCertificates,
}: UserStatsCardsProps) {
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
      {/* Completed Courses */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-2">
            <Award className="h-5 w-5 text-green-600" />
            <div>
              <p className="font-bold text-2xl">{totalCompletedCourses}</p>
              <p className="text-muted-foreground text-sm">
                <PERSON><PERSON><PERSON><PERSON> học hoàn thành
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Total XP */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-purple-600" />
            <div>
              <p className="font-bold text-2xl">{totalXP}</p>
              <p className="text-muted-foreground text-sm">Tổng XP tích lũy</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Study Time */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-amber-600" />
            <div>
              <p className="font-bold text-2xl">{totalStudyTime}h</p>
              <p className="text-muted-foreground text-sm">
                Thời gian học tích lũy
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Certificates */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-2">
            <GraduationCap className="h-5 w-5 text-cyan-600" />
            <div>
              <p className="font-bold text-2xl">{totalCertificates}</p>
              <p className="text-muted-foreground text-sm">
                Chứng chỉ đạt được
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
