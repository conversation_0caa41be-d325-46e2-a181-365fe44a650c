import { Alert<PERSON>ircle, CheckCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface UserSkillsOverviewProps {
  averageScore: number;
}

export function UserSkillsOverview({ averageScore }: UserSkillsOverviewProps) {
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      {/* Strengths */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-600">
            <CheckCircle className="h-5 w-5" />
            Điểm mạnh
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span>React Development</span>
            <Badge className="bg-green-100 text-green-800">
              {averageScore + 10} điểm
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span>Frontend Development</span>
            <Badge className="bg-green-100 text-green-800">
              {averageScore + 5} điểm
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span>TypeScript</span>
            <Badge className="bg-green-100 text-green-800">
              {averageScore} điểm
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Areas for Improvement */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-amber-600">
            <AlertCircle className="h-5 w-5" />
            Cần cải thiện
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span>Backend Development</span>
            <Badge className="bg-amber-100 text-amber-800">
              {averageScore - 15} điểm
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span>Database Design</span>
            <Badge className="bg-amber-100 text-amber-800">
              {averageScore - 20} điểm
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span>System Architecture</span>
            <Badge className="bg-amber-100 text-amber-800">
              {averageScore - 25} điểm
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
