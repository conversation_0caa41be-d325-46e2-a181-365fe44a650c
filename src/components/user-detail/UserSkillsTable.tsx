import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface UserSkillsTableProps {
  averageScore: number;
}

export function UserSkillsTable({ averageScore }: UserSkillsTableProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Chi tiết điểm quiz theo kỹ năng</CardTitle>
        <CardDescription>
          Kết quả các bài kiểm tra và quiz được phân loại theo kỹ năng
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Kỹ năng</TableHead>
              <TableHead>Khóa học</TableHead>
              <TableHead>Loại bài kiểm tra</TableHead>
              <TableHead><PERSON><PERSON><PERSON><PERSON> số</TableHead>
              <TableHead><PERSON><PERSON><PERSON> làm bài</TableHead>
              <TableHead>Trạng thái</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell className="font-medium">React Development</TableCell>
              <TableCell>React Fundamentals</TableCell>
              <TableCell>Quiz cuối khóa</TableCell>
              <TableCell>
                <Badge className="bg-green-100 text-green-800">
                  {averageScore + 10}/100
                </Badge>
              </TableCell>
              <TableCell>2024-03-15</TableCell>
              <TableCell>
                <Badge className="bg-green-100 text-green-800">Xuất sắc</Badge>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">JavaScript</TableCell>
              <TableCell>JavaScript Mastery</TableCell>
              <TableCell>Bài kiểm tra giữa kỳ</TableCell>
              <TableCell>
                <Badge className="bg-blue-100 text-blue-800">
                  {averageScore}/100
                </Badge>
              </TableCell>
              <TableCell>2024-03-10</TableCell>
              <TableCell>
                <Badge className="bg-blue-100 text-blue-800">Tốt</Badge>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">UI/UX Design</TableCell>
              <TableCell>UI/UX Design Basics</TableCell>
              <TableCell>Dự án thực hành</TableCell>
              <TableCell>
                <Badge className="bg-amber-100 text-amber-800">
                  {averageScore - 10}/100
                </Badge>
              </TableCell>
              <TableCell>2024-03-25</TableCell>
              <TableCell>
                <Badge className="bg-amber-100 text-amber-800">Khá</Badge>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">Backend Development</TableCell>
              <TableCell>Node.js Basics</TableCell>
              <TableCell>Quiz đánh giá</TableCell>
              <TableCell>
                <Badge className="bg-red-100 text-red-800">
                  {averageScore - 20}/100
                </Badge>
              </TableCell>
              <TableCell>2024-03-20</TableCell>
              <TableCell>
                <Badge className="bg-red-100 text-red-800">Cần cải thiện</Badge>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
