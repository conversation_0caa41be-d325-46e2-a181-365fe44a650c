import { Al<PERSON><PERSON>ir<PERSON>, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { User } from "@/types/users";

type Props = {
  isDialogOpen: boolean;
  setIsDialogOpen: (isOpen: boolean) => void;
  userData: User;
};

const UserDeleteDialog = ({
  isDialogOpen,
  setIsDialogOpen,
  userData,
}: Props) => {
  const onUserDelete = () => {
    console.log("onUserDelete");
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="cursor-pointer border-red-200 bg-transparent text-red-600 hover:border-red-300 hover:text-red-700"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Xóa
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Xác nhận xóa người dùng</DialogTitle>
          <DialogDescription>
            Bạn có chắc chắn muốn xóa người dùng này? Hành động này không thể
            hoàn tác.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">Cảnh báo</span>
            </div>
            <p className="mt-2 text-red-700 text-sm">Việc xóa người dùng sẽ:</p>
            <ul className="mt-1 ml-4 list-disc text-red-700 text-sm">
              <li>Xóa vĩnh viễn tất cả dữ liệu học tập</li>
              <li>Hủy tất cả khóa học đang tham gia</li>
              <li>Xóa lịch sử hoạt động và chứng chỉ</li>
            </ul>
          </div>
          <div className="space-y-2">
            <Label>Để xác nhận, vui lòng nhập tên người dùng:</Label>
            <Input placeholder={userData.name} />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
            Hủy
          </Button>
          <Button variant="destructive" onClick={onUserDelete}>
            Xóa người dùng
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UserDeleteDialog;
