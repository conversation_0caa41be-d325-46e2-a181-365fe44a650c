import { Edit } from "lucide-react";
import { AlertCircle, Camera, CheckCircle, Upload } from "lucide-react";
import React, { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { User } from "@/types/users";

type Props = {
  isDialogOpen: boolean;
  setIsDialogOpen: (isOpen: boolean) => void;
  userData: User;
};

const UserEditInfoDialog = ({
  isDialogOpen,
  setIsDialogOpen,
  userData,
}: Props) => {
  const [showPasswordFields, setShowPasswordFields] = useState(false);

  const [editForm, setEditForm] = useState({
    name: userData.name,
    email: userData.email,
    role: userData.role,
    department: userData.department,
    phone: userData.phone || "",
    location: userData.location || "",
    avatar: userData.avatar,
    status: userData.status,
    newPassword: "",
    confirmPassword: "",
    sendEmail: false,
  });

  const onUserUpdate = (updatedUser: User) => {
    console.log("updatedUser", updatedUser);
  };

  // Handle form save - updates user data
  const handleSaveChanges = () => {
    onUserUpdate({ ...userData, ...editForm });
    setIsDialogOpen(false);
    setShowPasswordFields(false);
  };
  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="cursor-pointer">
          <Edit className="mr-2 h-4 w-4" />
          Chỉnh sửa
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-[90vh] max-w-3xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Chỉnh sửa thông tin người dùng</DialogTitle>
          <DialogDescription>
            Cập nhật thông tin và cài đặt của người dùng
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6">
          {/* Avatar Upload */}
          <div className="flex items-center gap-4">
            <Avatar className="h-20 w-20">
              <AvatarImage src={editForm.avatar || "/placeholder.svg"} />
              <AvatarFallback className="text-lg">
                {editForm.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-2">
              <Label>Ảnh đại diện</Label>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Upload className="mr-2 h-4 w-4" />
                  Tải lên
                </Button>
                <Button variant="outline" size="sm">
                  <Camera className="mr-2 h-4 w-4" />
                  Chụp ảnh
                </Button>
              </div>
            </div>
          </div>

          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="font-medium text-lg">Thông tin cơ bản</h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">Họ và tên *</Label>
                <Input
                  id="name"
                  value={editForm.name}
                  onChange={(e) =>
                    setEditForm({ ...editForm, name: e.target.value })
                  }
                  placeholder="Nhập họ và tên"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={editForm.email}
                  onChange={(e) =>
                    setEditForm({ ...editForm, email: e.target.value })
                  }
                  placeholder="Nhập email"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Số điện thoại</Label>
                <Input
                  id="phone"
                  value={editForm.phone}
                  onChange={(e) =>
                    setEditForm({ ...editForm, phone: e.target.value })
                  }
                  placeholder="Nhập số điện thoại"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location">Địa điểm làm việc</Label>
                <select
                  id="location"
                  value={editForm.location}
                  onChange={(e) =>
                    setEditForm({
                      ...editForm,
                      location: e.target.value,
                    })
                  }
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="Hà Nội">Hà Nội</option>
                  <option value="TP.HCM">TP.HCM</option>
                  <option value="Đà Nẵng">Đà Nẵng</option>
                  <option value="Remote">Remote</option>
                </select>
              </div>
            </div>
          </div>

          {/* Role and Department */}
          <div className="space-y-4">
            <h3 className="font-medium text-lg">Vai trò và phòng ban</h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="role">Vai trò *</Label>
                <select
                  id="role"
                  value={editForm.role}
                  onChange={(e) =>
                    setEditForm({ ...editForm, role: e.target.value })
                  }
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="Developer">Developer</option>
                  <option value="Senior Developer">Senior Developer</option>
                  <option value="Marketing Manager">Marketing Manager</option>
                  <option value="HR Specialist">HR Specialist</option>
                  <option value="Sales Executive">Sales Executive</option>
                  <option value="Content Creator">Content Creator</option>
                  <option value="DevOps Engineer">DevOps Engineer</option>
                  <option value="Admin">Admin</option>
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="department">Phòng ban *</Label>
                <select
                  id="department"
                  value={editForm.department}
                  onChange={(e) =>
                    setEditForm({
                      ...editForm,
                      department: e.target.value,
                    })
                  }
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="Kỹ thuật">Kỹ thuật</option>
                  <option value="Marketing">Marketing</option>
                  <option value="Nhân sự">Nhân sự</option>
                  <option value="Bán hàng">Bán hàng</option>
                  <option value="Tài chính">Tài chính</option>
                </select>
              </div>
            </div>
          </div>

          {/* Account Status */}
          <div className="space-y-4">
            <h3 className="font-medium text-lg">Trạng thái tài khoản</h3>
            <div className="space-y-3">
              <Label>Trạng thái *</Label>
              <div className="flex gap-6">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="status-active"
                    name="status"
                    value="active"
                    checked={editForm.status === "active"}
                    onChange={(e) =>
                      setEditForm({
                        ...editForm,
                        status: e.target.value as "active" | "inactive",
                      })
                    }
                    className="h-4 w-4 border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <Label
                    htmlFor="status-active"
                    className="flex cursor-pointer items-center gap-2"
                  >
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Kích hoạt
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="status-inactive"
                    name="status"
                    value="inactive"
                    checked={editForm.status === "inactive"}
                    onChange={(e) =>
                      setEditForm({
                        ...editForm,
                        status: e.target.value as "active" | "inactive",
                      })
                    }
                    className="h-4 w-4 border-gray-300 text-red-600 focus:ring-red-500"
                  />
                  <Label
                    htmlFor="status-inactive"
                    className="flex cursor-pointer items-center gap-2"
                  >
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    Vô hiệu hóa
                  </Label>
                </div>
              </div>
            </div>
          </div>

          {/* Password Change Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="changePassword"
                checked={showPasswordFields}
                onChange={(e) => setShowPasswordFields(e.target.checked)}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <Label
                htmlFor="changePassword"
                className="cursor-pointer font-medium text-lg"
              >
                Đổi mật khẩu
              </Label>
            </div>

            {showPasswordFields && (
              <div className="space-y-4 border-blue-200 border-l-2 pl-6">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">Mật khẩu mới *</Label>
                    <Input
                      id="newPassword"
                      type="password"
                      value={editForm.newPassword || ""}
                      onChange={(e) =>
                        setEditForm({
                          ...editForm,
                          newPassword: e.target.value,
                        })
                      }
                      placeholder="Nhập mật khẩu mới"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Xác nhận mật khẩu *</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={editForm.confirmPassword || ""}
                      onChange={(e) =>
                        setEditForm({
                          ...editForm,
                          confirmPassword: e.target.value,
                        })
                      }
                      placeholder="Nhập lại mật khẩu mới"
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="sendEmail"
                    checked={editForm.sendEmail || false}
                    onChange={(e) =>
                      setEditForm({
                        ...editForm,
                        sendEmail: e.target.checked,
                      })
                    }
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <Label htmlFor="sendEmail" className="cursor-pointer">
                    Gửi email thông báo cho người dùng
                  </Label>
                </div>
              </div>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
            Hủy
          </Button>
          <Button onClick={handleSaveChanges}>Lưu tất cả thay đổi</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UserEditInfoDialog;
