import { ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "./button";

interface SortableHeaderProps {
  children: React.ReactNode;
  sortDirection: "asc" | "desc" | "none";
  onSort?: () => void;
  className?: string;
  sortable?: boolean;
}

export function SortableHeader({
  children,
  sortDirection,
  onSort,
  className,
  sortable = true,
}: SortableHeaderProps) {
  if (!sortable) {
    return <div className={className}>{children}</div>;
  }

  const SortIcon = () => {
    if (sortDirection === "asc") {
      return <ChevronUp className="ml-1 h-3 w-3" />;
    }
    if (sortDirection === "desc") {
      return <ChevronDown className="ml-1 h-3 w-3" />;
    }
    return (
      <ChevronDown className="ml-1 h-3 w-3 opacity-0 group-hover:opacity-50" />
    );
  };

  return (
    <Button
      variant="ghost"
      onClick={onSort}
      className={cn(
        "group flex h-auto w-full justify-start p-0 font-semibold text-gray-600 text-xs uppercase tracking-wider hover:bg-transparent hover:text-gray-900",
        className
      )}
    >
      {children}
      <SortIcon />
    </Button>
  );
}

// For shadcn/ui Table component headers
export function SortableTableHead({
  children,
  sortDirection,
  onSort,
  className,
  sortable = true,
}: SortableHeaderProps) {
  if (!sortable) {
    return <th className={className}>{children}</th>;
  }

  const SortIcon = () => {
    if (sortDirection === "asc") {
      return <ChevronUp className="ml-1 h-4 w-4" />;
    }
    if (sortDirection === "desc") {
      return <ChevronDown className="ml-1 h-4 w-4" />;
    }
    return (
      <ChevronDown className="ml-1 h-4 w-4 opacity-0 group-hover:opacity-50" />
    );
  };

  return (
    <th className={className}>
      <Button
        variant="ghost"
        onClick={onSort}
        className="group flex min-h-[40px] w-full justify-start p-0 font-medium hover:bg-transparent hover:text-foreground"
      >
        {children}
        <SortIcon />
      </Button>
    </th>
  );
}
