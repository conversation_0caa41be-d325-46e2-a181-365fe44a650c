import { cn } from '@/lib/utils';

function Skeleton({ className, ...props }: React.ComponentProps<'div'>) {
  return <div data-slot="skeleton" className={cn('animate-pulse rounded-md bg-accent', className)} {...props} />;
}

function SkeletonLine({ width = 'w-full' }: { width?: string }) {
  return <Skeleton className={`h-4 ${width}`} />;
}

function WikiSidebarSkeleton() {
  return (
    <div className="h-full w-full space-y-4 overflow-x-hidden">
      <SkeletonLine width="w-full h-10 mb-8" />
      <SkeletonLine width="w-full h-6" />
      <SkeletonLine width="w-full ml-4" />
      <SkeletonLine width="w-full ml-4" />
      <SkeletonLine width="w-full ml-4" />
      <SkeletonLine width="w-full h-6" />
      <SkeletonLine width="w-full ml-4" />
      <SkeletonLine width="w-full ml-4" />
      <SkeletonLine width="w-full h-6" />
      <SkeletonLine width="w-full ml-4" />
    </div>
  );
}

export { Skeleton, SkeletonLine, WikiSidebarSkeleton };
