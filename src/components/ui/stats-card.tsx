import { ReactNode } from "react";
import { Badge } from "./badge";
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardFooter, CardDescription, CardTitle, CardAction } from "./card";

interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  badge?: {
    variant?: "default" | "secondary" | "destructive" | "outline";
    icon?: ReactNode;
    text: string;
  };
  footer?: {
    title: string;
    subtitle: string;
    icon?: ReactNode;
  };
  className?: string;
}

export function StatsCard({
  title,
  value,
  description,
  badge,
  footer,
  className,
}: StatsCardProps) {
  return (
    <Card className={`@container/card ${className || ""}`}>
      <CardHeader>
        <CardDescription>{title}</CardDescription>
        <CardTitle className="font-semibold @[250px]/card:text-3xl text-2xl tabular-nums">
          {value}
        </CardTitle>
        {badge && (
          <CardAction>
            <Badge variant={badge.variant || "outline"}>
              {badge.icon}
              {badge.text}
            </Badge>
          </CardAction>
        )}
      </CardHeader>
      {footer && (
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {footer.title}
            {footer.icon && footer.icon}
          </div>
          {footer.subtitle && (
            <div className="text-muted-foreground">{footer.subtitle}</div>
          )}
        </CardFooter>
      )}
    </Card>
  );
}

// Preset variants for common use cases
export function RevenueStatsCard({
  value,
  change,
  changeIcon,
  period = "this month",
  subtitle = "Visitors for the last 6 months",
  className,
}: {
  value: string | number;
  change: string;
  changeIcon?: ReactNode;
  period?: string;
  subtitle?: string;
  className?: string;
}) {
  return (
    <StatsCard
      title="Total Revenue"
      value={value}
      badge={{
        variant: "outline",
        icon: changeIcon,
        text: change,
      }}
      footer={{
        title: `Trending up ${period}`,
        subtitle,
        icon: changeIcon,
      }}
      className={className}
    />
  );
}

export function UserStatsCard({
  value,
  change,
  changeIcon,
  period = "this month",
  subtitle = "Active users in the last 30 days",
  className,
}: {
  value: string | number;
  change: string;
  changeIcon?: ReactNode;
  period?: string;
  subtitle?: string;
  className?: string;
}) {
  return (
    <StatsCard
      title="Total Users"
      value={value}
      badge={{
        variant: "outline",
        icon: changeIcon,
        text: change,
      }}
      footer={{
        title: `Growing ${period}`,
        subtitle,
        icon: changeIcon,
      }}
      className={className}
    />
  );
}

export function CourseStatsCard({
  value,
  change,
  changeIcon,
  period = "this month",
  subtitle = "Courses completed in the last 30 days",
  className,
}: {
  value: string | number;
  change: string;
  changeIcon?: ReactNode;
  period?: string;
  subtitle?: string;
  className?: string;
}) {
  return (
    <StatsCard
      title="Total Courses"
      value={value}
      badge={{
        variant: "outline",
        icon: changeIcon,
        text: change,
      }}
      footer={{
        title: `Completion rate up ${period}`,
        subtitle,
        icon: changeIcon,
      }}
      className={className}
    />
  );
}