import { createFormHook, createF<PERSON><PERSON>ookContexts } from "@tanstack/react-form";
import type { PropsWithChildren } from "react";
import { cn } from "@/utils/cn";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";

const { fieldContext, formContext, useFieldContext, useFormContext } =
  createFormHookContexts();
const { useAppForm, withForm } = createFormHook({
  fieldComponents: {
    TextField,
    TextareaField,
    FileField,
    FieldMessage,
  },
  formComponents: {
    SubmitButton: SubmitButton,
  },
  fieldContext,
  formContext,
});

function SubmitButton({
  children,
  onlyDirty,
  ...props
}: PropsWithChildren &
  React.ComponentProps<typeof Button> & {
    onlyDirty?: boolean;
  }) {
  const form = useFormContext();

  return (
    <form.Subscribe
      selector={(state) => [state.canSubmit, state.isDirty]}
      children={([canSubmit, isDirty]) => (
        <Button
          type="submit"
          disabled={!canSubmit || (onlyDirty && !isDirty)}
          {...props}
        >
          {children}
        </Button>
      )}
    />
  );
}

function TextField(props: React.ComponentProps<"input">) {
  const field = useFieldContext<string>();
  return (
    <Input
      value={field.state.value}
      onChange={(e) => field.handleChange(e.target.value)}
      {...props}
    />
  );
}

function TextareaField(props: React.ComponentProps<"textarea">) {
  const field = useFieldContext<string>();
  return (
    <Textarea
      value={field.state.value}
      onChange={(e) => field.handleChange(e.target.value)}
      {...props}
    />
  );
}

function FileField(
  props: React.ComponentProps<"input"> & {
    onFileChange?: (file: File | undefined) => void;
  },
) {
  const field = useFieldContext<File | undefined>();
  const { onChange, ...rest } = props;
  return (
    <Input
      type="file"
      onChange={(e) => {
        props.onChange?.(e);
        field.handleChange(e.target.files?.[0]);
      }}
      {...rest}
    />
  );
}

export function FieldMessage({
  className,
  ...props
}: React.ComponentProps<"p">) {
  const field = useFieldContext<string>();
  return field.state.meta.errors.map((error) => (
    <p
      key={error.message}
      className={cn("text-destructive text-sm", className)}
      {...props}
    >
      {error.message}
    </p>
  ));
}

export { useAppForm, withForm };
