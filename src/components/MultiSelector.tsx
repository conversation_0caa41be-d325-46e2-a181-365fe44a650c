import React from "react";
import Select from "react-select";
import makeAnimated from "react-select/animated";

const animatedComponents = makeAnimated();

export default function MultiSelector(props: {
  options: { value: string; label: string }[];
  selectedValues: { value: string; label: string }[];
  onSelect: (selected: { value: string; label: string }[]) => void;
  placeholder?: string;
}) {
  const { options, selectedValues, onSelect, placeholder } = props;

  return (
    <div>
      <Select
        options={options}
        components={animatedComponents}
        defaultValue={selectedValues}
        isMulti
        placeholder={placeholder ? placeholder : "Select..."}
        onChange={onSelect}
      />
    </div>
  );
}
