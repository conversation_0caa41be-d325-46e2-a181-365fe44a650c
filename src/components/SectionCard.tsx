import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Section } from "@/types/lessons";
import AIGeneratedEditor from "./AIGeneratedEditor";
import GameSection from "./GameSection";
import BeautyEditor from "./lesson-editor/tiptap-editor";
import QuizSection from "./QuizSection";

interface SectionCardProps {
  sections: Section[];
  selectedSection: number;
  setSelectedSection: (index: number) => void;
  updateSection: (sections: Section[], index: number) => void;
}

export default function SectionCard({
  sections,
  selectedSection,
  setSelectedSection,
  updateSection,
}: SectionCardProps) {
  const [api, setApi] = useState<CarouselApi>();

  const updateContent = (e: unknown, index: number) => {
    let sectionTmp = [...sections];
    sectionTmp[index].content = JSON.stringify(e);
    updateSection(sectionTmp, index);
  };

  // Sync carousel with selected section
  useEffect(() => {
    console.log("🎠 SectionCard: selectedSection changed to:", selectedSection);
    console.log("🎠 SectionCard: sections.length:", sections.length);
    console.log("🎠 SectionCard: api available:", !!api);

    if (
      !api ||
      typeof selectedSection !== "number" ||
      selectedSection < 0 ||
      selectedSection >= sections.length
    ) {
      console.log("🎠 SectionCard: Early return - conditions not met");
      return;
    }

    console.log("🎠 SectionCard: Scrolling to section:", selectedSection);
    api.scrollTo(selectedSection);
  }, [api, selectedSection, sections]);

  // Sync selectedSection when carousel changes
  useEffect(() => {
    if (!api) return;

    const onSelect = () => {
      const currentIndex = api.selectedScrollSnap();
      if (currentIndex !== selectedSection) {
        setSelectedSection(currentIndex);
      }
    };

    api.on("select", onSelect);
    return () => {
      api.off("select", onSelect);
    };
  }, [api, selectedSection, setSelectedSection]);

  return (
    <div className="flex h-full min-h-full w-full">
      <div className="w-full rounded-md rounded-s-none p-0">
        <div className="w-full px-5">
          <div className="relative w-full">
            <Carousel opts={{ loop: false, watchDrag: false }} setApi={setApi}>
              <CarouselContent>
                {sections?.map((section, index) => (
                  <CarouselItem
                    key={section.id}
                    className="flex h-full w-full flex-col items-center overflow-scroll"
                  >
                    {section.type === "TEXT" && (
                      <div
                        id={`section-view-${section.id}`}
                        className="px-3 py-6"
                      >
                        <BeautyEditor
                          content={section.content}
                          setContent={(e) => updateContent(e, index)}
                        />
                      </div>
                    )}
                    {section.type === "GAME" && (
                      <div
                        className="w-full px-1 py-6"
                        id={`section-view-${section.id}`}
                      >
                        <GameSection section={section} />
                      </div>
                    )}
                    {section.type === "CHALLENGE" && (
                      <div className="w-full" id={`section-view-${section.id}`}>
                        <QuizSection sectionId={section.id} />
                      </div>
                    )}
                    {section.type === "AIGENERATED" && (
                      <div
                        className="h-full w-full"
                        id={`section-view-${section.id}`}
                      >
                        <AIGeneratedEditor
                          content={section.content}
                          setContent={(e) => updateContent(e, index)}
                        />
                      </div>
                    )}
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="hidden" />
              <CarouselNext className="hidden" />
            </Carousel>
          </div>
        </div>
      </div>
    </div>
  );
}
