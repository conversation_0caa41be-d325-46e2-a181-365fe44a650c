import Editor from "@monaco-editor/react";
import {
  ArrowLeftRight,
  Code,
  Eye,
  Loader,
  Save,
  Sparkles,
  Wand2,
} from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import {
  fetchJsxCode,
  GenerateCode,
  UploadJSXFile,
} from "@/services/instructor";

interface AIGeneratedEditorProps {
  content: string;
  setContent: (content: {
    prompt: string;
    model: string;
    jsx_url: string;
  }) => void;
}

export default function AIGeneratedEditor({
  content,
  setContent,
}: AIGeneratedEditorProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [prompt, setPrompt] = useState("");
  const [selectedModel, setSelectedModel] = useState("gemini-2.5-flash-lite");
  const [codeString, setCodeString] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [isUIMode, setIsUIMode] = useState(true);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (!content) return;
    try {
      const parsedContent = JSON.parse(content);
      setPrompt(parsedContent.prompt);
      if (parsedContent.jsx_url) {
        fetchJsxCode({ url: parsedContent.jsx_url }).then((e) => {
          setCodeString(e);
          setTimeout(() => sendToIframe(e), 5000);
        });
      }
      setSelectedModel(parsedContent.model);
    } catch (e) {
      console.warn(`invalid content format. recreating`);
    }
  }, [content]);

  const handleGenerate = async () => {
    try {
      setLoading(true);
      setErrorMessage("");
      const rs = await GenerateCode({ message: prompt, model: selectedModel });
      if (rs.message.toLowerCase() !== "success") {
        setErrorMessage(rs.message);
      }
      setCodeString(rs.data.react_code);
      sendToIframe(rs.data.react_code);
      setLoading(false);
    } catch (e) {
      setLoading(false);
      console.error(`Error: `, e);
    }
  };
  const adjustHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto";
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  };

  const shrinkToDefault = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto";
    }
  };

  const sendToIframe = (data: string) => {
    const dataToSend = {
      target: "aicademy-previewer",
      code: data,
    };
    if (iframeRef.current?.contentWindow) {
      const targetOrigin = new URL(iframeRef.current.src).origin;
      iframeRef.current.contentWindow.postMessage(dataToSend, targetOrigin);
    } else {
      console.error("Iframe chưa sẵn sàng để nhận dữ liệu.");
    }
  };

  const toggleMode = () => {
    if (!isUIMode) {
      sendToIframe(codeString);
    }
    setIsUIMode(!isUIMode);
  };

  const saveJsx = async () => {
    setSaving(true);
    try {
      const fileUrl = await UploadJSXFile({ jsxCode: codeString });
      setContent({
        prompt: prompt,
        model: selectedModel,
        jsx_url: fileUrl,
      });
    } catch (e) {
      toast.error("Save section failed!");
      console.error(`Error: `, e);
    }
    setSaving(false);
  };

  return (
    <div className="min-h-screen py-3">
      <Card className="mb-4">
        <CardContent className="py-4">
          <div className="flex items-start gap-4">
            <div className="relative flex-1">
              <div className="relative">
                <Textarea
                  defaultValue={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Try entering a detailed idea for AI to create an widget"
                  ref={textareaRef}
                  rows={3}
                  onFocus={adjustHeight}
                  onInput={adjustHeight}
                  onBlur={shrinkToDefault}
                  className="resize-none overflow-hidden transition-[height] duration-1000 ease-in-out"
                />
                <div className="absolute top-3 right-3">
                  <Sparkles className="h-5 w-5 text-slate-400" />
                </div>
              </div>
            </div>

            <div className="mt-1 flex gap-5">
              <Button
                onClick={handleGenerate}
                disabled={!prompt || loading}
                size="lg"
                className="h-[60px] min-w-[100px] bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
              >
                {loading ? (
                  <Loader className="h-4 w-4 animate-spin" />
                ) : (
                  <Wand2 className="h-4 w-4" />
                )}
                <span>Generate</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <div className="h-[calc(100vh-140px)] w-full">
        <div className="mb-4 flex items-center justify-between">
          <Button
            variant="outline"
            onClick={toggleMode}
            className="flex items-center gap-3"
          >
            {isUIMode ? (
              <>
                <Eye className="h-4 w-4" />
                <span>Preview mode</span>
                <ArrowLeftRight className="h-4 w-4 text-blue-500" />
              </>
            ) : (
              <>
                <Code className="h-4 w-4" />
                <span>Editor mode</span>
                <ArrowLeftRight className="h-4 w-4 text-slate-400" />
              </>
            )}
          </Button>
          <span className="text-black/70 text-sm italic">
            Tip: If the output isn't as desired, please detail your prompt
            further.
          </span>
          <Button
            disabled={saving}
            onClick={saveJsx}
            className="bg-green-600 hover:bg-green-700"
          >
            <Save className="h-4 w-4" />
            <span>{saving ? "Saving" : "Save"}</span>
          </Button>
        </div>
        {loading && <Loader className="mx-auto mt-5 h-10 w-10 animate-spin" />}
        <Card hidden={loading} className="h-[calc(100%-80px)]">
          <CardContent className="h-full p-4">
            {errorMessage && (
              <div className="rounded-lg border border-red-300 bg-red-100 p-3 text-red-700 italic">
                AI generate failed: {errorMessage}
              </div>
            )}
            {!codeString && (
              <p className="p-3 text-slate-500 italic">
                Your visualized content will be displayed here...
              </p>
            )}
            <iframe
              ref={iframeRef}
              id="reactAppIframe"
              src="https://jsx-previewer.pages.dev/"
              title="React App Iframe"
              hidden={!isUIMode || !codeString}
              className="h-full w-full rounded-md"
            />
            {!isUIMode && (
              <Editor
                className="h-full"
                height="100%"
                defaultLanguage="javascript"
                defaultValue={codeString ?? ""}
                onChange={(e) => setCodeString(e ?? "")}
                theme="vs-light"
              />
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
