import { <PERSON><PERSON><PERSON>, Edit, Plus, Users } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import type { HistoryItem } from "./types";

// Mock data for course history
const mockHistory: HistoryItem[] = [
  {
    id: 1,
    action: "Tạo khóa học",
    type: "create",
    description: "Khóa học React Fundamentals đã được tạo mới",
    user: "<PERSON>",
    role: "Instructor",
    date: "2024-01-10",
    time: "10:00 AM",
  },
  {
    id: 2,
    action: "Chỉnh sửa nội dung",
    type: "edit",
    description: "<PERSON><PERSON> cập nhật nội dung bài học Giới thiệu về React",
    user: "<PERSON>",
    role: "Editor",
    date: "2024-01-12",
    time: "02:30 PM",
  },
  {
    id: 3,
    action: "<PERSON><PERSON><PERSON> nhật phân quyền",
    type: "permission",
    description: "<PERSON><PERSON> cấp quyền truy cập cho phòng ban Kỹ thuật",
    user: "Alice Johnson",
    role: "Admin",
    date: "2024-01-15",
    time: "09:45 AM",
  },
];

interface CourseHistoryProps {
  history?: HistoryItem[];
}

export function CourseHistory({ history = mockHistory }: CourseHistoryProps) {
  const getActionIcon = (type: string) => {
    switch (type) {
      case "create":
        return Plus;
      case "edit":
        return Edit;
      case "publish":
        return BookOpen;
      case "permission":
        return Users;
      default:
        return Edit;
    }
  };

  const getActionColor = (type: string) => {
    switch (type) {
      case "create":
        return {
          bg: "bg-green-50",
          icon: "text-green-600",
          border: "border-green-200",
        };
      case "edit":
        return {
          bg: "bg-blue-50",
          icon: "text-blue-600",
          border: "border-blue-200",
        };
      case "publish":
        return {
          bg: "bg-purple-50",
          icon: "text-purple-600",
          border: "border-purple-200",
        };
      case "permission":
        return {
          bg: "bg-orange-50",
          icon: "text-orange-600",
          border: "border-orange-200",
        };
      default:
        return {
          bg: "bg-gray-50",
          icon: "text-gray-600",
          border: "border-gray-200",
        };
    }
  };

  const getBadgeVariant = (type: string) => {
    switch (type) {
      case "create":
        return "bg-green-100 text-green-800 border-green-200";
      case "edit":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "publish":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "permission":
        return "bg-orange-100 text-orange-800 border-orange-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getActionText = (type: string) => {
    switch (type) {
      case "create":
        return "Tạo mới";
      case "edit":
        return "Nội dung";
      case "publish":
        return "Xuất bản";
      case "permission":
        return "Phân quyền";
      default:
        return type;
    }
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="border-gray-100 border-b">
        <CardTitle className="font-semibold text-lg">
          Lịch sử chỉnh sửa khóa học
        </CardTitle>
        <CardDescription className="mt-1">
          Theo dõi tất cả các thay đổi được thực hiện trên khóa học này
        </CardDescription>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-6">
          {history.map((item, index) => {
            const Icon = getActionIcon(item.type);
            const colors = getActionColor(item.type);
            const isLast = index === history.length - 1;

            return (
              <div key={item.id} className="flex gap-4">
                <div className="flex flex-col items-center">
                  <div
                    className={`flex h-12 w-12 items-center justify-center rounded-xl ${colors.bg} ${colors.border} border-2 shadow-sm`}
                  >
                    <Icon className={`h-6 w-6 ${colors.icon}`} />
                  </div>
                  {!isLast && (
                    <div className="mt-3 h-16 w-px bg-gradient-to-b from-gray-300 to-gray-100" />
                  )}
                </div>
                <div className="flex-1 pb-6">
                  <div className="mb-3 flex items-center gap-3">
                    <h3 className="font-semibold text-gray-900">
                      {item.action}
                    </h3>
                    <Badge
                      className={`${getBadgeVariant(item.type)} text-xs shadow-sm`}
                      variant="outline"
                    >
                      {getActionText(item.type)}
                    </Badge>
                  </div>
                  <p className="mb-4 text-gray-700 leading-relaxed">
                    {item.description}
                  </p>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8 shadow-sm ring-2 ring-white">
                        <AvatarImage
                          src={`/placeholder.svg?height=32&width=32&text=${item.user.charAt(0)}`}
                        />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 font-semibold text-sm text-white">
                          {item.user.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <span className="font-semibold text-gray-900 text-sm">
                          {item.user}
                        </span>
                        <span className="ml-2 text-gray-500 text-sm">
                          • {item.role}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 text-gray-500 text-sm">
                      <span>•</span>
                      <time className="font-medium">
                        {new Date(item.date).toLocaleDateString("vi-VN")}
                      </time>
                      <span>•</span>
                      <time>{item.time}</time>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
