import { Award, BarChart3, <PERSON><PERSON>dingU<PERSON>, Users } from "lucide-react";
import { StatsCard } from "@/components/ui/stats-card";
import { m } from "@/paraglide/messages.js";
import type { Course } from "@/types/courses";

interface CourseStatsProps {
  course: Course;
}

export function CourseStats({ course }: CourseStatsProps) {
  const formatValue = (value: number | string) => {
    if (typeof value === "number" && value > 999) {
      return `${(value / 1000).toFixed(1)}k`;
    }
    return value;
  };

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
      <StatsCard
        title={m["courseStats.totalStudents"]()}
        value={formatValue(course.stats?.learners || 0)}
        badge={{
          variant: "outline",
          icon: <Users className="h-4 w-4" />,
          text: m["courseStats.studentsLabel"](),
        }}
        footer={{
          title: m["courseStats.totalStudentsTitle"](),
          subtitle: m["courseStats.totalStudentsSubtitle"](),
          icon: <Users className="h-4 w-4" />,
        }}
        className="border-blue-100 bg-gradient-to-t from-blue-50 to-card"
      />

      <StatsCard
        title={m["courseStats.totalLessons"]()}
        value={formatValue(course.stats?.lessons || 0)}
        badge={{
          variant: "secondary",
          icon: <Award className="h-4 w-4" />,
          text: m["courseStats.lessonsLabel"](),
        }}
        footer={{
          title: m["courseStats.totalLessonsTitle"](),
          subtitle: m["courseStats.totalLessonsSubtitle"](),
          icon: <Award className="h-4 w-4" />,
        }}
        className="border-green-100 bg-gradient-to-t from-green-50 to-card"
      />

      <StatsCard
        title={m["courseStats.completionRate"]()}
        value={`${course.completionRate || 0}%`}
        badge={{
          variant: "outline",
          icon: <TrendingUp className="h-4 w-4" />,
          text:
            course.completionRate > 80
              ? m["courseStats.goodRate"]()
              : m["courseStats.needsImprovement"](),
        }}
        footer={{
          title:
            course.completionRate > 80
              ? m["courseStats.goodRateTitle"]()
              : m["courseStats.needsImprovementTitle"](),
          subtitle: m["courseStats.completionRateSubtitle"](),
          icon: <TrendingUp className="h-4 w-4" />,
        }}
        className="border-orange-100 bg-gradient-to-t from-orange-50 to-card"
      />

      <StatsCard
        title={m["courseStats.rating"]()}
        value={`${course.stats?.stars || 0}/5`}
        badge={{
          variant: "outline",
          icon: <BarChart3 className="h-4 w-4" />,
          text:
            (course.stats?.stars || 0) >= 4
              ? m["courseStats.excellent"]()
              : (course.stats?.stars || 0) >= 3
                ? m["courseStats.good"]()
                : m["courseStats.fair"](),
        }}
        footer={{
          title: m["courseStats.courseRatingTitle"](),
          subtitle: `${course.stats?.reviews || 0} ${m["courseStats.ratingSubtitle"]()}`,
          icon: <BarChart3 className="h-4 w-4" />,
        }}
        className="border-purple-100 bg-gradient-to-t from-purple-50 to-card"
      />
    </div>
  );
}
