import { Edit, LinkIcon, Save, Upload, X } from "lucide-react";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import type { CourseData } from "./types";

interface CourseOverviewProps {
  courseData: CourseData;
  onSave: (updatedData: Partial<CourseData>) => void;
}

export function CourseOverview({ courseData, onSave }: CourseOverviewProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [uploadMethod, setUploadMethod] = useState<"file" | "url">("file");
  const [formData, setFormData] = useState({
    title: courseData.title,
    slug: courseData.slug,
    description: courseData.description,
    category: courseData.category,
    level: courseData.level,
    goal: courseData.goal,
    imageUrl: courseData.imageUrl,
    requirement: courseData.requirement,
  });

  const handleSave = () => {
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData({
      title: courseData.title,
      slug: courseData.slug,
      description: courseData.description,
      category: courseData.category,
      level: courseData.level,
      goal: courseData.goal,
      imageUrl: courseData.imageUrl,
      requirement: courseData.requirement,
    });
    setIsEditing(false);
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setFormData({ ...formData, imageUrl: reader.result as string });
      };
      reader.readAsDataURL(file);
    }
  };

  const getRequirementColor = (requirement: string) => {
    switch (requirement) {
      case "company":
        return "bg-red-50 text-red-700 border-red-200 ring-red-600/20";
      case "department":
        return "bg-orange-50 text-orange-700 border-orange-200 ring-orange-600/20";
      case "optional":
        return "bg-green-50 text-green-700 border-green-200 ring-green-600/20";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200 ring-gray-600/20";
    }
  };

  const getRequirementText = (requirement: string) => {
    switch (requirement) {
      case "company":
        return "Bắt buộc cả công ty";
      case "department":
        return "Bắt buộc theo phòng ban";
      case "optional":
        return "Tùy chọn/khuyến khích";
      default:
        return requirement;
    }
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="border-gray-100 border-b">
        <div className="flex items-center justify-between">
          <CardTitle className="font-semibold text-lg">
            Thông tin khóa học
          </CardTitle>
          {!isEditing ? (
            <Button
              onClick={() => setIsEditing(true)}
              variant="outline"
              size="sm"
              className="shadow-sm"
            >
              <Edit className="mr-2 h-4 w-4" />
              Chỉnh sửa
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button onClick={handleSave} size="sm" className="shadow-sm">
                <Save className="mr-2 h-4 w-4" />
                Lưu
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                size="sm"
                className="shadow-sm"
              >
                <X className="mr-2 h-4 w-4" />
                Hủy
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-6">
        {!isEditing ? (
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            <div className="space-y-6">
              <div className="space-y-2">
                <Label className="font-medium text-gray-700 text-sm">
                  Tên khóa học
                </Label>
                <p className="font-medium text-gray-900 text-sm">
                  {courseData.title}
                </p>
              </div>
              <div className="space-y-2">
                <Label className="font-medium text-gray-700 text-sm">
                  Slug khóa học
                </Label>
                <p className="rounded bg-gray-50 px-3 py-1 font-mono text-gray-600 text-sm">
                  {courseData.slug}
                </p>
              </div>
              <div className="space-y-2">
                <Label className="font-medium text-gray-700 text-sm">
                  Danh mục
                </Label>
                <Badge variant="outline" className="w-fit">
                  {courseData.category}
                </Badge>
              </div>
              <div className="space-y-2">
                <Label className="font-medium text-gray-700 text-sm">
                  Cấp độ học viên
                </Label>
                <Badge variant="secondary" className="w-fit">
                  {courseData.level}
                </Badge>
              </div>
              <div className="space-y-2">
                <Label className="font-medium text-gray-700 text-sm">
                  Loại khóa học
                </Label>
                <Badge
                  className={`w-fit ${getRequirementColor(courseData.requirement)}`}
                  variant="outline"
                >
                  {getRequirementText(courseData.requirement)}
                </Badge>
              </div>
            </div>
            <div className="space-y-6">
              <div className="space-y-2">
                <Label className="font-medium text-gray-700 text-sm">
                  Mô tả khóa học
                </Label>
                <p className="text-gray-900 text-sm leading-relaxed">
                  {courseData.description}
                </p>
              </div>
              <div className="space-y-2">
                <Label className="font-medium text-gray-700 text-sm">
                  Mục tiêu khóa học
                </Label>
                <p className="text-gray-900 text-sm leading-relaxed">
                  {courseData.goal}
                </p>
              </div>
              <div className="space-y-2">
                <Label className="font-medium text-gray-700 text-sm">
                  Ảnh khóa học
                </Label>
                {courseData.imageUrl && (
                  <div className="mt-2">
                    <img
                      src={courseData.imageUrl || "/placeholder.svg"}
                      alt="Course preview"
                      className="h-32 w-48 rounded-lg border border-gray-200 object-cover shadow-sm"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title" className="font-medium text-sm">
                  Tên khóa học *
                </Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) =>
                    setFormData({ ...formData, title: e.target.value })
                  }
                  placeholder="Nhập tên khóa học"
                  className="shadow-sm"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="slug" className="font-medium text-sm">
                  Slug khóa học *
                </Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) =>
                    setFormData({ ...formData, slug: e.target.value })
                  }
                  placeholder="course-slug"
                  className="font-mono shadow-sm"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category" className="font-medium text-sm">
                  Danh mục *
                </Label>
                <select
                  value={formData.category}
                  onChange={(e) =>
                    setFormData({ ...formData, category: e.target.value })
                  }
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm shadow-sm ring-offset-background file:border-0 file:bg-transparent file:font-medium file:text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="">Chọn danh mục</option>
                  <option value="Development">Phát triển</option>
                  <option value="Programming">Lập trình</option>
                  <option value="Design">Thiết kế</option>
                  <option value="Backend">Backend</option>
                  <option value="Marketing">Marketing</option>
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="level" className="font-medium text-sm">
                  Cấp độ học viên *
                </Label>
                <select
                  value={formData.level}
                  onChange={(e) =>
                    setFormData({ ...formData, level: e.target.value })
                  }
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm shadow-sm ring-offset-background file:border-0 file:bg-transparent file:font-medium file:text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="">Chọn cấp độ</option>
                  <option value="Beginner">Người mới bắt đầu</option>
                  <option value="Intermediate">Trung cấp</option>
                  <option value="Advanced">Nâng cao</option>
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="requirement" className="font-medium text-sm">
                  Loại khóa học *
                </Label>
                <select
                  value={formData.requirement}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      requirement: e.target.value as
                        | "company"
                        | "department"
                        | "optional",
                    })
                  }
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm shadow-sm ring-offset-background file:border-0 file:bg-transparent file:font-medium file:text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="">Chọn loại khóa học</option>
                  <option value="company">Bắt buộc cả công ty</option>
                  <option value="department">Bắt buộc theo phòng ban</option>
                  <option value="optional">Tùy chọn/khuyến khích</option>
                </select>
              </div>
            </div>
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="description" className="font-medium text-sm">
                  Mô tả khóa học *
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  placeholder="Mô tả những gì học viên sẽ học trong khóa học này"
                  rows={4}
                  className="resize-none shadow-sm"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="goal" className="font-medium text-sm">
                  Mục tiêu khóa học *
                </Label>
                <Textarea
                  id="goal"
                  value={formData.goal}
                  onChange={(e) =>
                    setFormData({ ...formData, goal: e.target.value })
                  }
                  placeholder="Học viên sẽ đạt được gì sau khi hoàn thành khóa học này?"
                  rows={4}
                  className="resize-none shadow-sm"
                />
              </div>
              <div className="space-y-3">
                <Label className="font-medium text-sm">Ảnh khóa học</Label>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant={uploadMethod === "file" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setUploadMethod("file")}
                    className="shadow-sm"
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Tải file
                  </Button>
                  <Button
                    type="button"
                    variant={uploadMethod === "url" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setUploadMethod("url")}
                    className="shadow-sm"
                  >
                    <LinkIcon className="mr-2 h-4 w-4" />
                    Nhập URL
                  </Button>
                </div>
                {uploadMethod === "file" ? (
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="shadow-sm"
                  />
                ) : (
                  <Input
                    value={formData.imageUrl}
                    onChange={(e) =>
                      setFormData({ ...formData, imageUrl: e.target.value })
                    }
                    placeholder="https://example.com/course-image.jpg"
                    className="shadow-sm"
                  />
                )}
                {formData.imageUrl && (
                  <div className="mt-3">
                    <img
                      src={formData.imageUrl || "/placeholder.svg"}
                      alt="Course preview"
                      className="h-32 w-48 rounded-lg border border-gray-200 object-cover shadow-sm"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
