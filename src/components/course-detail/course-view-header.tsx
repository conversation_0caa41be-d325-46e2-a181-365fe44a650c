import { useNavigate } from "@tanstack/react-router";
import { ArrowLeft, Edit } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { m } from "@/paraglide/messages.js";
import { Course } from "@/types/courses";

interface CourseViewHeaderProps {
  course: Course;
}

export function CourseViewHeader({ course }: CourseViewHeaderProps) {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate({ to: "/dashboard/courses" });
  };

  const handleEdit = () => {
    navigate({
      to: "/dashboard/courses/$courseSlug/edit",
      params: { courseSlug: course.slug },
    });
  };

  const getStatusBadge = () => {
    if (!course.unpublished) {
      return (
        <Badge className="bg-green-100 text-green-800">
          {m["common.published"]()}
        </Badge>
      );
    }
    return (
      <Badge className="bg-yellow-100 text-yellow-800">
        {m["common.draft"]()}
      </Badge>
    );
  };

  const getLevelBadge = () => {
    return (
      <Badge variant="outline" className="capitalize">
        {course.level}
      </Badge>
    );
  };

  const getLanguageBadge = () => {
    return (
      <Badge variant="secondary" className="uppercase">
        {course.language}
      </Badge>
    );
  };

  return (
    <div className="border-gray-200 border-b bg-white px-4 py-4 sm:px-6">
      <div className="mx-auto max-w-7xl">
        {/* Mobile Layout */}
        <div className="block sm:hidden">
          <div className="mb-4 flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="xs:inline hidden">{m["common.goBack"]()}</span>
            </Button>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                className="shadow-sm"
                onClick={handleEdit}
              >
                <Edit className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <img
              src={course.image_url}
              alt={course.title || course.name}
              className="h-16 w-16 rounded-lg object-cover"
            />
            <div className="min-w-0 flex-1">
              <h1 className="truncate font-bold text-gray-900 text-lg">
                {course.title || course.name}
              </h1>
              <p className="text-gray-500 text-sm">
                Người tạo khóa: {course.instructor?.name}
              </p>
              <div className="mt-2 flex flex-wrap gap-1">
                {getStatusBadge()}
                {getLevelBadge()}
                {getLanguageBadge()}
              </div>
            </div>
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="hidden sm:block">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-4 w-4" />
                {m["common.backToList"]()}
              </Button>
              <div className="h-6 w-px bg-gray-300" />
              <div className="flex items-center gap-4">
                <img
                  src={course.image_url}
                  alt={course.title || course.name}
                  className="h-12 w-12 rounded-lg object-cover"
                />
                <div>
                  <h1 className="font-bold text-gray-900 text-xl">
                    {course.title || course.name}
                  </h1>
                  <p className="text-gray-500 text-sm">
                    Người tạo khóa: {course.instructor?.name}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                {getStatusBadge()}
                {getLevelBadge()}
                {getLanguageBadge()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
