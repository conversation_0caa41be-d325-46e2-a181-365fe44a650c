import { Search } from "lucide-react";
import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { SortableHeader } from "@/components/ui/sortable-header";
import { useSortableTable } from "@/hooks/use-table-sorting";
import { m } from "@/paraglide/messages.js";
import type { Student } from "./types";

interface StudentsTableProps {
  students?: Student[];
}

export function StudentsTable({
  students = getMockStudents(),
}: StudentsTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const filteredStudents = students.filter(
    (student) =>
      student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.department.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const { sortedData, handleSort, getSortIcon } = useSortableTable({
    data: filteredStudents,
    defaultSort: { column: "name", direction: "asc" },
  });

  const totalPages = Math.ceil(sortedData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedStudents = sortedData.slice(startIndex, endIndex);

  const pageNumbers = Array.from(
    { length: Math.min(5, totalPages) },
    (_, i) => i + 1,
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Hoàn thành":
        return "bg-green-100 text-green-800 border-green-200";
      case "Đang học":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "Tạm dừng":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="border-gray-100 border-b">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="font-semibold text-lg">
              {m["students.registeredStudents"]?.() ?? "Registered Students"}
            </CardTitle>
            <CardDescription className="mt-1">
              {m["students.currentProgressDesc"]?.() ??
                "Current students list and their learning progress"}
            </CardDescription>
          </div>
          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 transform text-gray-400" />
              <Input
                placeholder={
                  m["students.searchPlaceholder"]?.() ?? "Search students..."
                }
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64 pl-10 shadow-sm"
              />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full min-w-[60rem] table-fixed">
            <thead className="border-gray-200 border-b bg-gray-50">
              <tr>
                <th className="w-[20%] px-6 py-4 text-left sm:w-[25%]">
                  <SortableHeader
                    sortDirection={getSortIcon("name")}
                    onSort={() => handleSort("name")}
                  >
                    {m["students.columns.student"]?.() ?? "Student"}
                  </SortableHeader>
                </th>
                <th className="w-[10%] px-6 py-4 text-left">
                  <SortableHeader
                    sortDirection={getSortIcon("department")}
                    onSort={() => handleSort("department")}
                  >
                    {m["students.columns.department"]?.() ?? "Department"}
                  </SortableHeader>
                </th>
                <th className="w-[10%] px-6 py-4 text-left">
                  <SortableHeader
                    sortDirection={getSortIcon("enrolledDate")}
                    onSort={() => handleSort("enrolledDate")}
                  >
                    {m["students.columns.enrollDate"]?.() ?? "Enrolled Date"}
                  </SortableHeader>
                </th>
                <th className="w-[14%] px-6 py-4 text-left">
                  <SortableHeader
                    sortDirection={getSortIcon("progress")}
                    onSort={() => handleSort("progress")}
                  >
                    {m["students.columns.progress"]?.() ?? "Progress"}
                  </SortableHeader>
                </th>
                <th className="w-[10%] px-6 py-4 text-left">
                  <SortableHeader
                    sortDirection={getSortIcon("status")}
                    onSort={() => handleSort("status")}
                  >
                    {m["students.columns.status"]?.() ?? "Status"}
                  </SortableHeader>
                </th>
                <th className="w-[8%] px-6 py-4 text-left">
                  <SortableHeader
                    sortDirection={getSortIcon("score")}
                    onSort={() => handleSort("score")}
                  >
                    {m["students.columns.score"]?.() ?? "Score"}
                  </SortableHeader>
                </th>
                <th className="w-[10%] px-6 py-4 text-left">
                  <SortableHeader
                    sortDirection={getSortIcon("studyTime")}
                    onSort={() => handleSort("studyTime")}
                  >
                    {m["students.columns.studyTime"]?.() ?? "Study Time"}
                  </SortableHeader>
                </th>
                <th className="w-[13%] px-6 py-4 text-left">
                  <SortableHeader
                    sortDirection={getSortIcon("lastAccess")}
                    onSort={() => handleSort("lastAccess")}
                  >
                    {m["students.columns.lastAccess"]?.() ?? "Last Access"}
                  </SortableHeader>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {paginatedStudents.map((student) => (
                <tr
                  key={student.id}
                  className="transition-colors hover:bg-gray-50"
                >
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10 ring-2 ring-white">
                        <AvatarImage
                          src={`/placeholder.svg?height=40&width=40&text=${student.name.charAt(
                            0,
                          )}`}
                        />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 font-semibold text-white">
                          {student.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-semibold text-gray-900">
                          {student.name}
                        </p>
                        <p className="text-gray-500 text-sm">{student.role}</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <Badge variant="outline" className="shadow-sm">
                      {student.department}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 text-gray-600 text-sm">
                    {new Date(student.enrolledDate).toLocaleDateString("vi-VN")}
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <Progress value={student.progress} className="h-2 w-20" />
                      <span className="min-w-[3rem] font-semibold text-gray-900 text-sm">
                        {student.progress}%
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <Badge
                      className={`${getStatusColor(student.status)} shadow-sm`}
                      variant="outline"
                    >
                      {student.status}
                    </Badge>
                  </td>
                  <td className="px-6 py-4">
                    <span className="font-semibold text-gray-900 text-sm">
                      {student.score}{" "}
                      {m["students.score.points"]?.() ?? "points"}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-gray-600 text-sm">
                    {student.studyTime}
                  </td>
                  <td className="px-6 py-4 text-gray-500 text-sm">
                    {student.lastAccess}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {totalPages > 1 && (
          <div className="flex flex-col items-center justify-between gap-2 border-gray-200 border-t bg-gray-50 px-6 py-4 sm:flex-row">
            <div className="flex items-center gap-2 text-gray-600 text-sm">
              <span>{m["courseList.displayLabel"]?.() ?? "Show"}:</span>
              <select
                value={itemsPerPage.toString()}
                onChange={(e) => {
                  setItemsPerPage(parseInt(e.target.value));
                  setCurrentPage(1);
                }}
                className="h-8 w-20 rounded-md border border-gray-300 bg-white px-2 py-1 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
              </select>
              <span>{m["courseList.itemsLabel"]?.() ?? "items"}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="hidden text-gray-600 text-sm sm:inline">
                {m["courseList.resultsLabel"]?.() ?? "Results"}:{" "}
                {startIndex + 1} - {Math.min(endIndex, sortedData.length)}{" "}
                {m["students.paging.of"]?.() ?? "of"} {sortedData.length}
              </span>
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="shadow-sm"
                >
                  ‹
                </Button>
                {pageNumbers.map((pageNum) => (
                  <Button
                    key={pageNum}
                    variant={pageNum === currentPage ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(pageNum)}
                    className="h-8 w-8 shadow-sm"
                  >
                    {pageNum}
                  </Button>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="shadow-sm"
                >
                  ›
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function getMockStudents(): Student[] {
  return [
    {
      id: 1,
      name: "Nguyễn Văn A",
      department: "Marketing",
      enrolledDate: "2023-07-15",
      progress: 85,
      status: "Đang học",
      score: 92,
      studyTime: "12h 30m",
      lastAccess: "Hôm nay, 09:45",
      role: "Chuyên viên",
    },
    {
      id: 2,
      name: "Trần Thị B",
      department: "IT",
      enrolledDate: "2023-08-10",
      progress: 100,
      status: "Hoàn thành",
      score: 95,
      studyTime: "10h 15m",
      lastAccess: "Hôm qua, 15:20",
      role: "Trưởng nhóm",
    },
    {
      id: 3,
      name: "Lê Văn C",
      department: "Sales",
      enrolledDate: "2023-06-20",
      progress: 45,
      status: "Đang học",
      score: 78,
      studyTime: "6h 45m",
      lastAccess: "3 ngày trước",
      role: "Chuyên viên",
    },
    {
      id: 4,
      name: "Phạm Thị D",
      department: "HR",
      enrolledDate: "2023-09-01",
      progress: 65,
      status: "Đang học",
      score: 82,
      studyTime: "8h 20m",
      lastAccess: "Hôm nay, 14:30",
      role: "Quản lý",
    },
    {
      id: 5,
      name: "Hoàng Văn E",
      department: "Finance",
      enrolledDate: "2023-07-25",
      progress: 30,
      status: "Tạm dừng",
      score: 60,
      studyTime: "4h 10m",
      lastAccess: "1 tuần trước",
      role: "Kế toán",
    },
    {
      id: 6,
      name: "Vũ Thị F",
      department: "IT",
      enrolledDate: "2023-08-15",
      progress: 75,
      status: "Đang học",
      score: 88,
      studyTime: "9h 45m",
      lastAccess: "Hôm nay, 10:15",
      role: "Lập trình viên",
    },
    {
      id: 7,
      name: "Đặng Văn G",
      department: "Marketing",
      enrolledDate: "2023-09-05",
      progress: 95,
      status: "Hoàn thành",
      score: 94,
      studyTime: "11h 30m",
      lastAccess: "Hôm qua, 16:40",
      role: "Chuyên viên",
    },
    {
      id: 8,
      name: "Bùi Thị H",
      department: "Sales",
      enrolledDate: "2023-07-10",
      progress: 20,
      status: "Tạm dừng",
      score: 45,
      studyTime: "2h 50m",
      lastAccess: "2 tuần trước",
      role: "Nhân viên",
    },
    {
      id: 9,
      name: "Trương Văn I",
      department: "HR",
      enrolledDate: "2023-08-28",
      progress: 55,
      status: "Đang học",
      score: 76,
      studyTime: "7h 15m",
      lastAccess: "2 ngày trước",
      role: "Chuyên viên",
    },
    {
      id: 10,
      name: "Ngô Thị K",
      department: "Finance",
      enrolledDate: "2023-06-15",
      progress: 100,
      status: "Hoàn thành",
      score: 98,
      studyTime: "13h 45m",
      lastAccess: "5 ngày trước",
      role: "Trưởng phòng",
    },
    {
      id: 11,
      name: "Lý Văn L",
      department: "IT",
      enrolledDate: "2023-07-20",
      progress: 70,
      status: "Đang học",
      score: 84,
      studyTime: "9h 10m",
      lastAccess: "Hôm nay, 11:25",
      role: "Kỹ sư",
    },
    {
      id: 12,
      name: "Hồ Thị M",
      department: "Marketing",
      enrolledDate: "2023-09-10",
      progress: 40,
      status: "Đang học",
      score: 72,
      studyTime: "5h 30m",
      lastAccess: "4 ngày trước",
      role: "Nhân viên",
    },
  ];
}
