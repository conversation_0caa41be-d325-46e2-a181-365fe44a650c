import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Eye } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import type { CourseData } from "./types";

interface CourseHeaderProps {
  courseData: CourseData;
  isPublishing: boolean;
  onBack: () => void;
  onPublish: () => void;
}

export function CourseHeader({
  courseData,
  isPublishing,
  onBack,
  onPublish,
}: CourseHeaderProps) {
  return (
    <div className="border-gray-200 border-b bg-white px-4 py-4 sm:px-6">
      <div className="mx-auto max-w-7xl">
        {/* Mobile Layout */}
        <div className="block sm:hidden">
          <div className="mb-4 flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="xs:inline hidden">Quay lại</span>
            </Button>
            <div className="flex items-center gap-2">
              {courseData.status === "draft" && (
                <Button
                  onClick={onPublish}
                  disabled={isPublishing}
                  size="sm"
                  className="bg-green-600 text-white shadow-sm hover:bg-green-700"
                >
                  {isPublishing ? "Xuất bản..." : "Xuất bản"}
                </Button>
              )}
              <Button variant="outline" size="sm" className="shadow-sm">
                <Edit className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
              <span className="font-semibold text-white">
                {courseData.title.charAt(0)}
              </span>
            </div>
            <div className="min-w-0 flex-1">
              <h1 className="truncate font-bold text-gray-900 text-lg">
                {courseData.title}
              </h1>
              <div className="mt-1 flex flex-wrap items-center gap-2">
                <Badge
                  variant={
                    courseData.status === "published" ? "default" : "secondary"
                  }
                  className={
                    courseData.status === "published"
                      ? "border-green-200 bg-green-100 text-green-800"
                      : "border-yellow-200 bg-yellow-100 text-yellow-800"
                  }
                >
                  {courseData.status === "published"
                    ? "Đã xuất bản"
                    : "Bản nháp"}
                </Badge>
                <span className="text-gray-500 text-xs">
                  {courseData.lessons} bài học
                </span>
                <span className="text-gray-500 text-xs">
                  {courseData.duration}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="hidden sm:flex sm:items-center sm:justify-between">
          <div className="flex items-center gap-4 lg:gap-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4" />
              Quay lại
            </Button>
            <div className="flex items-center gap-3 lg:gap-4">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 lg:h-12 lg:w-12">
                <span className="font-semibold text-white lg:text-lg">
                  {courseData.title.charAt(0)}
                </span>
              </div>
              <div className="min-w-0">
                <h1 className="font-bold text-gray-900 text-xl lg:text-2xl">
                  {courseData.title}
                </h1>
                <div className="mt-1 flex items-center gap-2 lg:gap-3">
                  <Badge
                    variant={
                      courseData.status === "published"
                        ? "default"
                        : "secondary"
                    }
                    className={
                      courseData.status === "published"
                        ? "border-green-200 bg-green-100 text-green-800"
                        : "border-yellow-200 bg-yellow-100 text-yellow-800"
                    }
                  >
                    {courseData.status === "published"
                      ? "Đã xuất bản"
                      : "Bản nháp"}
                  </Badge>
                  <span className="text-gray-500 text-sm">•</span>
                  <span className="text-gray-500 text-sm">
                    {courseData.lessons} bài học
                  </span>
                  <span className="text-gray-500 text-sm">•</span>
                  <span className="text-gray-500 text-sm">
                    {courseData.duration}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2 lg:gap-3">
            {courseData.status === "draft" && (
              <Button
                onClick={onPublish}
                disabled={isPublishing}
                className="bg-green-600 text-white shadow-sm hover:bg-green-700"
              >
                {isPublishing ? "Đang xuất bản..." : "Xuất bản"}
              </Button>
            )}
            <Button variant="outline" className="shadow-sm">
              <Edit className="mr-2 h-4 w-4" />
              <span className="hidden lg:inline">Chỉnh sửa</span>
            </Button>
            <Button variant="outline" className="shadow-sm">
              <Eye className="mr-2 h-4 w-4" />
              <span className="hidden lg:inline">Xem trước</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
