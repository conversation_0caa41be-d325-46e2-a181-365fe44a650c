import { Edit } from "lucide-react";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { m } from "@/paraglide/messages.js";
import { Course, CourseRequirement } from "@/types/courses";
import { cn } from "@/utils/cn";
import { parseCourseDescription } from "@/utils/course-helpers";
import { CourseEditor } from "../courses/CourseEditor";
import { Button } from "../ui/button";

interface CourseViewOverviewProps {
  course: Course;
  refetchCourse?: () => void;
}

export function CourseViewOverview({
  course,
  refetchCourse,
}: CourseViewOverviewProps) {
  const parsedDescription = parseCourseDescription(course.description);
  const [editCourse, setEditCourse] = useState(false);

  if (editCourse) {
    return (
      <CourseEditor
        selectedCourse={course}
        onSuccess={() => {
          setEditCourse(false);
          refetchCourse?.();
        }}
      />
    );
  }

  return (
    <div className="flex gap-7">
      {/* Course Information */}
      <Card className="w-2/3">
        <CardHeader>
          <CardTitle>
            <div className="flex items-center justify-between">
              {m["courseDetail.overview.courseInfo"]?.() ??
                "Thông tin khóa học"}
              <Button
                variant="outline"
                className="shadow-sm"
                onClick={() => setEditCourse(true)}
              >
                <Edit className="mr-2 h-4 w-4" />
                {m["courseDetail.overview.editButton"]?.() ?? "Chỉnh sửa"}
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-muted-foreground text-sm">
                {m["courseDetail.overview.courseName"]?.() ?? "Tên khóa học"}
              </label>
              <p className="font-medium">{course.title || course.name}</p>
            </div>
            <div>
              <label className="text-muted-foreground text-sm">
                {m["courseDetail.overview.urlSlug"]?.() ?? "Đường dẫn URL"}
              </label>
              <p className="font-medium font-mono text-sm">{course.slug}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-muted-foreground text-sm">
                {m["courseDetail.overview.level"]?.() ?? "Cấp độ"}
              </label>
              <p className="font-medium capitalize">{course.level}</p>
            </div>
            <div>
              <label className="text-muted-foreground text-sm">
                {m["courseDetail.overview.language"]?.() ?? "Ngôn ngữ"}
              </label>
              <p className="font-medium uppercase">{course.language}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-muted-foreground text-sm">
                {m["courseDetail.overview.status"]?.() ?? "Trạng thái"}
              </label>
              <Badge
                className={cn(
                  "mt-1 block",
                  course.unpublished
                    ? "bg-yellow-500 text-white"
                    : "bg-green-500 text-white",
                )}
              >
                {course.unpublished
                  ? (m["courseDetail.overview.statusDraft"]?.() ?? "Bản nháp")
                  : (m["courseDetail.overview.statusPublished"]?.() ??
                    "Đã xuất bản")}
              </Badge>
            </div>

            <div>
              <label className="text-muted-foreground text-sm">
                {m["courseDetail.overview.instructor"]?.() ?? "Người tạo khóa"}
              </label>
              <p className="font-medium">
                {course.instructor?.name ||
                  (m["courseDetail.overview.noInstructor"]?.() ??
                    "Chưa phân công")}
              </p>
            </div>

            {course.created_at && (
              <div>
                <label className="text-muted-foreground text-sm">
                  {m["courseDetail.overview.createdDate"]?.() ?? "Ngày tạo"}
                </label>
                <p className="font-medium">
                  {new Date(course.created_at).toLocaleDateString("vi-VN")}
                </p>
              </div>
            )}

            {course.updated_at && (
              <div>
                <label className="text-muted-foreground text-sm">
                  {m["courseDetail.overview.updatedDate"]?.() ??
                    "Cập nhật cuối"}
                </label>
                <p className="font-medium">
                  {new Date(course.updated_at).toLocaleDateString("vi-VN")}
                </p>
              </div>
            )}

            <div>
              <label className="text-muted-foreground text-sm">
                {m["courseDetail.overview.categories"]?.() ?? "Danh mục"}
              </label>
              <div className="mt-1 flex flex-wrap gap-2">
                {course.categories && course.categories.length > 0 ? (
                  course.categories.map((category) => (
                    <span
                      key={category.id}
                      className="inline-flex items-center rounded-full bg-slate-100 px-3 py-1 font-medium text-slate-700 text-xs ring-1 ring-slate-200 ring-inset"
                    >
                      {category.name}
                    </span>
                  ))
                ) : (
                  <p className="text-muted-foreground text-sm">
                    {m["courseDetail.overview.noCategories"]?.() ??
                      "Chưa có danh mục"}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label className="text-muted-foreground text-sm">
                {m["courseDetail.overview.courseImage"]?.() ??
                  "Hình ảnh khóa học"}
              </label>
              <div className="mt-2">
                <img
                  src={course.image_url}
                  alt={course.title || course.name}
                  className="max-w-2xs rounded-lg object-cover"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Course Description */}
      <Card className="flex-1">
        <CardHeader>
          <CardTitle>
            {m["courseDetail.overview.courseDescription"]?.() ??
              "Mô tả khóa học"}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {parsedDescription.overview && (
            <div>
              <h3 className="mb-2 font-medium text-muted-foreground text-sm">
                {m["courseDetail.overview.overview"]?.() ?? "Tổng quan:"}
              </h3>
              <p className="text-sm leading-relaxed">
                {parsedDescription.overview}
              </p>
            </div>
          )}

          {parsedDescription.goals && parsedDescription.goals.length > 0 && (
            <div>
              <h3 className="mb-2 font-medium text-muted-foreground text-sm">
                {m["courseDetail.overview.courseGoals"]?.() ??
                  "Mục tiêu khóa học:"}
              </h3>
              <ul className="space-y-1">
                {parsedDescription.goals.map(
                  (goal: string, index: number) =>
                    goal.trim() && (
                      <li
                        key={index}
                        className="flex items-start gap-2 text-sm"
                      >
                        <span className="mt-1 text-green-600">✓</span>
                        <span>{goal}</span>
                      </li>
                    ),
                )}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
