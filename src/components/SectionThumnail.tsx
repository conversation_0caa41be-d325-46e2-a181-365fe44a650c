import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  FileText,
  Gamepad2,
  GripVertical,
  Sparkles,
  Trash2,
  Trophy,
} from "lucide-react";
import { memo, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Section } from "@/types/lessons";

interface ThumbnailProps {
  sectionId: string;
}

interface SectionThumbnailProps {
  sections: Section[];
  selected: number;
  setSelected: (index: number) => void;
  deleteSection: (index: number) => void;
  onReorder: (oldIndex: number, newIndex: number) => void;
  disabled: boolean;
}

function Thumbnail({ sectionId }: ThumbnailProps) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const waitRender = setInterval(() => {
      const originalElement = document.getElementById(
        `section-view-${sectionId}`,
      );
      if (originalElement) {
        const cloned = originalElement.cloneNode(true) as HTMLElement;
        cloned.id = `section-thumbnail-${sectionId}`;
        cloned.style.transform = "scale(0.3)";
        cloned.style.transformOrigin = "top left";
        cloned.style.width = "calc(100% / 0.3)";
        cloned.style.height = "auto";
        cloned.style.pointerEvents = "none";
        if (ref.current?.childNodes[0]) {
          ref.current.replaceChild(cloned, ref.current.childNodes[0]);
          clearInterval(waitRender);
        }
      }
    }, 1000);

    return () => clearInterval(waitRender);
  }, [sectionId]);

  return (
    <div className="h-full w-full overflow-hidden p-1" ref={ref}>
      <div className="flex h-full w-full items-center justify-center">
        ...Loading...
      </div>
    </div>
  );
}

interface SortableItemProps {
  section: Section;
  index: number;
  selected: number;
  setSelected: (index: number) => void;
  deleteSection: (index: number) => void;
  disabled: boolean;
}

const SortableItem = memo(function SortableItem({
  section,
  index,
  selected,
  setSelected,
  deleteSection,
  disabled,
}: SortableItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: section.id,
    transition: {
      duration: 200,
      easing: "cubic-bezier(0.25, 1, 0.5, 1)",
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: isDragging
      ? "none"
      : transition || "transform 200ms cubic-bezier(0.25, 1, 0.5, 1)",
    zIndex: isDragging ? 1000 : "auto",
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`group relative transition-all duration-200 ease-out ${
        isDragging
          ? "rotate-1 scale-105 opacity-90 shadow-2xl"
          : "rotate-0 scale-100 opacity-100 shadow-sm"
      } ${disabled ? "cursor-not-allowed" : "cursor-pointer"}`}
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className={`absolute top-2 left-2 z-20 flex h-6 w-6 items-center justify-center rounded-full bg-white/90 shadow-sm backdrop-blur-sm transition-opacity ${
          disabled
            ? "cursor-not-allowed opacity-50"
            : "cursor-grab opacity-0 active:cursor-grabbing group-hover:opacity-100"
        }`}
        title={disabled ? "Đang xử lý..." : "Kéo để sắp xếp lại"}
      >
        {disabled ? (
          <div className="h-3 w-3 animate-spin rounded-full border border-gray-400 border-t-transparent"></div>
        ) : (
          <GripVertical className="h-3 w-3 text-gray-600" />
        )}
      </div>

      {/* Action Buttons */}
      <div className="absolute top-2 right-2 z-10 flex flex-col gap-1 opacity-0 transition-opacity group-hover:opacity-100">
        <Button
          type="button"
          variant="outline"
          size="sm"
          disabled={disabled}
          className="h-7 w-7 border-red-200 bg-red-50 p-0 text-red-600 hover:bg-red-100 hover:text-red-700"
          onClick={() => deleteSection(index)}
          title="Xóa phần"
        >
          <Trash2 className="h-3 w-3" />
        </Button>
      </div>

      {/* Section Type Badge */}
      <div className="absolute right-2 bottom-2 z-10 rounded-md bg-white/90 px-2 py-1 shadow-sm backdrop-blur-sm">
        <div className="flex items-center gap-1" title={section.type}>
          {section.type === "TEXT" && (
            <>
              <FileText className="h-3 w-3 text-blue-600" />
              <span className="font-medium text-blue-600 text-xs">Text</span>
            </>
          )}
          {section.type === "GAME" && (
            <>
              <Gamepad2 className="h-3 w-3 text-green-600" />
              <span className="font-medium text-green-600 text-xs">Game</span>
            </>
          )}
          {section.type === "CHALLENGE" && (
            <>
              <Trophy className="h-3 w-3 text-orange-600" />
              <span className="font-medium text-orange-600 text-xs">Quiz</span>
            </>
          )}
          {section.type === "AIGENERATED" && (
            <>
              <Sparkles className="h-3 w-3 text-purple-600" />
              <span className="font-medium text-purple-600 text-xs">AI</span>
            </>
          )}
        </div>
      </div>

      {/* Section Number */}
      <div className="-top-2 -left-2 absolute z-10 flex h-6 w-6 items-center justify-center rounded-full bg-white/90 shadow-sm backdrop-blur-sm">
        <span className="font-semibold text-gray-700 text-xs">{index + 1}</span>
      </div>

      {/* Thumbnail Container */}
      <div
        onClick={() => setSelected(index)}
        className={`relative h-45 cursor-pointer overflow-hidden rounded-lg border-2 transition-all duration-200 ease-out ${
          selected === index
            ? "border-blue-500 bg-blue-50 shadow-md"
            : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm"
        }`}
        style={{
          willChange: isDragging ? "transform" : "auto",
          backfaceVisibility: "hidden",
          perspective: "1000px",
        }}
      >
        <Thumbnail sectionId={section.id} />
      </div>
    </div>
  );
});

export default function SectionThumbnail({
  sections,
  selected,
  setSelected,
  deleteSection,
  onReorder,
  disabled = false,
}: SectionThumbnailProps) {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Require 8px movement before drag starts
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = sections.findIndex(
        (section) => section.id === active.id,
      );
      const newIndex = sections.findIndex((section) => section.id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        onReorder(oldIndex, newIndex);
      }
    }
  };

  return (
    <div className="relative">
      {/* Loading State Indicator */}
      {disabled && (
        <div className="absolute top-0 right-0 left-0 z-40 flex items-center justify-center py-2">
          <div className="flex items-center gap-2 rounded-full border border-blue-100 bg-blue-50 px-4 py-2 shadow-sm">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-200 border-t-blue-600"></div>
            <span className="font-medium text-blue-700 text-sm">
              Đang sắp xếp lại...
            </span>
          </div>
        </div>
      )}

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        modifiers={[restrictToVerticalAxis]}
      >
        <SortableContext
          items={sections.map((section) => section.id)}
          strategy={verticalListSortingStrategy}
        >
          <div
            className={`space-y-3 transition-all duration-300 ease-out ${
              disabled
                ? "pointer-events-none scale-[0.98] opacity-30 blur-[0.5px] grayscale-[0.3]"
                : "scale-100 opacity-100 blur-0 grayscale-0"
            }`}
            style={{
              contain: "layout style",
              willChange: "auto",
            }}
          >
            {sections &&
              sections.length > 0 &&
              sections.map((section, index) => (
                <SortableItem
                  key={section.id}
                  section={section}
                  index={index}
                  selected={selected}
                  setSelected={setSelected}
                  deleteSection={deleteSection}
                  disabled={disabled}
                />
              ))}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  );
}
