import { type Icon } from "@tabler/icons-react";
import { Link } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon?: Icon;
  }[];
}) {
  const [activeLink, setActiveLink] = useState("");

  useEffect(() => {
    const currentPath = window.location.pathname;
    const activeItem = items.find((item) => item.url === currentPath);
    if (activeItem) {
      setActiveLink(activeItem.url);
    }
  }, [items.find]);

  const handleLinkClick = (url: string) => {
    setActiveLink(url);
  };

  return (
    <SidebarGroup>
      <SidebarGroupContent className="flex flex-col gap-2">
        <SidebarMenu>
          {items.map((item) => {
            const isActive = activeLink === item.url;

            return (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton
                  className={`${
                    isActive
                      ? "bg-primary text-primary-foreground hover:bg-primary/70 hover:text-primary-foreground"
                      : ""
                  }`}
                  tooltip={item.title}
                  asChild
                  onClick={() => handleLinkClick(item.url)}
                >
                  <Link to={item.url}>
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
