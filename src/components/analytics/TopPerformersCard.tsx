import { useNavigate } from "@tanstack/react-router";
import { ChevronLeft, ChevronRight, Maximize2, Search } from "lucide-react";
import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import type { TimeFilter, TopPerformer } from "@/types/analytics";

interface TopPerformersCardProps {
  performersData: TopPerformer[];
}

/**
 * Component for displaying top performing employees with expandable modal view
 * Shows employee rankings, scores, departments, and course completions
 */
export function TopPerformersCard({ performersData }: TopPerformersCardProps) {
  const [performersPeriod, setPerformersPeriod] =
    useState<TimeFilter>("30days");
  const [performersModalOpen, setPerformersModalOpen] = useState(false);
  const [performersSearch, setPerformersSearch] = useState("");
  const [performersPage, setPerformersPage] = useState(1);
  const navigate = useNavigate();
  const itemsPerPage = 10;

  // Filter performers based on search
  const filteredPerformers = performersData.filter(
    (performer) =>
      performer.name.toLowerCase().includes(performersSearch.toLowerCase()) ||
      performer.department
        .toLowerCase()
        .includes(performersSearch.toLowerCase()),
  );

  const totalPerformersPages = Math.ceil(
    filteredPerformers.length / itemsPerPage,
  );
  const paginatedPerformers = filteredPerformers.slice(
    (performersPage - 1) * itemsPerPage,
    performersPage * itemsPerPage,
  );

  // Handle navigation to user details
  const handleUserClick = (userId: string) => {
    navigate({ to: "/dashboard/users/$userId", params: { userId } });
  };

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-base">
                Top nhân viên xuất sắc
              </CardTitle>
              <p className="text-muted-foreground text-sm">
                Dựa trên điểm số và hoạt động học tập
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Select
                value={performersPeriod}
                onValueChange={(value) =>
                  setPerformersPeriod(value as TimeFilter)
                }
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7days">7 ngày</SelectItem>
                  <SelectItem value="30days">30 ngày</SelectItem>
                  <SelectItem value="90days">90 ngày</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setPerformersModalOpen(true)}
                className="p-2"
              >
                <Maximize2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Top performers table */}
          <Table>
            <TableHeader className="[&_tr]:border-0">
              <TableRow className="border-0 hover:bg-transparent">
                <TableHead className="w-8 border-0 px-0">#</TableHead>
                <TableHead className="border-0"></TableHead>
                <TableHead className="border-0">Tên nhân viên</TableHead>
                <TableHead className="border-0 text-right">Điểm XP</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="[&_tr]:border-0">
              {performersData.slice(0, 5).map((performer, index) => (
                <TableRow
                  key={performer.name}
                  className="cursor-pointer border-0 transition-colors hover:bg-gray-50"
                  onClick={() => handleUserClick?.(String(index + 1))}
                >
                  <TableCell className="w-8 border-0 px-0 font-medium text-muted-foreground text-sm">
                    {index + 1}
                  </TableCell>
                  <TableCell className="w-13 border-0 p-0">
                    <Avatar>
                      <AvatarImage src={performer.avatar} />
                      <AvatarFallback>
                        {performer.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                  </TableCell>
                  <TableCell className="border-0">
                    {/* Combined: Rank badge, Avatar, and User info */}
                    <div className="flex items-center gap-3">
                      {/* Avatar */}

                      {/* User info */}
                      <div>
                        <div className="font-medium text-sm">
                          {performer.name}
                        </div>
                        <div className="text-muted-foreground text-xs">
                          {performer.department} • {performer.courses} khóa học
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="border-0 text-right">
                    {/* Score */}
                    <div className="flex items-center justify-end gap-2">
                      <div className="font-bold text-lg">{performer.xp}</div>
                      <div className="text-muted-foreground text-xs">XP</div>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Performers Modal */}
      <Dialog open={performersModalOpen} onOpenChange={setPerformersModalOpen}>
        <DialogContent className="max-h-[80vh] max-w-4xl overflow-hidden">
          <DialogHeader>
            <DialogTitle>Tất cả nhân viên xuất sắc</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Search and filter controls */}
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 transform text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm nhân viên..."
                  value={performersSearch}
                  onChange={(e) => {
                    setPerformersSearch(e.target.value);
                    setPerformersPage(1);
                  }}
                  className="pl-10"
                />
              </div>
              <Select
                value={performersPeriod}
                onValueChange={(value) =>
                  setPerformersPeriod(value as TimeFilter)
                }
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7days">7 ngày</SelectItem>
                  <SelectItem value="30days">30 ngày</SelectItem>
                  <SelectItem value="90days">90 ngày</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Performers list with pagination */}
            <div className="max-h-96 overflow-y-auto">
              <Table>
                <TableBody className="[&_tr]:border-0">
                  {paginatedPerformers.map((performer, index) => (
                    <TableRow
                      key={performer.name}
                      className="cursor-pointer border-0 transition-colors hover:bg-gray-50"
                      onClick={() => handleUserClick?.(String(performer.id))}
                    >
                      <TableCell className="border-0">
                        {/* Combined: Rank badge, Avatar, and User info */}
                        <div className="flex items-center gap-3">
                          {/* Rank badge */}
                          <span className="font-bold text-sm">{index + 1}</span>

                          {/* Avatar */}
                          <Avatar>
                            <AvatarImage src={performer.avatar} />
                            <AvatarFallback>
                              {performer.name.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>

                          {/* User info */}
                          <div>
                            <div className="font-medium text-sm">
                              {performer.name}
                            </div>
                            <div className="text-muted-foreground text-xs">
                              {performer.department} • {performer.courses} khóa
                              học
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="border-0 text-right">
                        {/* Score */}
                        <div className="flex items-center justify-end gap-2">
                          <div className="font-bold text-lg">
                            {performer.xp}
                          </div>
                          <div className="text-muted-foreground text-xs">
                            XP
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Pagination controls */}
            <div className="flex items-center justify-between border-t pt-4">
              <div className="text-muted-foreground text-sm">
                Hiển thị {(performersPage - 1) * itemsPerPage + 1} -{" "}
                {Math.min(
                  performersPage * itemsPerPage,
                  filteredPerformers.length,
                )}{" "}
                trong tổng số {filteredPerformers.length} nhân viên
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setPerformersPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={performersPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm">
                  Trang {performersPage} / {totalPerformersPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setPerformersPage((prev) =>
                      Math.min(prev + 1, totalPerformersPages),
                    )
                  }
                  disabled={performersPage === totalPerformersPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
