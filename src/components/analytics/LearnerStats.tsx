import { Activity, UserPlus, Users } from "lucide-react";
import { useState } from "react";
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type {
  DepartmentStats,
  LearningTrendData,
  StatsTab,
  TimeFilter,
} from "@/types/analytics";
import formatDate from "@/utils/format-date";

interface LearnerStatsProps {
  learningTrendData: LearningTrendData[];
  departmentStats: DepartmentStats[];
}

/**
 * Component for displaying learner statistics with interactive charts and filters
 * Includes metrics for total, active, and new learners with trend visualization
 */
export function LearnerStats({
  learningTrendData,
  departmentStats,
}: LearnerStatsProps) {
  const [statsTab, setStatsTab] = useState<StatsTab>("total");
  const [statsTimeFilter, setStatsTimeFilter] = useState<TimeFilter>("30days");

  // Metric configuration for different tabs
  const metrics = [
    {
      key: "active" as const,
      title: "Người học active",
      value: "1.2K",
      icon: Activity,
    },
    {
      key: "new" as const,
      title: "Người học mới",
      value: "1.2K",
      icon: UserPlus,
    },
    {
      key: "total" as const,
      title: "Tất cả người học",
      value: "1.2K",
      icon: Users,
    },
  ];

  // Custom tooltip component for the chart
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      const value = data.value;

      const getMetricName = () => {
        switch (statsTab) {
          case "active":
            return "người học hoạt động";
          case "new":
            return "người học mới";
          case "total":
            return "người học hoàn thành";
          default:
            return "người học";
        }
      };

      return (
        <div className="rounded-lg border border-gray-200 bg-white p-3 shadow-lg">
          <p className="font-medium text-gray-900 text-sm">
            {formatDate(label)} - {value} {getMetricName()}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="gap-2.5">
      <CardHeader>
        <h3 className="font-semibold text-lg">Thống kê người học</h3>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-12">
          {/* Main chart section */}
          <div className="lg:col-span-8">
            {/* Metric cards row with time filter */}
            <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-start sm:gap-6">
              {/* Metric cards */}
              <div className="grid flex-1 grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
                {metrics.map((metric) => {
                  const isActive = statsTab === metric.key;
                  return (
                    <div
                      key={metric.key}
                      onClick={() => setStatsTab(metric.key)}
                      className={`cursor-pointer p-3 transition-all ${isActive ? "rounded-lg bg-blue-50 shadow-sm" : " border-r"}`}
                    >
                      <div className="mb-2 flex items-center justify-between">
                        <span
                          className={`title-base ${isActive ? "font-semibold text-blue-500" : ""}`}
                        >
                          {metric.title}
                        </span>
                      </div>
                      <div className="title-28-bold">{metric.value}</div>
                    </div>
                  );
                })}
              </div>

              {/* Time filter */}
              <div className="flex justify-end sm:justify-start">
                <Select
                  value={statsTimeFilter}
                  onValueChange={(value) =>
                    setStatsTimeFilter(value as TimeFilter)
                  }
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7days">7 ngày</SelectItem>
                    <SelectItem value="30days">30 ngày</SelectItem>
                    <SelectItem value="90days">90 ngày</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Learning trend chart */}
            <div className="h-48 sm:h-56 lg:h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={learningTrendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(value) => formatDate(value)}
                    stroke="#64748b"
                    fontSize={12}
                    tickMargin={10}
                    // Hide labels on very small screens to prevent overlap
                    className="mt-2 hidden sm:block"
                  />
                  <YAxis fontSize={12} tickMargin={10} />
                  <Tooltip content={<CustomTooltip />} />
                  <Line
                    type="monotone"
                    dataKey={
                      statsTab === "active"
                        ? "active"
                        : statsTab === "new"
                          ? "enrolled"
                          : "completed"
                    }
                    stroke="#2563eb"
                    strokeWidth={2}
                    dot={{ fill: "#2563eb", strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Sidebar section */}
          <div className="lg:col-span-4">
            <Card className="h-full">
              <CardContent className="flex h-full flex-col gap-8">
                <div className="flex flex-col border-muted-separator border-b pb-5">
                  <div className="mb-3 flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                    <span className="font-medium text-base uppercase">
                      Người học hoạt động gần đây
                    </span>
                    <Select defaultValue="7days">
                      <SelectTrigger className="h-8 w-full text-xs sm:w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="7days">7 ngày</SelectItem>
                        <SelectItem value="30days">30 ngày</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="font-bold text-xl sm:text-2xl">1.2K</div>
                </div>

                <div className="flex-1">
                  <div className="mb-3 flex items-center justify-between">
                    <div className="font-semibold text-base">
                      Theo phòng ban
                    </div>
                    <Select defaultValue="all">
                      <SelectTrigger className="w-fit border-none px-0 font-semibold text-base shadow-none outline-none focus-visible:ring-0">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Tất cả người học</SelectItem>
                        <SelectItem value="active">
                          Người học hoạt động
                        </SelectItem>
                        <SelectItem value="new">Người học mới</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    {departmentStats.slice(0, 5).map((dept) => (
                      <div key={dept.name} className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="truncate pr-2 text-sm">
                            {dept.name}
                          </span>
                          <span className="flex-shrink-0 font-medium text-sm">
                            {Math.floor(dept.employees * 0.4)}
                          </span>
                        </div>
                        <Progress
                          value={dept.completion}
                          className="h-[3px] bg-muted-separator [&>[data-slot='progress-indicator']]:bg-[#2463EB]"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
