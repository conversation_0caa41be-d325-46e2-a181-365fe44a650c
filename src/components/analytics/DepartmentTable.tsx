import { Bar<PERSON>hart3 } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { m } from "@/paraglide/messages.js";
import type { DepartmentStats } from "@/types/analytics";

interface DepartmentTableProps {
  departmentStats: DepartmentStats[];
}

/**
 * Component for displaying department performance statistics in a table format
 * Shows employees count, completion rate, average score, XP, and study time per department
 */
export function DepartmentTable({ departmentStats }: DepartmentTableProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-cyan-800" />
          <CardTitle className="text-base">
            {m["analytics.departmentStats"]()}
          </CardTitle>
        </div>
        <p className="text-muted-foreground text-sm">
          {m["analytics.departmentStatsSubtitle"]()}
        </p>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="px-4 py-3">
                {m["analytics.department"]()}
              </TableHead>
              <TableHead className="px-4 py-3">
                {m["analytics.employees"]()}
              </TableHead>
              <TableHead className="px-4 py-3">
                {m["analytics.completionPercent"]()}
              </TableHead>
              <TableHead className="py-3 pr-4 pl-12 text-center">
                {m["analytics.progress"]()}
              </TableHead>
              <TableHead className="px-4 py-3 text-center">
                {m["analytics.avgXP"]()}
              </TableHead>
              <TableHead className="px-4 py-3 text-center">
                {m["analytics.avgTime"]()}
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {departmentStats.map((dept) => (
              <TableRow key={dept.name}>
                <TableCell className="px-4 py-3 font-medium">
                  {dept.name}
                </TableCell>
                <TableCell className="px-4 py-3">
                  {dept.employees} người
                </TableCell>
                <TableCell className="px-4 py-3">
                  <div className="flex items-center gap-2 ">
                    <div className="h-2 flex-1 rounded-full bg-gray-200">
                      <div
                        className="h-2 rounded-full bg-green-500"
                        style={{ width: `${dept.completion}%` }}
                      ></div>
                    </div>
                    <span className="font-medium text-sm">
                      {dept.completion}%
                    </span>
                  </div>
                </TableCell>
                <TableCell className="py-3 pr-4 pl-12 text-center">
                  {dept.avgScore}/100
                </TableCell>
                <TableCell className="px-4 py-3 text-center">
                  {dept.avgXP} XP
                </TableCell>
                <TableCell className="px-4 py-3 text-center">
                  {dept.avgTime}h
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
