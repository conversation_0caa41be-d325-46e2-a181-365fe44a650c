import { useNavigate } from "@tanstack/react-router";
import { Activity, BookOpen, CheckCircle } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { RecentActivity } from "@/types/analytics";

interface RecentActivitiesProps {
  activities: RecentActivity[];
}

/**
 * Component for displaying recent learning activities
 * Shows user completions, enrollments, and certificate achievements
 */
export function RecentActivities({ activities }: RecentActivitiesProps) {
  const navigate = useNavigate();
  // Helper function to get icon based on activity type
  const getActivityIcon = (type: RecentActivity["type"]) => {
    switch (type) {
      case "completion":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "enrollment":
        return <BookOpen className="h-5 w-5 text-blue-600" />;
      case "certificate":
        return <Activity className="h-5 w-5 text-purple-600" />;
      default:
        return <Activity className="h-5 w-5 text-gray-600" />;
    }
  };

  // Handle navigation to user details
  const handleUserClick = (userId: string) => {
    navigate({ to: "/dashboard/users/$userId", params: { userId } });
  };

  // Handle navigation to course details
  const handleCourseClick = (courseId: string) => {
    navigate({
      to: "/dashboard/courses/$courseSlug",
      params: { courseSlug: courseId },
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base">Hoạt động gần đây</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div
              key={activity.id}
              className="flex items-center gap-4 rounded-lg border p-3"
            >
              <Avatar>
                <AvatarFallback>
                  {activity.userName.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>

              {/* Activity details */}
              <div className="flex-1">
                <p className="text-sm">
                  <button
                    type="button"
                    onClick={() => handleUserClick?.(activity.userId)}
                    className="cursor-pointer font-medium text-blue-600 hover:text-blue-800"
                  >
                    {activity.userName}
                  </button>
                  <span className="text-muted-foreground">
                    {" "}
                    {activity.action}{" "}
                  </span>
                  <button
                    type="button"
                    onClick={() => handleCourseClick?.(activity.courseId)}
                    className="cursor-pointer font-medium text-blue-600 hover:text-blue-800"
                  >
                    {activity.courseName}
                  </button>
                </p>
                <p className="text-muted-foreground text-xs">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
