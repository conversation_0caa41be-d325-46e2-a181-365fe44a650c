import { <PERSON><PERSON><PERSON>, CheckCircle } from "lucide-react";
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
} from "recharts";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { LearningTrendData } from "@/types/analytics";
import formatDate from "@/utils/format-date";

interface CourseStatsProps {
  learningTrendData: LearningTrendData[];
}

/**
 * Component for displaying course statistics with completion metrics and trends
 * Shows assigned courses, completed courses, and learning trend visualization
 */
export function CourseStats({ learningTrendData }: CourseStatsProps) {
  return (
    <Card className="gap-2.5">
      <CardHeader>
        <div className="flex items-center gap-2">
          <h3 className="font-semibold text-lg">Th<PERSON>ng kê khóa học</h3>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-12">
          {/* Main chart section */}
          <div className="lg:col-span-8">
            {/* Course metric cards row with time filter */}
            <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-start sm:gap-6">
              {/* Course metric cards */}
              <div className="grid flex-1 grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="border-gray-200 border-r p-4">
                  <span className="title-base">Khóa học phần công</span>
                  <div className="title-28-bold">256</div>
                </div>
                <div className="border-gray-200 border-r p-4">
                  <span className="title-base">Khóa học hoàn thành</span>
                  <div className="title-28-bold">168</div>
                </div>
              </div>

              {/* Time filter */}
              <div className="flex justify-end sm:justify-start">
                <Select defaultValue="30days">
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7days">7 ngày</SelectItem>
                    <SelectItem value="30days">30 ngày</SelectItem>
                    <SelectItem value="90days">90 ngày</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Course completion trend chart */}
            <div className="h-48 sm:h-56 lg:h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={learningTrendData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    stroke="#64748b"
                    fontSize={12}
                    tickFormatter={(value) => formatDate(value)}
                    tickMargin={10}
                    // Hide labels on very small screens to prevent overlap
                    className="hidden sm:block"
                  />
                  <YAxis yAxisId="left" fontSize={12} tickMargin={10} />
                  <YAxis
                    yAxisId="right"
                    orientation="right"
                    fontSize={12}
                    tickMargin={10}
                  />
                  <Tooltip />
                  <Line
                    yAxisId="left"
                    type="monotone"
                    dataKey="completed"
                    stroke="#2563eb"
                    strokeWidth={2}
                    name="Khóa học hoàn thành"
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="assigned"
                    stroke="#dc2626"
                    strokeWidth={2}
                    name="Tỷ lệ hoàn thành (%)"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Sidebar section with course metrics */}
          <div className="lg:col-span-4">
            <Card className="h-full">
              <CardContent className="flex h-full flex-col gap-4 p-4">
                {/* Average study time section */}
                <div className="flex-1">
                  {/* Header */}
                  <div className="mb-3 flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                    <span className="title-base">Thời gian học trung bình</span>
                    <Select defaultValue="30days">
                      <SelectTrigger className="h-8 w-full text-xs sm:w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30days">30 ngày</SelectItem>
                        <SelectItem value="90days">90 ngày</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="mb-1 font-bold text-xl sm:text-2xl">
                    1h36p
                  </div>
                </div>

                {/* Completion rate section */}
                <div className="flex-1 border-t pt-4">
                  {/* Header */}
                  <div className="mb-3 flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                    <span className="title-base">Tỷ lệ hoàn thành</span>
                    <Select defaultValue="30days">
                      <SelectTrigger className="h-8 w-full text-xs sm:w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30days">30 ngày</SelectItem>
                        <SelectItem value="90days">90 ngày</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="mb-1 font-bold text-xl sm:text-2xl">
                    65.6%
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
