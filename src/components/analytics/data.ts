// Sample data for analytics components based on example.tsx

import type {
  CourseData,
  DailyActivity,
  DepartmentActivity,
  DepartmentStats,
  LearningTrendData,
  RecentActivity,
  TopPerformer,
} from "@/types/analytics";

// Learning trend data for charts
export const learningTrendData: LearningTrendData[] = [
  { date: "2025-01-01", completed: 45, enrolled: 60, active: 52, assigned: 55 },
  { date: "2025-01-02", completed: 52, enrolled: 65, active: 58, assigned: 60 },
  { date: "2025-01-03", completed: 48, enrolled: 70, active: 62, assigned: 65 },
  { date: "2025-01-04", completed: 65, enrolled: 75, active: 68, assigned: 70 },
  { date: "2025-01-05", completed: 72, enrolled: 80, active: 75, assigned: 75 },
  { date: "2025-01-06", completed: 78, enrolled: 85, active: 82, assigned: 80 },
];

// Department statistics data
export const departmentStats: DepartmentStats[] = [
  {
    name: "<PERSON><PERSON> th<PERSON><PERSON><PERSON>",
    employees: 45,
    completion: 87,
    avgScore: 85,
    avgXP: 540,
    avgTime: 28.5,
  },
  {
    name: "Marketing",
    employees: 23,
    completion: 82,
    avgScore: 89,
    avgXP: 520,
    avgTime: 24.1,
  },
  {
    name: "Nhân sự",
    employees: 12,
    completion: 91,
    avgScore: 94,
    avgXP: 580,
    avgTime: 31.2,
  },
  {
    name: "Bán hàng",
    employees: 34,
    completion: 76,
    avgScore: 85,
    avgXP: 480,
    avgTime: 22.8,
  },
  {
    name: "Tài chính",
    employees: 18,
    completion: 89,
    avgScore: 91,
    avgXP: 560,
    avgTime: 26.7,
  },
];

// Daily activity data for active users chart
export const dailyActivityData: DailyActivity[] = [
  { day: 1, users: 32 },
  { day: 2, users: 28 },
  { day: 3, users: 35 },
  { day: 4, users: 42 },
  { day: 5, users: 38 },
  { day: 6, users: 45 },
  { day: 7, users: 41 },
  { day: 8, users: 39 },
  { day: 9, users: 47 },
  { day: 10, users: 44 },
  { day: 11, users: 36 },
  { day: 12, users: 33 },
  { day: 13, users: 40 },
  { day: 14, users: 38 },
];

// Department activity breakdown
export const departmentActivityData: DepartmentActivity[] = [
  { department: "Kỹ thuật", users: 18 },
  { department: "Marketing", users: 12 },
  { department: "Bán hàng", users: 17 },
];

// Popular courses data
export const popularCoursesData: CourseData[] = [
  {
    id: "1",
    name: "React Fundamentals",
    students: 324,
    tags: "Phát triển",
    numberOfLessons: 10,
  },
  {
    id: "2",
    name: "JavaScript Mastery",
    students: 298,
    tags: "Phát triển",
    numberOfLessons: 10,
  },
  {
    id: "3",
    name: "UI/UX Design",
    students: 267,
    tags: "Thiết kế",
    numberOfLessons: 10,
  },
  {
    id: "4",
    name: "Python Basics",
    students: 245,
    tags: "Phát triển",
    numberOfLessons: 10,
  },
  {
    id: "5",
    name: "Data Analysis",
    students: 223,
    tags: "Phát triển",
    numberOfLessons: 10,
  },
  {
    id: "6",
    name: "Node.js Advanced",
    students: 198,
    tags: "Phát triển",
    numberOfLessons: 10,
  },
  {
    id: "7",
    name: "Machine Learning",
    students: 187,
    tags: "Phát triển",
    numberOfLessons: 10,
  },
  {
    id: "8",
    name: "Digital Marketing",
    students: 176,
    tags: "Marketing",
    numberOfLessons: 10,
  },
  {
    id: "9",
    name: "Project Management",
    students: 165,
    tags: "Quản lý",
    numberOfLessons: 10,
  },
  {
    id: "10",
    name: "Cybersecurity",
    students: 154,
    tags: "Bảo mật",
    numberOfLessons: 10,
  },
  {
    id: "11",
    name: "Cloud Computing",
    students: 143,
    tags: "Cloud Computing",
    numberOfLessons: 10,
  },
  {
    id: "12",
    name: "Mobile Development",
    students: 132,
    tags: "Phát triển",
    numberOfLessons: 10,
  },
  {
    id: "13",
    name: "DevOps Fundamentals",
    students: 121,
    tags: "Phát triển",
    numberOfLessons: 10,
  },
  {
    id: "14",
    name: "Database Design",
    students: 110,
    tags: "Phát triển",
    numberOfLessons: 10,
  },
  {
    id: "15",
    name: "API Development",
    students: 98,
    tags: "Phát triển",
    numberOfLessons: 10,
  },
  {
    id: "16",
    name: "Testing & QA",
    students: 87,
    tags: "Phát triển",
    numberOfLessons: 10,
  },
  {
    id: "17",
    name: "Blockchain Basics",
    students: 76,
    tags: "Phát triển",
    numberOfLessons: 10,
  },
  {
    id: "18",
    name: "AI Ethics",
    students: 65,
    tags: "Phát triển",
    numberOfLessons: 10,
  },
];

// Top performers data
export const topPerformersData: TopPerformer[] = [
  {
    name: "Nguyễn Văn A",
    department: "Kỹ thuật",
    xp: 95,
    courses: 8,
    avatar: "NA",
    id: 1,
  },
  {
    name: "Trần Thị B",
    department: "Marketing",
    xp: 92,
    courses: 6,
    avatar: "TB",
    id: 2,
  },
  {
    name: "Lê Minh C",
    department: "Tài chính",
    xp: 91,
    courses: 7,
    avatar: "LC",
    id: 3,
  },
  {
    name: "Phạm Thị D",
    department: "Nhân sự",
    xp: 89,
    courses: 5,
    avatar: "PD",
    id: 4,
  },
  {
    name: "Hoàng Văn E",
    department: "Bán hàng",
    xp: 87,
    courses: 6,
    avatar: "HE",
    id: 5,
  },
  {
    name: "Vũ Thị F",
    department: "Kỹ thuật",
    xp: 86,
    courses: 4,
    avatar: "VF",
    id: 6,
  },
  {
    name: "Đỗ Văn G",
    department: "Marketing",
    xp: 85,
    courses: 5,
    avatar: "DG",
    id: 7,
  },
  {
    name: "Bùi Thị H",
    department: "Tài chính",
    xp: 84,
    courses: 7,
    avatar: "BH",
    id: 8,
  },
  {
    name: "Ngô Văn I",
    department: "Bán hàng",
    xp: 83,
    courses: 3,
    avatar: "NI",
    id: 9,
  },
  {
    name: "Lý Thị K",
    department: "Nhân sự",
    xp: 82,
    courses: 6,
    avatar: "LK",
    id: 10,
  },
  {
    name: "Phan Văn L",
    department: "Kỹ thuật",
    xp: 81,
    courses: 5,
    avatar: "PL",
    id: 11,
  },
  {
    name: "Võ Thị M",
    department: "Marketing",
    xp: 80,
    courses: 4,
    avatar: "VM",
    id: 12,
  },
  {
    name: "Đinh Văn N",
    department: "Tài chính",
    xp: 79,
    courses: 6,
    avatar: "DN",
    id: 13,
  },
  {
    name: "Mai Thị O",
    department: "Nhân sự",
    xp: 78,
    courses: 3,
    avatar: "MO",
    id: 14,
  },
  {
    name: "Trương Văn P",
    department: "Bán hàng",
    xp: 77,
    courses: 5,
    avatar: "TP",
    id: 15,
  },
  {
    name: "Lưu Thị Q",
    department: "Kỹ thuật",
    xp: 76,
    courses: 4,
    avatar: "LQ",
    id: 16,
  },
  {
    name: "Hồ Văn R",
    department: "Marketing",
    xp: 75,
    courses: 7,
    avatar: "HR",
    id: 17,
  },
  {
    name: "Chu Thị S",
    department: "Tài chính",
    xp: 74,
    courses: 3,
    avatar: "CS",
    id: 18,
  },
];

// Recent activities data
export const recentActivitiesData: RecentActivity[] = [
  {
    id: 1,
    type: "completion",
    userName: "Nguyễn Văn A",
    userId: "1",
    courseName: "Digital Marketing Cơ bản",
    courseId: "1",
    time: "2 giờ trước",
    action: "đã hoàn thành khóa học",
  },
  {
    id: 2,
    type: "enrollment",
    userName: "Trần Thị B",
    userId: "2",
    courseName: "Quản lý Dự án Agile",
    courseId: "2",
    time: "4 giờ trước",
    action: "đã đăng ký khóa học",
  },
  {
    id: 3,
    type: "certificate",
    userName: "Lê Minh C",
    userId: "3",
    courseName: "Data Analysis với Excel",
    courseId: "3",
    time: "6 giờ trước",
    action: "đã đạt chứng chỉ",
  },
  {
    id: 4,
    type: "completion",
    userName: "Phạm Thị D",
    userId: "4",
    courseName: "Python Fundamentals",
    courseId: "4",
    time: "1 ngày trước",
    action: "đã hoàn thành khóa học",
  },
  {
    id: 5,
    type: "enrollment",
    userName: "Hoàng Văn E",
    userId: "5",
    courseName: "Machine Learning Basics",
    courseId: "5",
    time: "1 ngày trước",
    action: "đã đăng ký khóa học",
  },
];
