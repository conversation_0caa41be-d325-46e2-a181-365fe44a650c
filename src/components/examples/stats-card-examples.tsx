import {
  IconBookOpen,
  IconTrendingDown,
  IconTrendingUp,
  IconUsers,
} from "@tabler/icons-react";
import {
  CourseStatsCard,
  RevenueStatsCard,
  StatsCard,
  UserStatsCard,
} from "@/components/ui/stats-card";

export function StatsCardExamples() {
  return (
    <div className="grid @5xl/main:grid-cols-4 @xl/main:grid-cols-2 grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card *:data-[slot=card]:shadow-xs lg:px-6 dark:*:data-[slot=card]:bg-card">
      {/* Using the preset RevenueStatsCard */}
      <RevenueStatsCard
        value="$1,250.00"
        change="+12.5%"
        changeIcon={<IconTrendingUp />}
      />

      {/* Using the preset UserStatsCard */}
      <UserStatsCard
        value="1,234"
        change="+8.3%"
        changeIcon={<IconUsers className="size-4" />}
        subtitle="New users in the last 30 days"
      />

      {/* Using the preset CourseStatsCard */}
      <CourseStatsCard
        value="45"
        change="+15.2%"
        changeIcon={<IconBookOpen className="size-4" />}
        subtitle="Active courses this month"
      />

      {/* Custom StatsCard with declining trend */}
      <StatsCard
        title="New Customers"
        value="1,234"
        badge={{
          variant: "outline",
          icon: <IconTrendingDown />,
          text: "-20%",
        }}
        footer={{
          title: "Trending down this month",
          subtitle: "Customer acquisition in the last 30 days",
          icon: <IconTrendingDown className="size-4" />,
        }}
      />

      {/* Simple StatsCard without badge or footer */}
      <StatsCard
        title="Total Sales"
        value="8,450"
        description="All time sales count"
      />

      {/* StatsCard with custom badge variant */}
      <StatsCard
        title="Active Users"
        value="2,847"
        badge={{
          variant: "secondary",
          text: "Live",
        }}
        footer={{
          title: "Currently online",
          subtitle: "Users active in the last 5 minutes",
        }}
      />

      {/* StatsCard with destructive badge */}
      <StatsCard
        title="Failed Payments"
        value="12"
        badge={{
          variant: "destructive",
          text: "Critical",
        }}
        footer={{
          title: "Requires attention",
          subtitle: "Payment failures in the last 24 hours",
        }}
      />

      {/* Minimal StatsCard */}
      <StatsCard
        title="Completion Rate"
        value="94.5%"
        footer={{
          title: "Excellent performance",
          subtitle: "Course completion rate this quarter",
        }}
      />
    </div>
  );
}

// Usage in a dashboard
export function DashboardStats() {
  return (
    <section className="space-y-6">
      <div>
        <h2 className="font-bold text-2xl">Dashboard Overview</h2>
        <p className="text-muted-foreground">
          Key metrics and performance indicators
        </p>
      </div>

      <div className="grid @5xl/main:grid-cols-4 @xl/main:grid-cols-2 grid-cols-1 gap-4">
        <RevenueStatsCard
          value="$45,231.00"
          change="+20.1%"
          changeIcon={<IconTrendingUp />}
          period="compared to last month"
        />

        <StatsCard
          title="Active Students"
          value="2,350"
          badge={{
            variant: "outline",
            icon: <IconTrendingUp />,
            text: "+12.5%",
          }}
          footer={{
            title: "Growing steadily",
            subtitle: "Enrolled students this semester",
            icon: <IconUsers className="size-4" />,
          }}
        />

        <StatsCard
          title="Course Completion"
          value="87.2%"
          badge={{
            variant: "secondary",
            text: "Target: 85%",
          }}
          footer={{
            title: "Above target",
            subtitle: "Average completion rate",
          }}
        />

        <StatsCard
          title="Satisfaction Score"
          value="4.8/5.0"
          badge={{
            variant: "outline",
            icon: <IconTrendingUp />,
            text: "+0.3",
          }}
          footer={{
            title: "Excellent feedback",
            subtitle: "Student satisfaction rating",
          }}
        />
      </div>
    </section>
  );
}
