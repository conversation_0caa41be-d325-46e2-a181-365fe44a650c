import { use<PERSON>tom, useAtomValue, useSet<PERSON>tom } from "jotai";
import {
  addComponentTo<PERSON>one<PERSON><PERSON>,
  addSectionToSlide<PERSON>tom,
  addSlide<PERSON>tom,
  changeSectionLayout<PERSON>tom,
  closePicker<PERSON>tom,
  currentSlide<PERSON>tom,
  currentSlideIndexAtom,
  // Drag & Drop atoms
  draggedComponent<PERSON>tom,
  dragOver<PERSON>tom,
  dragOverZoneAtom,
  editingComponent<PERSON>tom,
  endDragAtom,
  lesson<PERSON>tom,
  performDragDropAtom,
  pickerPosition<PERSON>tom,
  previewMode<PERSON>tom,
  removeComponent<PERSON>rom<PERSON>one<PERSON>tom,
  removeSectionFromSlide<PERSON>tom,
  removeSlide<PERSON>tom,
  // UI interaction atoms
  selectedZone<PERSON>tom,
  selectZone<PERSON>tom,
  showFloating<PERSON>icker<PERSON>tom,
  startDragAtom,
  // Action atoms
  updateComponentContentAtom,
  updateLesson<PERSON>tom,
} from "@/store/layout-builder";
// Types are used in the hook return type, keeping import for clarity

/**
 * Custom hook for managing shared lesson state with Jo<PERSON>
 * Only includes state that needs to be shared across multiple components
 */
export const useJotaiLessonState = () => {
  // Core lesson data (shared across many components)
  const lesson = useAtomValue(lessonAtom);
  const currentSlide = useAtomValue(currentSlideAtom);
  const [currentSlideIndex, setCurrentSlideIndex] = useAtom(
    currentSlideIndexAtom,
  );

  // Shared UI state (used by multiple components)
  const [previewMode, setPreviewMode] = useAtom(previewModeAtom);
  const [editingComponent, setEditingComponent] = useAtom(editingComponentAtom);

  // Drag & Drop state
  const draggedComponent = useAtomValue(draggedComponentAtom);
  const dragOverZone = useAtomValue(dragOverZoneAtom);

  // UI interaction state
  const selectedZone = useAtomValue(selectedZoneAtom);
  const showFloatingPicker = useAtomValue(showFloatingPickerAtom);
  const pickerPosition = useAtomValue(pickerPositionAtom);

  // Action setters
  const updateLesson = useSetAtom(updateLessonAtom);
  const addComponentToZone = useSetAtom(addComponentToZoneAtom);
  const addSectionToSlide = useSetAtom(addSectionToSlideAtom);
  const changeSectionLayout = useSetAtom(changeSectionLayoutAtom);
  const updateComponentContent = useSetAtom(updateComponentContentAtom);
  const selectZone = useSetAtom(selectZoneAtom);
  const closePicker = useSetAtom(closePickerAtom);
  const startDrag = useSetAtom(startDragAtom);
  const dragOver = useSetAtom(dragOverAtom);
  const endDrag = useSetAtom(endDragAtom);
  const performDragDrop = useSetAtom(performDragDropAtom);

  // New operations
  const removeComponentFromZone = useSetAtom(removeComponentFromZoneAtom);
  const removeSectionFromSlide = useSetAtom(removeSectionFromSlideAtom);
  const addSlide = useSetAtom(addSlideAtom);
  const removeSlide = useSetAtom(removeSlideAtom);

  return {
    // Core data
    lesson,
    currentSlide,
    currentSlideIndex,
    setCurrentSlideIndex,

    // Shared UI state
    previewMode,
    setPreviewMode,
    editingComponent,
    setEditingComponent,

    // Drag & Drop state
    draggedComponent,
    dragOverZone,

    // UI interaction state
    selectedZone,
    showFloatingPicker,
    pickerPosition,

    // Actions
    updateLesson,
    addComponentToZone,
    addSectionToSlide,
    changeSectionLayout,
    updateComponentContent,
    selectZone,
    closePicker,
    startDrag,
    dragOver,
    endDrag,
    performDragDrop,

    // New operations
    removeComponentFromZone,
    removeSectionFromSlide,
    addSlide,
    removeSlide,
  };
};
