import { Plus, Tag } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ModuleBadgeProps {
  moduleName?: string | null;
  isUnmoduled?: boolean;
  onAddToModule?: () => void;
  className?: string;
}

export const ModuleBadge = ({
  moduleName,
  isUnmoduled = false,
  onAddToModule,
  className,
}: ModuleBadgeProps) => {
  if (!moduleName || isUnmoduled) {
    return (
      <div className={cn("flex items-center gap-1", className)}>
        <Badge variant="outline" className="border-gray-300 text-gray-500">
          <Tag className="mr-1 h-3 w-3" />
          Không module
        </Badge>
        {onAddToModule && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onAddToModule}
            className="h-6 px-2 text-blue-600 text-xs hover:bg-blue-50 hover:text-blue-700"
          >
            <Plus className="mr-1 h-3 w-3" />
            Thêm vào module
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className={cn("flex items-center gap-1", className)}>
      <Badge
        variant="secondary"
        className="border-blue-200 bg-blue-50 text-blue-700"
      >
        <Tag className="mr-1 h-3 w-3" />
        {moduleName}
      </Badge>
      {onAddToModule && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onAddToModule}
          className="h-6 px-2 text-blue-600 text-xs hover:bg-blue-50 hover:text-blue-700"
        >
          <Plus className="mr-1 h-3 w-3" />
          Đổi module
        </Button>
      )}
    </div>
  );
};
