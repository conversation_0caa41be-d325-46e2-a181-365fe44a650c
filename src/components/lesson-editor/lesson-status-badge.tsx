import { Archive, CheckCircle, Clock } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface LessonStatusBadgeProps {
  publishedAt?: string | null;
  archivedAt?: string | null;
  className?: string;
}

export const LessonStatusBadge = ({
  publishedAt,
  archivedAt,
  className,
}: LessonStatusBadgeProps) => {
  if (archivedAt) {
    return (
      <Badge
        variant="outline"
        className={cn("border-gray-300 bg-gray-100 text-gray-700", className)}
      >
        <Archive className="mr-1 h-3 w-3" />
        Đ<PERSON> lưu trữ
      </Badge>
    );
  }

  if (publishedAt) {
    return (
      <Badge
        variant="outline"
        className={cn(
          "border-green-200 bg-green-100 text-green-800",
          className,
        )}
      >
        <CheckCircle className="mr-1 h-3 w-3" />
        <PERSON><PERSON> xu<PERSON><PERSON> bản
      </Badge>
    );
  }

  return (
    <Badge
      variant="outline"
      className={cn(
        "border-yellow-200 bg-yellow-100 text-yellow-800",
        className,
      )}
    >
      <Clock className="mr-1 h-3 w-3" />
      Bản nháp
    </Badge>
  );
};
