import { useForm } from "@tanstack/react-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  BookOpen,
  Clock,
  Eye,
  EyeOff,
  Hash,
  Plus,
  Save,
  Star,
  Tag,
  Type,
  X,
} from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { createLesson, updateLesson } from "@/services/instructor";
import type { UUID } from "@/types/app";
import type { Lesson } from "@/types/lessons";

interface LessonFormProps {
  courseId: string;
  lesson?: Lesson;
  existingGroups: string[];
  existingLessons: Lesson[];
  open: boolean;
  onClose: () => void;
  onSuccess?: (lesson: Lesson) => void;
}

export const LessonForm = ({
  courseId,
  lesson,
  existingGroups,
  existingLessons,
  open,
  onClose,
  onSuccess,
}: LessonFormProps) => {
  const queryClient = useQueryClient();
  const isEditing = !!lesson;

  const [newGroupName, setNewGroupName] = useState("");
  const [showNewGroupInput, setShowNewGroupInput] = useState(false);
  const [dynamicGroups, setDynamicGroups] = useState<string[]>([]);

  const initialValues = useMemo(
    () => ({
      name: lesson?.name || "",
      slug: lesson?.slug || "",
      description: lesson?.description || "",
      group: lesson?.group || "",
      duration: lesson?.duration || 10,
      points: lesson?.points || 10,
      isPublished: !!lesson?.published_at,
    }),
    [lesson],
  );

  // Combined groups list (existing + dynamically created)
  const allGroups = useMemo(() => {
    const combined = [...existingGroups, ...dynamicGroups];
    return Array.from(new Set(combined)).sort();
  }, [existingGroups, dynamicGroups]);

  const form = useForm({
    defaultValues: initialValues,
    validators: {
      onSubmit: ({ value }) => {
        const errors: Record<string, string> = {};

        if (!value.name || value.name.length < 2) {
          errors.name = "Tên bài học phải có ít nhất 2 ký tự";
        }
        if (!value.slug || value.slug.length < 2) {
          errors.slug = "Slug phải có ít nhất 2 ký tự";
        }

        return Object.keys(errors).length > 0 ? errors : undefined;
      },
    },
    onSubmit: async ({ value }) => {
      // Use the group from form value directly
      let finalGroup = value.group;

      // If user is creating new group and hasn't confirmed yet, use the new group name
      if (showNewGroupInput && newGroupName.trim() && !value.group) {
        finalGroup = newGroupName.trim();
      }

      // Calculate ordinal_index automatically
      const maxOrdinalIndex =
        existingLessons.length > 0
          ? Math.max(...existingLessons.map((l) => l.ordinal_index || 0))
          : 0;
      const nextOrdinalIndex = isEditing
        ? lesson?.ordinal_index || 1
        : maxOrdinalIndex + 1;

      const lessonData: Lesson = {
        ...(lesson || {}),
        id: lesson?.id || (crypto.randomUUID() as UUID),
        course_id: courseId as UUID,
        name: value.name,
        slug: value.slug,
        description: value.description,
        group: finalGroup || null,
        duration: value.duration,
        points: value.points,
        ordinal_index: nextOrdinalIndex,
        published_at: value.isPublished
          ? lesson?.published_at || new Date().toISOString()
          : null,
        archived_at: lesson?.archived_at || null,
      };

      if (isEditing && lesson?.id) {
        updateLessonMutation.mutate({ id: lesson.id, lesson: lessonData });
        return;
      }

      createLessonMutation.mutate({ lesson: lessonData });
    },
  });

  // Reset form when lesson changes
  useEffect(() => {
    if (open) {
      form.reset(initialValues);
    }
  }, [initialValues, open, form]);

  // Reset group states when dialog opens/closes
  useEffect(() => {
    if (!open) {
      // Reset states when dialog closes
      setNewGroupName("");
      setShowNewGroupInput(false);
      return;
    }

    // Reset group-related states when dialog opens
    setNewGroupName("");
    setShowNewGroupInput(false);
  }, [open]);
  const createLessonMutation = useMutation({
    mutationFn: createLesson,
    onSuccess: (data) => {
      toast.success("Bài học đã được tạo thành công");

      queryClient.setQueryData(
        ["lessons", courseId],
        (oldData: Lesson[] | undefined) => {
          if (!oldData) return [data];
          return [...oldData, data];
        },
      );

      // Then invalidate to ensure consistency
      queryClient.invalidateQueries({
        queryKey: ["lessons"],
      });

      onSuccess?.(data);
      onClose();
    },
    onError: (error) => {
      toast.error("Không thể tạo bài học. Vui lòng thử lại.");
    },
  });

  const updateLessonMutation = useMutation({
    mutationFn: ({ id, lesson: lessonData }: { id: string; lesson: Lesson }) =>
      updateLesson({ id, lesson: lessonData }),
    onSuccess: (data) => {
      toast.success("Bài học đã được cập nhật thành công");

      // Optimistic update: update the lesson in cache immediately
      queryClient.setQueryData(
        ["lessons", courseId],
        (oldData: Lesson[] | undefined) => {
          if (!oldData) return [data];
          return oldData.map((lesson) =>
            lesson.id === data.id ? data : lesson,
          );
        },
      );

      // Then invalidate to ensure consistency
      queryClient.invalidateQueries({
        queryKey: ["lessons"],
      });

      onSuccess?.(data);
      onClose();
    },
    onError: (error) => {
      toast.error("Không thể cập nhật bài học. Vui lòng thử lại.");
    },
  });

  const isLoading =
    createLessonMutation.isPending || updateLessonMutation.isPending;

  // Check if form is valid for submission
  const isFormValid = useMemo(() => {
    const values = form.state.values;

    // Only check required fields: name and slug
    const hasValidName = values.name && values.name.length >= 2;
    const hasValidSlug = values.slug && values.slug.length >= 2;

    // Basic validation passes if name and slug are valid
    return hasValidName && hasValidSlug;
  }, [form.state.values]);

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent
        className="max-h-[90vh] overflow-y-auto sm:max-w-4xl"
        onPointerDownOutside={(e) => isLoading && e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-blue-600" />
            {isEditing ? "Chỉnh sửa bài học" : "Tạo bài học mới"}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Cập nhật thông tin bài học"
              : "Tạo bài học mới cho khóa học này"}
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="space-y-6"
        >
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Type className="h-4 w-4 text-gray-500" />
                <h3 className="font-semibold text-gray-900 text-sm">
                  Thông tin cơ bản
                </h3>
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <form.Field
                  name="name"
                  validators={{
                    onChange: ({ value }) =>
                      !value || value.length < 2
                        ? "Tên bài học phải có ít nhất 2 ký tự"
                        : undefined,
                  }}
                  children={(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="name">
                        Tên bài học <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="name"
                        name={field.name}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => {
                          field.handleChange(e.target.value);
                          // Auto-generate slug from name for new lessons
                          if (!isEditing) {
                            const slug = e.target.value
                              .toLowerCase()
                              .normalize("NFD")
                              .replace(/[\u0300-\u036f]/g, "")
                              .replace(/[đĐ]/g, "d")
                              .replace(/[^a-z0-9\s]/g, "")
                              .trim()
                              .replace(/\s+/g, "-");
                            form.setFieldValue("slug", slug);
                          }
                        }}
                        placeholder="Nhập tên bài học"
                        className="focus:border-blue-500 focus:ring-blue-500"
                      />
                      {field.state.meta.errors.length > 0 && (
                        <p className="text-red-600 text-sm">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                    </div>
                  )}
                />

                <form.Field
                  name="slug"
                  validators={{
                    onChange: ({ value }) =>
                      !value || value.length < 2
                        ? "Slug phải có ít nhất 2 ký tự"
                        : undefined,
                  }}
                  children={(field) => (
                    <div className="space-y-2">
                      <Label htmlFor="slug">
                        Slug <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="slug"
                        name={field.name}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="lesson-slug"
                        className="font-mono text-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                      {field.state.meta.errors.length > 0 && (
                        <p className="text-red-600 text-sm">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                    </div>
                  )}
                />
              </div>

              <form.Field
                name="description"
                children={(field) => (
                  <div className="space-y-2">
                    <Label htmlFor="description">Mô tả</Label>
                    <Textarea
                      id="description"
                      name={field.name}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      placeholder="Nhập mô tả bài học"
                      rows={3}
                      className="resize-none focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                )}
              />
            </div>

            <Separator />

            {/* Group Management */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Tag className="h-4 w-4 text-gray-500" />
                <h3 className="font-semibold text-gray-900 text-sm">
                  Module bài học{" "}
                  <span className="font-normal text-gray-400 text-xs">
                    (Tùy chọn)
                  </span>
                </h3>
              </div>

              <form.Field
                name="group"
                children={(field) => (
                  <div className="space-y-3">
                    {/* Main Group Selection */}
                    <div className="flex gap-2">
                      <div className="flex-1">
                        <Select
                          value={field.state.value || ""}
                          onValueChange={(value) => {
                            if (value === "__none__") {
                              field.handleChange("");
                            } else {
                              field.handleChange(value);
                            }
                            setShowNewGroupInput(false);
                          }}
                        >
                          <SelectTrigger className="focus:border-blue-500 focus:ring-blue-500">
                            <SelectValue placeholder="Chọn module hoặc để trống">
                              {field.state.value ? (
                                <div className="flex items-center gap-2">
                                  <Tag className="h-3 w-3" />
                                  {field.state.value}
                                </div>
                              ) : (
                                "Chọn module hoặc để trống"
                              )}
                            </SelectValue>
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="__none__">
                              <div className="flex items-center gap-2 text-gray-500">
                                <Tag className="h-3 w-3" />
                                Không module
                              </div>
                            </SelectItem>
                            {allGroups.length > 0 && (
                              <>
                                <div className="px-2 py-1.5 font-semibold text-gray-500 text-xs uppercase tracking-wide">
                                  Module có sẵn
                                </div>
                                {allGroups.map((group) => (
                                  <SelectItem key={group} value={group}>
                                    <div className="flex items-center gap-2">
                                      <Tag className="h-3 w-3" />
                                      {group}
                                    </div>
                                  </SelectItem>
                                ))}
                              </>
                            )}
                          </SelectContent>
                        </Select>
                      </div>

                      <Button
                        type="button"
                        variant="outline"
                        size="default"
                        onClick={() => setShowNewGroupInput(!showNewGroupInput)}
                        className="px-3 shadow-sm hover:border-blue-300 hover:bg-blue-50 hover:text-blue-700"
                      >
                        Tạo module
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* New Group Input - Collapsible */}
                    {showNewGroupInput && (
                      <div className="space-y-2 rounded-lg border border-blue-200 bg-blue-50 p-4">
                        <Label
                          htmlFor="newGroup"
                          className="font-medium text-blue-900"
                        >
                          Tạo module mới
                        </Label>
                        <div className="flex gap-2">
                          <Input
                            id="newGroup"
                            value={newGroupName}
                            onChange={(e) => setNewGroupName(e.target.value)}
                            placeholder="Nhập tên module mới"
                            className="flex-1 bg-white focus:border-blue-500 focus:ring-blue-500"
                          />
                          <Button
                            type="button"
                            size="sm"
                            onClick={() => {
                              if (!newGroupName.trim()) return;

                              const trimmedName = newGroupName.trim();
                              // Add to dynamic groups first if not already exists
                              if (!allGroups.includes(trimmedName)) {
                                setDynamicGroups((prev) => [
                                  ...prev,
                                  trimmedName,
                                ]);
                              }
                              // Then set the form field value to select the new group
                              field.handleChange(trimmedName);
                              setNewGroupName("");
                              setShowNewGroupInput(false);
                            }}
                            disabled={!newGroupName.trim()}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            Thêm
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setShowNewGroupInput(false);
                              setNewGroupName("");
                            }}
                          >
                            Hủy
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Current Selection Display */}
                    {field.state.value && (
                      <div className="fade-in-50 flex animate-in items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-3">
                        <Tag className="h-4 w-4 text-green-600" />
                        <span className="font-medium text-green-800">
                          {isEditing
                            ? "Bài học thuộc module: "
                            : "Bài học sẽ được thêm vào module: "}
                          "
                          <span className="font-semibold">
                            {field.state.value}
                          </span>
                          "
                        </span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => field.handleChange("")}
                          className="ml-auto h-6 w-6 p-0 text-green-600 hover:bg-green-100 hover:text-green-700"
                          title={isEditing ? "Xóa khỏi module" : "Xóa module"}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    )}

                    {/* Helper text */}
                    <div className="rounded border bg-gray-50 p-2 text-gray-500 text-xs">
                      💡 <strong>Mẹo:</strong> Module giúp bạn tổ chức bài học
                      theo chủ đề. Bạn có thể để trống nếu không cần module.
                    </div>
                  </div>
                )}
              />
            </div>

            <Separator />

            {/* Lesson Settings */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Hash className="h-4 w-4 text-gray-500" />
                <h3 className="font-semibold text-gray-900 text-sm">
                  Cài đặt bài học
                </h3>
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <form.Field
                  name="duration"
                  validators={{
                    onChange: ({ value }) =>
                      !value || value < 1
                        ? "Thời lượng phải lớn hơn 0"
                        : undefined,
                  }}
                  children={(field) => (
                    <div className="space-y-2">
                      <Label
                        htmlFor="duration"
                        className="flex items-center gap-1"
                      >
                        <Clock className="h-3 w-3" />
                        Thời lượng (phút)
                      </Label>
                      <Input
                        id="duration"
                        name={field.name}
                        type="number"
                        min="1"
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) =>
                          field.handleChange(parseInt(e.target.value))
                        }
                        className="focus:border-blue-500 focus:ring-blue-500"
                      />
                      {field.state.meta.errors.length > 0 && (
                        <p className="text-red-600 text-sm">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                    </div>
                  )}
                />

                <form.Field
                  name="points"
                  validators={{
                    onChange: ({ value }) =>
                      value < 0 ? "Điểm thưởng không thể âm" : undefined,
                  }}
                  children={(field) => (
                    <div className="space-y-2">
                      <Label
                        htmlFor="points"
                        className="flex items-center gap-1"
                      >
                        <Star className="h-3 w-3" />
                        Điểm thưởng
                      </Label>
                      <Input
                        id="points"
                        name={field.name}
                        type="number"
                        min="0"
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) =>
                          field.handleChange(parseInt(e.target.value))
                        }
                        className="focus:border-blue-500 focus:ring-blue-500"
                      />
                      {field.state.meta.errors.length > 0 && (
                        <p className="text-red-600 text-sm">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                    </div>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Publish Status */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Eye className="h-4 w-4 text-gray-500" />
                <h3 className="font-semibold text-gray-900 text-sm">
                  Trạng thái xuất bản
                </h3>
              </div>

              <form.Field
                name="isPublished"
                children={(field) => (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between rounded-lg border border-gray-200 p-4">
                      <div className="flex items-center gap-3">
                        {field.state.value ? (
                          <Eye className="h-5 w-5 text-green-600" />
                        ) : (
                          <EyeOff className="h-5 w-5 text-gray-400" />
                        )}
                        <div>
                          <Label
                            htmlFor="isPublished"
                            className="font-medium text-gray-900"
                          >
                            {field.state.value
                              ? "Đã xuất bản"
                              : "Chưa xuất bản"}
                          </Label>
                          <p className="text-gray-500 text-sm">
                            {field.state.value
                              ? "Bài học này có thể được học viên truy cập"
                              : "Bài học này chỉ có thể nhìn thấy bởi Người tạo khóa"}
                          </p>
                        </div>
                      </div>
                      <Switch
                        id="isPublished"
                        checked={field.state.value}
                        onCheckedChange={(checked) =>
                          field.handleChange(checked)
                        }
                      />
                    </div>

                    {/* Show publish info if editing and published */}
                    {isEditing && lesson?.published_at && field.state.value && (
                      <div className="rounded-lg border border-green-200 bg-green-50 p-3">
                        <div className="flex items-center gap-2 text-green-800">
                          <Eye className="h-4 w-4" />
                          <span className="font-medium text-sm">
                            Đã xuất bản từ:{" "}
                            {new Date(lesson.published_at).toLocaleDateString(
                              "vi-VN",
                              {
                                year: "numeric",
                                month: "long",
                                day: "numeric",
                                hour: "2-digit",
                                minute: "2-digit",
                              },
                            )}
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Helper text */}
                    <div className="rounded border bg-gray-50 p-2 text-gray-500 text-xs">
                      💡 <strong>Mẹo:</strong> Bạn có thể tạo bài học và để ở
                      trạng thái "Chưa xuất bản" để chuẩn bị nội dung trước khi
                      công khai.
                    </div>
                  </div>
                )}
              />
            </div>
          </div>

          <div className="flex justify-end gap-3 border-t pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Hủy
            </Button>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button
                  type="submit"
                  disabled={isLoading || !canSubmit || isSubmitting}
                >
                  {isLoading || isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      {isEditing ? "Đang cập nhật..." : "Đang tạo..."}
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Save className="h-4 w-4" />
                      {isEditing ? "Cập nhật" : "Tạo bài học"}
                    </div>
                  )}
                </Button>
              )}
            />
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
