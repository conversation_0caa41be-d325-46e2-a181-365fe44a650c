import { But<PERSON> } from "@/components/ui/button";
import { COMPONENT_DESCRIPTIONS, componentTypes } from "../constants";
import type { LayoutComponent, PickerPosition, SelectedZone } from "../types";

interface FloatingComponentPickerProps {
  isVisible: boolean;
  selectedZone: SelectedZone | null;
  position: PickerPosition;
  onAddComponent: (
    sectionId: string,
    zoneId: string,
    type: LayoutComponent["type"],
  ) => void;
  onClose: () => void;
}

export const FloatingComponentPicker = ({
  isVisible,
  selectedZone,
  position,
  onAddComponent,
  onClose,
}: FloatingComponentPickerProps) => {
  if (!isVisible || !selectedZone) return null;

  return (
    <>
      {/* Backdrop to close picker */}
      <div className="fixed inset-0 z-40 bg-transparent" onClick={onClose} />

      {/* Floating Picker */}
      <div
        className="fade-in zoom-in-95 fixed z-50 w-64 animate-in rounded-lg border border-gray-300 bg-white shadow-2xl duration-200"
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
        }}
      >
        <div className="rounded-t-lg border-gray-100 border-b bg-gray-50 p-4">
          <h3 className="font-semibold text-gray-900 text-sm">Add Component</h3>
          <p className="mt-1 text-gray-500 text-xs">
            Click a component to add it to this zone
          </p>
        </div>

        <div className="max-h-72 overflow-y-auto p-2">
          <div className="space-y-1">
            {componentTypes.map(({ type, icon: Icon, label }) => (
              <Button
                key={type}
                variant="ghost"
                className="h-auto w-full justify-start border border-transparent p-3 transition-colors hover:border-blue-200 hover:bg-blue-50"
                onClick={() => {
                  onAddComponent(
                    selectedZone.sectionId,
                    selectedZone.zoneId,
                    type as LayoutComponent["type"],
                  );
                  onClose();
                }}
              >
                <Icon className="mr-3 h-4 w-4 text-gray-700" />
                <div className="flex-1 text-left">
                  <div className="font-medium text-gray-900 text-sm">
                    {label}
                  </div>
                  <div className="mt-0.5 text-gray-500 text-xs">
                    {
                      COMPONENT_DESCRIPTIONS[
                        type as keyof typeof COMPONENT_DESCRIPTIONS
                      ]
                    }
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </div>

        <div className="rounded-b-lg border-gray-100 border-t bg-gray-50 p-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-full text-gray-600 hover:text-gray-900"
          >
            Cancel
          </Button>
        </div>
      </div>
    </>
  );
};
