import { Plus } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { componentTypes } from "../constants";
import type {
  DraggedComponent,
  DragOverZone,
  LayoutComponent,
  LayoutSection,
  LayoutZone,
  PreviewMode,
} from "../types";
import { ComponentRenderer } from "./ComponentRenderer";

interface ZoneRendererProps {
  section: LayoutSection;
  zone: LayoutZone;
  zoneIndex: number;
  previewMode: PreviewMode;
  onZoneClick: (
    event: React.MouseEvent,
    sectionId: string,
    zoneId: string,
  ) => void;
  onAddComponent: (
    sectionId: string,
    zoneId: string,
    type: LayoutComponent["type"],
  ) => void;
  onDeleteComponent: (
    sectionId: string,
    zoneId: string,
    componentId: string,
  ) => void;
  onUpdateComponent: (
    sectionId: string,
    zoneId: string,
    componentId: string,
    content: string,
  ) => void;
  onToggleEdit: (componentId: string) => void;
  editingComponent: string | null;
  draggedComponent: DraggedComponent | null;
  dragOverZone: DragOverZone | null;
  onDragStart: (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    componentId: string,
    index: number,
  ) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDragOverWithFeedback: (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    index: number,
  ) => void;
  onDrop: (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    index: number,
  ) => void;
  onDragEnd: () => void;
  onDragLeave: (e: React.DragEvent) => void;
}

export const ZoneRenderer = ({
  section,
  zone,
  zoneIndex,
  previewMode,
  onZoneClick,
  onAddComponent,
  onDeleteComponent,
  onUpdateComponent,
  onToggleEdit,
  editingComponent,
  draggedComponent,
  dragOverZone,
  onDragStart,
  onDragOver,
  onDragOverWithFeedback,
  onDrop,
  onDragEnd,
  onDragLeave,
}: ZoneRendererProps) => {
  if (zone.components.length === 0) {
    return (
      <div
        className={`group/empty-zone relative min-h-[140px] rounded-lg border-2 border-gray-300 border-dashed bg-gray-50/30 transition-all duration-300 hover:border-blue-300 hover:bg-blue-50/50 ${
          dragOverZone?.sectionId === section.id &&
          dragOverZone?.zoneId === zone.id &&
          dragOverZone?.index === 0
            ? "border-blue-500 bg-blue-50"
            : ""
        }`}
        onDragOver={onDragOver}
        onDragEnter={(e) => onDragOverWithFeedback(e, section.id, zone.id, 0)}
        onDrop={(e) => onDrop(e, section.id, zone.id, 0)}
        onDragLeave={onDragLeave}
      >
        {/* Default state */}
        <div className="flex min-h-[140px] items-center justify-center py-6 transition-opacity duration-200 group-hover/empty-zone:hidden">
          <div className="flex flex-col items-center">
            <Plus className="mb-2 h-5 w-5 text-gray-400" />
            <p className="font-medium text-gray-600 text-sm">
              Zone {zoneIndex + 1}
            </p>
            <p className="text-gray-500 text-xs">Hover to add components</p>
          </div>
        </div>

        {/* Hover state - component buttons */}
        <div className="fade-in-0 slide-in-from-bottom-2 hidden h-[140px] animate-in flex-wrap items-center justify-center gap-2 p-3 duration-200 group-hover/empty-zone:flex">
          {componentTypes.map(({ type, icon: Icon }) => (
            <Button
              key={type}
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onAddComponent(
                  section.id,
                  zone.id,
                  type as LayoutComponent["type"],
                );
              }}
              className="h-7 px-2 text-xs transition-colors duration-300 hover:border-blue-400 hover:bg-blue-50"
              style={{
                animationDelay: `${
                  componentTypes.findIndex((ct) => ct.type === type) * 50
                }ms`,
              }}
            >
              <Icon className="mr-1 h-3 w-3" />
              {type === "text"
                ? "Text"
                : type === "image"
                  ? "Image"
                  : type === "video"
                    ? "Video"
                    : type === "card"
                      ? "Card"
                      : type === "chart"
                        ? "Chart"
                        : type === "game"
                          ? "Game"
                          : "Quiz"}
            </Button>
          ))}
        </div>
      </div>
    );
  }

  // Hide empty zones in preview mode
  if (previewMode && zone.components.length === 0) {
    return null;
  }

  return (
    <div className={previewMode === false ? "space-y-3" : "space-y-1"}>
      {/* {previewMode === false && (
        <div className="flex items-center justify-start px-2">
          <Badge
            variant="outline"
            className="border-gray-300 text-gray-700 text-xs"
          >
            Zone {zoneIndex + 1} ({zone.components.length} components)
          </Badge>
        </div>
      )} */}

      <div className={previewMode === false ? "space-y-2" : "space-y-4"}>
        {zone.components.map((component, componentIndex) => (
          <div key={component.id}>
            {previewMode === false && (
              <div
                className={`h-4 rounded-md border-2 border-dashed transition-all duration-200 ${
                  dragOverZone?.sectionId === section.id &&
                  dragOverZone?.zoneId === zone.id &&
                  dragOverZone?.index === componentIndex
                    ? "h-8 border-blue-500 bg-blue-50"
                    : "border-transparent hover:border-gray-300 hover:bg-gray-100"
                }`}
                onDragOver={onDragOver}
                onDragEnter={(e) =>
                  onDragOverWithFeedback(e, section.id, zone.id, componentIndex)
                }
                onDrop={(e) => onDrop(e, section.id, zone.id, componentIndex)}
                onDragLeave={onDragLeave}
              >
                {dragOverZone?.sectionId === section.id &&
                  dragOverZone?.zoneId === zone.id &&
                  dragOverZone?.index === componentIndex && (
                    <div className="flex h-full items-center justify-center font-medium text-blue-600 text-xs">
                      Drop here
                    </div>
                  )}
              </div>
            )}

            <ComponentRenderer
              component={component}
              componentIndex={componentIndex}
              sectionId={section.id}
              zoneId={zone.id}
              previewMode={previewMode}
              isEditing={editingComponent === component.id}
              isDragged={draggedComponent?.componentId === component.id}
              onDeleteComponent={onDeleteComponent}
              onUpdateComponent={onUpdateComponent}
              onToggleEdit={onToggleEdit}
              onDragStart={onDragStart}
              onDragEnd={onDragEnd}
            />
          </div>
        ))}
      </div>

      {/* Add component area at bottom of zone */}
      {previewMode === false && (
        <div
          className="group/add-area relative mt-4 min-h-[80px] rounded-lg border-2 border-gray-200 border-dashed bg-gray-50/30 transition-all duration-300 hover:border-blue-300 hover:bg-blue-50/50"
          onClick={(e) => onZoneClick(e, section.id, zone.id)}
        >
          {/* Default state - shows on no hover */}
          <div className="flex min-h-[80px] items-center justify-center transition-opacity duration-200 group-hover/add-area:hidden">
            <div className="flex items-center gap-2 text-gray-600 opacity-60">
              <Plus className="h-4 w-4" />
              <span className="font-medium text-sm">Add Component</span>
            </div>
          </div>

          {/* Hover state - shows component buttons inline */}
          <div className="fade-in-0 slide-in-from-bottom-2 hidden min-h-[80px] animate-in flex-wrap items-center justify-center gap-2 p-4 duration-200 group-hover/add-area:flex">
            {componentTypes.map(({ type, icon: Icon }, buttonIndex) => (
              <Button
                key={type}
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onAddComponent(
                    section.id,
                    zone.id,
                    type as LayoutComponent["type"],
                  );
                }}
                className="h-7 px-2 text-xs transition-colors duration-300 hover:border-blue-400 hover:bg-blue-50"
                style={{ animationDelay: `${buttonIndex * 50}ms` }}
              >
                <Icon className="mr-1 h-3 w-3" />
                {type === "text"
                  ? "Text"
                  : type === "image"
                    ? "Image"
                    : type === "video"
                      ? "Video"
                      : type === "card"
                        ? "Card"
                        : type === "chart"
                          ? "Chart"
                          : type === "game"
                            ? "Game"
                            : "Quiz"}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
