import { LayoutGrid, Plus, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import type { Lesson, Slide } from "../types";
import { countComponentsInSlide } from "../utils";

interface LessonSidebarProps {
  lesson: Lesson;
  currentSlide: Slide;
  currentSlideIndex: number;
  onSlideSelect: (index: number) => void;
  onAddSlide: () => void;
  onDeleteSlide: (index: number) => void;
}

export const LessonSidebar = ({
  lesson,
  currentSlide,
  currentSlideIndex,
  onSlideSelect,
  onAddSlide,
  onDeleteSlide,
}: LessonSidebarProps) => {
  return (
    <aside className="fixed top-[90px] left-0 z-10 flex h-[calc(100vh-80px)] w-80 flex-col overflow-hidden border-gray-200 border-r bg-white">
      {/* Slide Navigation */}
      <div className="border-gray-200 border-b p-4">
        <div className="mb-3 flex items-center justify-between">
          <h2 className="font-semibold text-gray-900 text-lg">Slides</h2>
          <Button
            variant="outline"
            size="sm"
            onClick={onAddSlide}
            className="h-8"
          >
            <Plus className="mr-1 h-4 w-4" />
            New Slide
          </Button>
        </div>

        {/* Slide List */}
        <div className="max-h-[calc(60dvh)] space-y-2 overflow-y-auto">
          {lesson.slides.map((slide, index) => (
            <div
              key={slide.id}
              className={`group cursor-pointer rounded-lg border-2 p-3 transition-all ${
                index === currentSlideIndex
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 bg-gray-50 hover:border-gray-300 hover:bg-gray-100"
              }`}
              onClick={() => onSlideSelect(index)}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-1 font-medium text-gray-900 text-sm">
                    <LayoutGrid className="h-3 w-3" />
                    Slide {index + 1}
                  </div>
                  <div className="text-gray-500 text-xs">
                    {slide.sections.length} section
                    {slide.sections.length !== 1 ? "s" : ""}
                  </div>
                </div>

                {lesson.slides.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteSlide(index);
                    }}
                    className="h-6 w-6 p-0 text-red-600 opacity-0 hover:bg-red-50 hover:text-red-800 group-hover:opacity-100"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Instructions & Overview */}
      <div className="flex flex-1 flex-col overflow-hidden p-4">
        <div className="space-y-4">
          {/* Instructions */}
          <div className="rounded-lg border border-blue-200 bg-blue-50 p-3">
            <h4 className="mb-1 font-medium text-blue-900 text-sm">
              💡 How to add content
            </h4>
            <p className="text-blue-700 text-xs">
              Click the "Add Component" area at the bottom of zones to add
              content. Drag components to reorder.
            </p>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="mt-auto border-gray-200 border-t pt-4">
          <h4 className="mb-3 font-medium text-gray-700 text-xs uppercase tracking-wide">
            Current Slide Overview
          </h4>
          <div className="grid grid-cols-3 gap-2 text-center">
            <div>
              <div className="font-bold text-blue-600 text-lg">
                {lesson.slides.length}
              </div>
              <div className="text-gray-500 text-xs">Total Slides</div>
            </div>
            <div>
              <div className="font-bold text-green-600 text-lg">
                {currentSlide.sections.length}
              </div>
              <div className="text-gray-500 text-xs">Sections</div>
            </div>
            <div>
              <div className="font-bold text-lg text-purple-600">
                {countComponentsInSlide(currentSlide)}
              </div>
              <div className="text-gray-500 text-xs">Components</div>
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
};
