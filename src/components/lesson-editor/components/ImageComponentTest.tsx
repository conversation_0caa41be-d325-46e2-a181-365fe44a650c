import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { SimpleImageComponent } from "./SimpleImageComponent";

/**
 * Test component để verify logic của ImageComponent
 */
export function ImageComponentTest() {
  const [content, setContent] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const scenarios = [
    {
      name: "New Component (Empty + Editing)",
      content: "",
      isEditing: true,
      isPreviewMode: false,
      description: "Khi tạo component mới - nên hiển thị uploader",
    },
    {
      name: "Empty + Not Editing",
      content: "",
      isEditing: false,
      isPreviewMode: false,
      description: "Component rỗng không editing - nên hiển thị empty state",
    },
    {
      name: "Has Image + Editing",
      content: "https://picsum.photos/400/300",
      isEditing: true,
      isPreviewMode: false,
      description: "Có ảnh và đang edit - nên hiển thị controls",
    },
    {
      name: "Has Image + Display Mode",
      content: "https://picsum.photos/400/300",
      isEditing: false,
      isPreviewMode: false,
      description:
        "Có ảnh không edit - nên hiển thị ảnh với hover edit + button cố định",
    },
    {
      name: "Small Image + Display Mode",
      content: "https://picsum.photos/150/100",
      isEditing: false,
      isPreviewMode: false,
      description: "Ảnh nhỏ - nên có button edit cố định dễ click",
    },
    {
      name: "Has Image + Preview Mode",
      content: "https://picsum.photos/400/300",
      isEditing: false,
      isPreviewMode: true,
      description: "Preview mode - chỉ hiển thị ảnh",
    },
  ];

  const runScenario = (scenario: (typeof scenarios)[0]) => {
    setContent(scenario.content);
    setIsEditing(scenario.isEditing);
    setIsPreviewMode(scenario.isPreviewMode);
  };

  return (
    <div className="mx-auto max-w-6xl space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Image Component Logic Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current State */}
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h3 className="mb-4 font-semibold text-lg">Current State</h3>
              <div className="space-y-2 text-sm">
                <p>
                  <strong>Content:</strong> {content || "(empty)"}
                </p>
                <p>
                  <strong>Is Editing:</strong> {isEditing ? "Yes" : "No"}
                </p>
                <p>
                  <strong>Is Preview Mode:</strong>{" "}
                  {isPreviewMode ? "Yes" : "No"}
                </p>
              </div>

              <div className="mt-4 space-y-2">
                <h4 className="font-medium">Manual Controls:</h4>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant={isEditing ? "default" : "outline"}
                    onClick={() => setIsEditing(!isEditing)}
                  >
                    {isEditing ? "Stop Edit" : "Start Edit"}
                  </Button>
                  <Button
                    size="sm"
                    variant={isPreviewMode ? "default" : "outline"}
                    onClick={() => setIsPreviewMode(!isPreviewMode)}
                  >
                    {isPreviewMode ? "Exit Preview" : "Preview"}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setContent("")}
                  >
                    Clear
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setContent("https://picsum.photos/400/300")}
                  >
                    Set Image
                  </Button>
                </div>
              </div>
            </div>

            <div>
              <h3 className="mb-4 font-semibold text-lg">Test Scenarios</h3>
              <div className="space-y-2">
                {scenarios.map((scenario, index) => (
                  <div key={index} className="rounded border p-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-medium text-sm">{scenario.name}</h4>
                        <p className="mt-1 text-gray-600 text-xs">
                          {scenario.description}
                        </p>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => runScenario(scenario)}
                      >
                        Test
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Component Display */}
          <div>
            <h3 className="mb-4 font-semibold text-lg">Component Output</h3>
            <div className="min-h-[300px] rounded-lg border bg-gray-50 p-4">
              <SimpleImageComponent
                content={content}
                isPreviewMode={isPreviewMode}
                isEditing={isEditing}
                onUpdate={(newContent) => {
                  setContent(newContent);
                }}
                onToggleEdit={() => {
                  setIsEditing(!isEditing);
                }}
                onDelete={() => {
                  setContent("");
                }}
              />
            </div>
          </div>

          {/* Expected Behavior */}
          <div>
            <h3 className="mb-4 font-semibold text-lg">Expected Behavior</h3>
            <div className="space-y-2 text-sm">
              <p>
                <strong>✅ New Component (Empty + Editing):</strong> Should show
                ImageUploader
              </p>
              <p>
                <strong>✅ Empty + Not Editing:</strong> Should show empty state
                with "Add Image" button
              </p>
              <p>
                <strong>✅ Has Image + Editing:</strong> Should show image with
                change/remove controls
              </p>
              <p>
                <strong>✅ Has Image + Display:</strong> Should show image with
                hover edit button
              </p>
              <p>
                <strong>✅ Has Image + Preview:</strong> Should show only image,
                no controls
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
