import { Al<PERSON><PERSON><PERSON>gle, <PERSON><PERSON><PERSON>, Grid, Layers } from "lucide-react";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
// import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { layoutTemplates } from "../constants";
import type { LayoutSection, LayoutTemplate } from "../types";

export type DataMigrationOption =
  | "distribute" // Đưa data vào các zone tương ứng
  | "first-zone" // Cho hết vào zone đầu tiên
  | "discard"; // Hủy bỏ hết data

interface LayoutChangeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentSection: LayoutSection;
  onLayoutChange: (
    newTemplate: LayoutTemplate,
    migrationOption: DataMigrationOption,
  ) => void;
}

export function LayoutChangeDialog({
  open,
  onOpenChange,
  currentSection,
  onLayoutChange,
}: LayoutChangeDialogProps) {
  const [selectedTemplate, setSelectedTemplate] =
    useState<LayoutTemplate | null>(null);
  const [migrationOption, setMigrationOption] =
    useState<DataMigrationOption>("distribute");

  const hasExistingData = currentSection.zones.some(
    (zone) => zone.components.length > 0,
  );
  const totalComponents = currentSection.zones.reduce(
    (total, zone) => total + zone.components.length,
    0,
  );

  const handleConfirm = () => {
    if (!selectedTemplate) return;

    onLayoutChange(selectedTemplate, migrationOption);
    onOpenChange(false);
    setSelectedTemplate(null);
    setMigrationOption("distribute");
  };

  const handleCancel = () => {
    onOpenChange(false);
    setSelectedTemplate(null);
    setMigrationOption("distribute");
  };

  const getPreviewText = () => {
    if (!selectedTemplate || !hasExistingData) return "";

    const newZones = selectedTemplate.zones.length;

    switch (migrationOption) {
      case "distribute":
        return `Distribute ${totalComponents} components across ${newZones} zones. Extra components go to first zone.`;
      case "first-zone":
        return `Move all ${totalComponents} components to the first zone of new layout.`;
      case "discard":
        return `Delete all ${totalComponents} components and start fresh.`;
      default:
        return "";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Grid className="h-5 w-5" />
            Change Section Layout
          </DialogTitle>
          <DialogDescription>
            Choose a new layout template for this section.
            {hasExistingData &&
              " Your existing content will be migrated based on your choice below."}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Layout Template Selection */}
          <div className="space-y-3">
            <Label className="font-medium text-sm">Select New Layout</Label>
            <Select
              value={selectedTemplate?.id || ""}
              onValueChange={(value) => {
                const template = layoutTemplates.find((t) => t.id === value);
                setSelectedTemplate(template || null);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose a layout template" />
              </SelectTrigger>
              <SelectContent>
                {layoutTemplates.map((template) => (
                  <SelectItem key={template.id} value={template.id}>
                    <div className="flex items-center gap-2">
                      <Layers className="h-4 w-4" />
                      <span>{template.name}</span>
                      <span className="text-gray-500 text-xs">
                        ({template.zones.length} zones)
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Current vs New Layout Preview */}
          {selectedTemplate && (
            <div className="rounded-lg border bg-gray-50 p-4">
              <div className="flex items-center justify-between">
                <div className="text-center">
                  <div className="font-medium text-sm">Current Layout</div>
                  <div className="text-gray-600 text-xs">
                    {currentSection.template.name} (
                    {currentSection.zones.length} zones)
                  </div>
                  <div className="text-gray-500 text-xs">
                    {totalComponents} components
                  </div>
                </div>
                <ArrowRight className="h-5 w-5 text-gray-400" />
                <div className="text-center">
                  <div className="font-medium text-sm">New Layout</div>
                  <div className="text-gray-600 text-xs">
                    {selectedTemplate.name} ({selectedTemplate.zones.length}{" "}
                    zones)
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Data Migration Options */}
          {hasExistingData && selectedTemplate && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-amber-500" />
                <Label className="font-medium text-sm">
                  What to do with existing content? ({totalComponents}{" "}
                  components)
                </Label>
              </div>

              <div className="space-y-3">
                <div className="flex items-start space-x-2">
                  <input
                    type="radio"
                    value="distribute"
                    id="distribute"
                    name="migration"
                    checked={migrationOption === "distribute"}
                    onChange={(e) =>
                      setMigrationOption(e.target.value as DataMigrationOption)
                    }
                    className="mt-1"
                  />
                  <div className="space-y-1">
                    <Label htmlFor="distribute" className="font-medium text-sm">
                      Smart Distribution (Recommended)
                    </Label>
                    <p className="text-gray-600 text-xs">
                      Distribute components to corresponding zones. Extra
                      components go to first zone.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-2">
                  <input
                    type="radio"
                    value="first-zone"
                    id="first-zone"
                    name="migration"
                    checked={migrationOption === "first-zone"}
                    onChange={(e) =>
                      setMigrationOption(e.target.value as DataMigrationOption)
                    }
                    className="mt-1"
                  />
                  <div className="space-y-1">
                    <Label htmlFor="first-zone" className="font-medium text-sm">
                      Move All to First Zone
                    </Label>
                    <p className="text-gray-600 text-xs">
                      Put all existing components in the first zone of new
                      layout.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-2">
                  <input
                    type="radio"
                    value="discard"
                    id="discard"
                    name="migration"
                    checked={migrationOption === "discard"}
                    onChange={(e) =>
                      setMigrationOption(e.target.value as DataMigrationOption)
                    }
                    className="mt-1"
                  />
                  <div className="space-y-1">
                    <Label
                      htmlFor="discard"
                      className="font-medium text-red-600 text-sm"
                    >
                      Discard All Content
                    </Label>
                    <p className="text-gray-600 text-xs">
                      Delete all existing components and start with empty
                      layout.
                    </p>
                  </div>
                </div>
              </div>

              {/* Preview of migration */}
              <div className="rounded border bg-blue-50 p-3">
                <div className="font-medium text-blue-800 text-xs">
                  Preview:
                </div>
                <div className="text-blue-700 text-xs">{getPreviewText()}</div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!selectedTemplate}
            variant={migrationOption === "discard" ? "destructive" : "default"}
          >
            {migrationOption === "discard"
              ? "Change Layout & Delete Content"
              : "Change Layout"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
