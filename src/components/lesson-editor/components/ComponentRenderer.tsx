import {
  <PERSON><PERSON><PERSON><PERSON>,
  CardSelector,
  CardTemplate,
} from "@aicademy/shared-card-templates";
import {
  Edit3,
  Gamepad2,
  GripVertical,
  HelpCircle,
  Trash2,
} from "lucide-react";
import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import BeautyEditor from "../tiptap-editor";
import type { LayoutComponent, PreviewMode } from "../types";
import { convertTiptapJsonToHtml } from "../utils";
import { ImageComponent } from "./ImageComponent";
import { VideoComponent } from "./VideoComponent";

interface ComponentRendererProps {
  component: LayoutComponent;
  componentIndex: number;
  sectionId: string;
  zoneId: string;
  previewMode: PreviewMode;
  isEditing: boolean;
  isDragged: boolean;
  onDeleteComponent: (
    sectionId: string,
    zoneId: string,
    componentId: string,
  ) => void;
  onUpdateComponent: (
    sectionId: string,
    zoneId: string,
    componentId: string,
    content: string | object,
  ) => void;
  onToggleEdit: (componentId: string) => void;
  onDragStart: (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    componentId: string,
    index: number,
  ) => void;
  onDragEnd: () => void;
}

export const ComponentRenderer = ({
  component,
  componentIndex,
  sectionId,
  zoneId,
  previewMode,
  isEditing,
  isDragged,
  onDeleteComponent,
  onUpdateComponent,
  onToggleEdit,
  onDragStart,
  onDragEnd,
}: ComponentRendererProps) => {
  const [showCardEditor, setShowCardEditor] = useState(false);

  const renderComponentContent = () => {
    if (
      previewMode === false &&
      isEditing &&
      (component.type === "text" || component.type === "card")
    ) {
      // Ensure we have valid JSON content for BeautyEditor
      let validJsonContent = "{}";
      try {
        if (component.content && typeof component.content === "string") {
          JSON.parse(component.content); // Validate JSON
          validJsonContent = component.content;
        }
      } catch (error) {
        // If content is not valid JSON, create a default JSON structure
        validJsonContent = JSON.stringify({
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text:
                    typeof component.content === "string"
                      ? component.content
                      : "Enter your content here...",
                },
              ],
            },
          ],
        });
      }

      return (
        <div>
          <BeautyEditor
            content={validJsonContent}
            setContent={(content: string) =>
              onUpdateComponent(sectionId, zoneId, component.id, content)
            }
          />
          <Button
            onClick={() => onToggleEdit(component.id)}
            className="mt-2"
            size="sm"
          >
            Save
          </Button>
        </div>
      );
    }

    if (component.type === "game") {
      // Hide game component in preview mode (not implemented yet)
      if (previewMode) {
        return null;
      }

      return (
        <div className="rounded-lg border-2 border-green-200 bg-green-50 p-4">
          <div className="mb-3 flex items-center justify-center">
            <Gamepad2 className="mr-2 h-8 w-8 text-green-600" />
            <div>
              <div className="font-medium text-green-900">
                {component.gameData?.gameType === "memory"
                  ? "Memory Game"
                  : "Puzzle Game"}
              </div>
              <div className="text-green-700 text-sm">
                Interactive game component
              </div>
            </div>
          </div>
          <div className="flex justify-center">
            <Button
              size="sm"
              variant="outline"
              className="border-green-300 text-green-700"
            >
              Configure Game
            </Button>
          </div>
        </div>
      );
    }

    if (component.type === "quiz") {
      // Hide quiz component in preview mode (not implemented yet)
      if (previewMode) {
        return null;
      }

      return (
        <div className="rounded-lg border-2 border-purple-200 bg-purple-50 p-4">
          <div className="mb-3 flex items-center justify-center">
            <HelpCircle className="mr-2 h-8 w-8 text-purple-600" />
            <div>
              <div className="font-medium text-purple-900">
                {component.quizData?.quizType === "multiple-choice"
                  ? "Multiple Choice Quiz"
                  : "Quiz"}
              </div>
              <div className="text-purple-700 text-sm">
                Interactive quiz component
              </div>
            </div>
          </div>
          <div className="flex justify-center">
            <Button
              size="sm"
              variant="outline"
              className="border-purple-300 text-purple-700"
            >
              Configure Quiz
            </Button>
          </div>
        </div>
      );
    }

    if (component.type === "image") {
      return (
        <ImageComponent
          content={component.content as string}
          previewMode={previewMode}
          isEditing={isEditing}
          onUpdate={(content) => {
            onUpdateComponent(sectionId, zoneId, component.id, content);
          }}
          onToggleEdit={() => onToggleEdit(component.id)}
        />
      );
    }

    if (component.type === "video") {
      return (
        <VideoComponent
          content={component.content as string}
          previewMode={previewMode}
          isEditing={isEditing}
          onUpdate={(content) => {
            console.log("ComponentRenderer video onUpdate:", {
              componentId: component.id,
              content,
              sectionId,
              zoneId,
            });
            onUpdateComponent(sectionId, zoneId, component.id, content);
          }}
          onToggleEdit={() => onToggleEdit(component.id)}
          onDelete={() => onDeleteComponent(sectionId, zoneId, component.id)}
        />
      );
    }

    // Default text/card content - render as HTML in view mode
    if (component.type === "text") {
      // Check if content is empty in preview mode
      if (previewMode) {
        const isEmpty =
          !component.content ||
          (typeof component.content === "string" &&
            component.content.trim() === "") ||
          component.content === "{}" ||
          (typeof component.content === "string" &&
            component.content.includes('"content":[]'));

        if (isEmpty) {
          return null; // Hide empty text components in preview
        }
      }

      try {
        if (component.content && typeof component.content === "string") {
          const jsonContent = JSON.parse(component.content);
          // Convert JSON content to HTML for display
          const htmlContent = convertTiptapJsonToHtml(jsonContent);
          return (
            <div
              className="prose prose-sm max-w-none"
              // biome-ignore lint/security/noDangerouslySetInnerHtml: Rich text content needs to be rendered as HTML
              dangerouslySetInnerHTML={{ __html: htmlContent }}
            />
          );
        }
      } catch (error) {
        // Fallback for non-JSON content - display as plain text
        return (
          <div className="prose prose-sm max-w-none">
            <p>
              {typeof component.content === "string"
                ? component.content
                : "No content"}
            </p>
          </div>
        );
      }
    }

    if (component.type === "card") {
      if (typeof component.content === "object" && component.content !== null) {
        const cardProps = component.content;
        return (
          <>
            <CardRenderer {...cardProps} />
            {!previewMode && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCardEditor(true)}
                className="mt-2"
              >
                Edit
              </Button>
            )}
            <Dialog open={showCardEditor} onOpenChange={setShowCardEditor}>
              <DialogContent>
                <CardSelector
                  initialParams={cardProps}
                  onCardSelect={(newCardProps) => {
                    onUpdateComponent(
                      sectionId,
                      zoneId,
                      component.id,
                      newCardProps,
                    );
                    setShowCardEditor(false);
                  }}
                  onCancel={() => setShowCardEditor(false)}
                />
              </DialogContent>
            </Dialog>
          </>
        );
      }
      return null;
    }

    return (
      <div className="prose prose-sm max-w-none">
        <p>
          {typeof component.content === "string"
            ? component.content
            : JSON.stringify(component.content)}
        </p>
      </div>
    );
  };

  return (
    <div
      className={`${component.color} ${
        previewMode === false
          ? component.size === "small"
            ? "p-4"
            : component.size === "large"
              ? "p-8"
              : "p-6"
          : "" // No padding in preview mode
      } ${
        previewMode === false
          ? "group rounded-lg border border-gray-200 transition-all duration-200 hover:shadow-md"
          : "" // No border in preview mode
      } ${isDragged ? "scale-95 opacity-50 shadow-xl" : ""}`}
    >
      <div className="flex items-start justify-between">
        {previewMode === false && (
          <div
            className="mr-3 flex cursor-grab items-center opacity-60 transition-opacity active:cursor-grabbing group-hover:opacity-100"
            draggable={true}
            onDragStart={(e) =>
              onDragStart(e, sectionId, zoneId, component.id, componentIndex)
            }
            onDragEnd={onDragEnd}
          >
            <GripVertical className="h-5 w-5 text-gray-500" />
          </div>
        )}

        <div className="flex-1">
          {previewMode === false && (
            <div className="mb-3 flex items-center gap-2">
              <Badge
                variant="outline"
                className="border-gray-300 text-gray-700 text-xs"
              >
                {component.type}
              </Badge>
              {(component.type === "text" || component.type === "card") && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onToggleEdit(component.id)}
                  className="h-6 px-2 text-gray-600 hover:text-gray-900"
                >
                  <Edit3 className="h-3 w-3" />
                </Button>
              )}
            </div>
          )}

          {renderComponentContent()}
        </div>

        {previewMode === false && (
          <div className="ml-3 flex items-start gap-1 opacity-0 transition-opacity group-hover:opacity-100">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDeleteComponent(sectionId, zoneId, component.id)}
              className="h-6 w-6 p-0 text-red-600 hover:bg-red-50 hover:text-red-800"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
