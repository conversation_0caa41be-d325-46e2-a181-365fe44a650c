import { Edit3, Image as ImageIcon, Upload } from "lucide-react";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import type { PreviewMode } from "../types";
import { ImageUploader } from "./ImageUploader";

interface ImageComponentProps {
  content: string;
  previewMode: PreviewMode;
  isEditing: boolean;
  onUpdate: (content: string) => void;
  onToggleEdit: () => void;
  className?: string;
}

export function ImageComponent({
  content,
  previewMode,
  isEditing,
  onUpdate,
  onToggleEdit,
  className,
}: ImageComponentProps) {
  const [showUploader, setShowUploader] = useState(false);

  // Parse content - could be URL string or JSON with metadata
  const getImageData = () => {
    try {
      const parsed = JSON.parse(content);
      return {
        url: parsed.url || "",
        alt: parsed.alt || "",
        caption: parsed.caption || "",
        width: parsed.width,
        height: parsed.height,
      };
    } catch {
      // If content is just a string URL
      return {
        url: content || "",
        alt: "",
        caption: "",
        width: undefined,
        height: undefined,
      };
    }
  };

  const imageData = getImageData();
  // Check if content is a real image URL (not placeholder text)
  const isPlaceholder =
    !imageData.url ||
    imageData.url.includes("placeholder") ||
    imageData.url.includes("Click to upload") ||
    imageData.url.includes("doc") ||
    imageData.url.includes("paragraph") ||
    imageData.url.includes("text") ||
    imageData.url.includes("type") ||
    imageData.url.startsWith("{") ||
    imageData.url.startsWith("[") ||
    imageData.url.length < 10;
  const hasImage = Boolean(imageData.url) && !isPlaceholder;

  const handleImageUpdate = (url: string) => {
    // If current content is simple URL string, keep it simple
    if (typeof content === "string" && !content.startsWith("{")) {
      onUpdate(url);
    } else {
      // If it's JSON or we want to add metadata
      const newImageData = {
        ...imageData,
        url,
      };
      onUpdate(JSON.stringify(newImageData));
    }

    setShowUploader(false);

    // Exit edit mode after successful upload
    if (isEditing) {
      onToggleEdit();
    }
  };

  // Show uploader when explicitly requested OR when editing with no image
  if (showUploader || (isEditing && !hasImage)) {
    return (
      <div className={cn("w-full", className)}>
        <ImageUploader
          value={imageData.url}
          onChange={handleImageUpdate}
          onCancel={() => {
            setShowUploader(false);
            if (!hasImage) {
              onToggleEdit();
            }
          }}
        />
      </div>
    );
  }

  // Preview mode - only show if has image, hide if empty
  if (previewMode) {
    if (hasImage) {
      return (
        <div className={cn("flex w-full justify-start", className)}>
          <img
            src={imageData.url}
            alt={imageData.alt || "Image"}
            className="w-auto object-contain"
            style={{ maxHeight: "400px" }}
            onError={(e) => {
              e.currentTarget.src =
                "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200' viewBox='0 0 400 200'%3E%3Crect width='400' height='200' fill='%23f3f4f6'/%3E%3Ctext x='200' y='100' text-anchor='middle' dy='.3em' fill='%236b7280' font-size='16'%3EImage not found%3C/text%3E%3C/svg%3E";
            }}
          />
        </div>
      );
    } else {
      // Hide empty image component in preview mode
      return null;
    }
  }

  // Edit mode with existing image
  if (isEditing && hasImage) {
    return (
      <div className={cn("w-full", className)}>
        <div className="flex flex-col items-center space-y-3">
          <div className="group relative">
            <img
              src={imageData.url}
              alt={imageData.alt || "Image"}
              className="h-auto w-full rounded-lg shadow-sm"
              style={{
                maxWidth: imageData.width ? `${imageData.width}px` : "600px",
                maxHeight: imageData.height ? `${imageData.height}px` : "400px",
              }}
              onError={(e) => {
                e.currentTarget.src =
                  "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200' viewBox='0 0 400 200'%3E%3Crect width='400' height='200' fill='%23f3f4f6'/%3E%3Ctext x='200' y='100' text-anchor='middle' dy='.3em' fill='%236b7280' font-size='16'%3EImage not found%3C/text%3E%3C/svg%3E";
              }}
            />
          </div>

          {/* Edit mode controls */}
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setShowUploader(true)}
            >
              <Upload className="mr-1 h-3 w-3" />
              Change Image
            </Button>
            <Button size="sm" variant="outline" onClick={onToggleEdit}>
              Done
            </Button>
          </div>

          {imageData.caption && (
            <p className="text-center text-gray-600 text-sm italic">
              {imageData.caption}
            </p>
          )}
        </div>
      </div>
    );
  }

  // Display mode with image (not editing, not preview)
  if (hasImage && !isEditing && !previewMode) {
    return (
      <div className={cn("w-full", className)}>
        <div className="flex flex-col items-center space-y-3">
          <div className="group relative">
            <img
              src={imageData.url}
              alt={imageData.alt || "Image"}
              className="h-auto w-full rounded-lg shadow-sm"
              style={{
                maxWidth: imageData.width ? `${imageData.width}px` : "600px",
                maxHeight: imageData.height ? `${imageData.height}px` : "400px",
              }}
              onError={(e) => {
                e.currentTarget.src =
                  "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200' viewBox='0 0 400 200'%3E%3Crect width='400' height='200' fill='%23f3f4f6'/%3E%3Ctext x='200' y='100' text-anchor='middle' dy='.3em' fill='%236b7280' font-size='16'%3EImage not found%3C/text%3E%3C/svg%3E";
              }}
            />
          </div>

          {/* Always visible edit button for better accessibility */}
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={onToggleEdit}
              className="text-xs"
            >
              <Edit3 className="mr-1 h-3 w-3" />
              Edit Image
            </Button>
          </div>

          {imageData.caption && (
            <p className="text-center text-gray-600 text-sm italic">
              {imageData.caption}
            </p>
          )}
        </div>
      </div>
    );
  }

  // Empty state
  return (
    <Card className={cn("w-full border-2 border-dashed", className)}>
      <CardContent className="p-8">
        <div className="space-y-4 text-center">
          <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
          <div>
            <h3 className="font-medium text-gray-900">No image selected</h3>
            <p className="mt-1 text-gray-600 text-sm">
              Upload an image or enter a URL to get started
            </p>
          </div>
          <Button onClick={() => setShowUploader(true)}>
            <Upload className="mr-2 h-4 w-4" />
            Add Image
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
