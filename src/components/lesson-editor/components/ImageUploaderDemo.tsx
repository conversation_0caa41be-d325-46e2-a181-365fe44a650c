import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ImageComponent } from "./ImageComponent";
import { ImageUploader } from "./ImageUploader";

/**
 * Demo component to showcase ImageUploader and ImageComponent
 * This can be used for testing and development
 */
export function ImageUploaderDemo() {
  const [imageUrl, setImageUrl] = useState("");
  const [showUploader, setShowUploader] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const handleImageChange = (url: string) => {
    setImageUrl(url);
    setShowUploader(false);
  };

  const handleImageUpdate = (content: string) => {
    setImageUrl(content);
  };

  const toggleEdit = () => {
    setIsEditing(!isEditing);
  };

  return (
    <div className="mx-auto max-w-4xl space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Image Upload Components Demo</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Standalone ImageUploader */}
          <div>
            <h3 className="mb-4 font-semibold text-lg">
              Standalone Image Uploader
            </h3>
            <div className="mb-4 flex gap-4">
              <Button
                onClick={() => setShowUploader(!showUploader)}
                variant={showUploader ? "secondary" : "default"}
              >
                {showUploader ? "Hide" : "Show"} Uploader
              </Button>
              <Button
                onClick={() => setImageUrl("")}
                variant="outline"
                disabled={!imageUrl}
              >
                Clear Image
              </Button>
            </div>

            {showUploader && (
              <ImageUploader
                value={imageUrl}
                onChange={handleImageChange}
                onCancel={() => setShowUploader(false)}
                placeholder="Upload your image here"
              />
            )}

            {imageUrl && !showUploader && (
              <div className="space-y-2">
                <p className="font-medium text-sm">Current Image URL:</p>
                <p className="break-all text-gray-600 text-sm">{imageUrl}</p>
                <img
                  src={imageUrl}
                  alt="Uploaded"
                  className="max-w-md rounded-lg border"
                  onError={(e) => {
                    e.currentTarget.src =
                      "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200' viewBox='0 0 400 200'%3E%3Crect width='400' height='200' fill='%23f3f4f6'/%3E%3Ctext x='200' y='100' text-anchor='middle' dy='.3em' fill='%236b7280' font-size='16'%3EImage not found%3C/text%3E%3C/svg%3E";
                  }}
                />
              </div>
            )}
          </div>

          {/* ImageComponent Demo */}
          <div>
            <h3 className="mb-4 font-semibold text-lg">
              Image Component (as used in lesson editor)
            </h3>
            <div className="mb-4 flex gap-4">
              <Button
                onClick={toggleEdit}
                variant={isEditing ? "secondary" : "default"}
              >
                {isEditing ? "Stop Editing" : "Start Editing"}
              </Button>
              <Button
                onClick={() => setImageUrl("")}
                variant="outline"
                disabled={!imageUrl}
              >
                Clear Image
              </Button>
            </div>

            <div className="rounded-lg border p-4">
              <ImageComponent
                content={imageUrl}
                isPreviewMode={false}
                isEditing={isEditing}
                onUpdate={handleImageUpdate}
                onToggleEdit={toggleEdit}
                onDelete={() => setImageUrl("")}
              />
            </div>
          </div>

          {/* Preview Mode Demo */}
          {imageUrl && (
            <div>
              <h3 className="mb-4 font-semibold text-lg">Preview Mode</h3>
              <div className="rounded-lg border bg-gray-50 p-4">
                <ImageComponent
                  content={imageUrl}
                  isPreviewMode={true}
                  isEditing={false}
                  onUpdate={handleImageUpdate}
                  onToggleEdit={toggleEdit}
                />
              </div>
            </div>
          )}

          {/* JSON Content Demo */}
          <div>
            <h3 className="mb-4 font-semibold text-lg">JSON Content Example</h3>
            <p className="mb-2 text-gray-600 text-sm">
              The ImageComponent can handle both simple URL strings and JSON
              objects with metadata:
            </p>
            <pre className="overflow-x-auto rounded bg-gray-100 p-3 text-sm">
              {`// Simple URL string
"https://example.com/image.jpg"

// JSON with metadata
{
  "url": "https://example.com/image.jpg",
  "alt": "Description of the image",
  "caption": "Image caption text",
  "width": 800,
  "height": 600
}`}
            </pre>
          </div>

          {/* Features List */}
          <div>
            <h3 className="mb-4 font-semibold text-lg">Features</h3>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="mb-2 font-medium">ImageUploader</h4>
                <ul className="space-y-1 text-gray-600 text-sm">
                  <li>• Drag & drop support</li>
                  <li>• File upload with validation</li>
                  <li>• URL input option</li>
                  <li>• File size limit (10MB)</li>
                  <li>• Image format validation</li>
                  <li>• Upload progress indicator</li>
                  <li>• Error handling</li>
                  <li>• Preview functionality</li>
                </ul>
              </div>
              <div>
                <h4 className="mb-2 font-medium">ImageComponent</h4>
                <ul className="space-y-1 text-gray-600 text-sm">
                  <li>• Edit/Preview mode support</li>
                  <li>• Hover controls overlay</li>
                  <li>• Download functionality</li>
                  <li>• External link opening</li>
                  <li>• Image replacement</li>
                  <li>• Component deletion</li>
                  <li>• JSON metadata support</li>
                  <li>• Responsive design</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
