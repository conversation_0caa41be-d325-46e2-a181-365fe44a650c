import { <PERSON><PERSON><PERSON>, Eye, Monitor, Save, Smartphone, Tablet } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { Lesson, PreviewMode } from "../types";

interface LessonHeaderProps {
  lesson: Lesson;
  previewMode: PreviewMode;
  onSetPreviewMode: (mode: PreviewMode) => void;
  onSave: () => void;
}

export const LessonHeader = ({
  lesson,
  previewMode,
  onSetPreviewMode,
  onSave,
}: LessonHeaderProps) => {
  return (
    <header className="fixed top-0 right-0 left-0 z-20 border-b bg-white shadow-sm">
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <div className="mb-2 flex items-center gap-3">
            <BookOpen className="h-6 w-6 text-blue-600" />
            <h1 className="font-bold font-sans text-2xl text-gray-900">
              {lesson.title}
            </h1>
          </div>
          <p className="font-serif text-gray-600 text-sm">
            {lesson.description}
          </p>
        </div>

        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant={previewMode ? "default" : "outline"} size="sm">
                <Eye className="mr-2 h-4 w-4" />
                {previewMode ? `Preview (${previewMode})` : "Preview"}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onSetPreviewMode(false)}>
                <Eye className="mr-2 h-4 w-4" />
                Edit Mode
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onSetPreviewMode("desktop")}>
                <Monitor className="mr-2 h-4 w-4" />
                Desktop Preview
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onSetPreviewMode("tablet")}>
                <Tablet className="mr-2 h-4 w-4" />
                Tablet Preview
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onSetPreviewMode("mobile")}>
                <Smartphone className="mr-2 h-4 w-4" />
                Mobile Preview
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button size="sm" onClick={onSave}>
            <Save className="mr-2 h-4 w-4" />
            Save Lesson
          </Button>
        </div>
      </div>
    </header>
  );
};
