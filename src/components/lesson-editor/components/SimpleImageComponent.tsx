import { Edit3, Image as ImageIcon, Upload } from "lucide-react";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { ImageUploader } from "./ImageUploader";

interface SimpleImageComponentProps {
  content: string;
  isPreviewMode: boolean;
  isEditing: boolean;
  onUpdate: (content: string) => void;
  onToggleEdit: () => void;
  onDelete?: () => void;
  className?: string;
}

// Constants
const PLACEHOLDER_KEYWORDS = ["placeholder", "Click to upload"];
const IMAGE_CONSTRAINTS = {
  maxWidth: "600px",
  maxHeight: "400px",
  editMaxHeight: "300px",
} as const;

const FALLBACK_IMAGE_SVG =
  "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200' viewBox='0 0 400 200'%3E%3Crect width='400' height='200' fill='%23f3f4f6'/%3E%3Ctext x='200' y='100' text-anchor='middle' dy='.3em' fill='%236b7280' font-size='16'%3EImage not found%3C/text%3E%3C/svg%3E";

export function SimpleImageComponent({
  content,
  isPreviewMode,
  isEditing,
  onUpdate,
  onToggleEdit,
  onDelete,
  className,
}: SimpleImageComponentProps) {
  const [showUploader, setShowUploader] = useState(false);

  // Parse and validate image URL
  const imageUrl = content?.trim() || "";
  const isPlaceholder =
    !imageUrl ||
    PLACEHOLDER_KEYWORDS.some((keyword) => imageUrl.includes(keyword)) ||
    imageUrl.includes("doc") ||
    imageUrl.includes("paragraph") ||
    imageUrl.includes("text") ||
    imageUrl.includes("type") ||
    imageUrl.startsWith("{") ||
    imageUrl.startsWith("[") ||
    imageUrl.length < 10;
  const hasImage = Boolean(imageUrl) && !isPlaceholder;

  // Event handlers
  const handleImageUpdate = (url: string) => {
    onUpdate(url);
    setShowUploader(false);
  };

  const handleCancelUpload = () => {
    setShowUploader(false);
    if (!hasImage) {
      onToggleEdit();
    }
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = FALLBACK_IMAGE_SVG;
  };

  // Debug logging (remove in production)
  if (process.env.NODE_ENV === "development") {
    console.log("SimpleImageComponent:", {
      content,
      isPreviewMode,
      isEditing,
      showUploader,
      hasImage,
      isPlaceholder,
    });
  }

  // Render uploader when explicitly requested OR when editing with no image
  if (showUploader || (isEditing && !hasImage)) {
    return (
      <div className={cn("w-full", className)}>
        <Card>
          <CardContent className="p-6">
            <h3 className="mb-4 font-semibold text-lg">Upload Image</h3>
            <ImageUploader
              value={imageUrl}
              onChange={handleImageUpdate}
              onCancel={handleCancelUpload}
            />
          </CardContent>
        </Card>
      </div>
    );
  }

  // Preview mode - show image only
  if (isPreviewMode && hasImage) {
    return (
      <div className={cn("flex w-full justify-center", className)}>
        <img
          src={imageUrl}
          alt="Lesson content"
          className="w-auto object-contain"
          style={{ maxHeight: "400px" }}
          onError={handleImageError}
        />
      </div>
    );
  }

  // Edit mode with image
  if (hasImage && isEditing) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-4">
          <div className="space-y-4">
            <img
              src={imageUrl}
              alt="Editing content"
              className="h-auto w-full rounded-lg"
              style={{
                maxWidth: IMAGE_CONSTRAINTS.maxWidth,
                maxHeight: IMAGE_CONSTRAINTS.editMaxHeight,
                objectFit: "contain",
              }}
              onError={handleImageError}
            />

            <div className="flex items-center justify-between">
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowUploader(true)}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Change Image
                </Button>
                <Button size="sm" variant="outline" onClick={onToggleEdit}>
                  Done
                </Button>
              </div>

              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => onUpdate("")}
                >
                  Remove
                </Button>
                {onDelete && (
                  <Button size="sm" variant="destructive" onClick={onDelete}>
                    Delete Component
                  </Button>
                )}
              </div>
            </div>

            <div className="text-gray-500 text-xs">
              <p>URL: {imageUrl}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Display mode with image (not editing, not preview)
  if (hasImage && !isEditing && !isPreviewMode) {
    return (
      <div className={cn("w-full", className)}>
        <div className="flex flex-col items-center space-y-3">
          <div className="group relative">
            <img
              src={imageUrl}
              alt="Display content"
              className="h-auto w-full rounded-lg shadow-sm"
              style={{
                maxWidth: IMAGE_CONSTRAINTS.maxWidth,
                maxHeight: IMAGE_CONSTRAINTS.maxHeight,
              }}
              onError={handleImageError}
            />

            {/* Hover overlay for larger images */}
            <div className="absolute inset-0 flex items-center justify-center rounded-lg bg-gray-900 bg-opacity-0 opacity-0 transition-all duration-200 group-hover:bg-opacity-10 group-hover:opacity-100">
              <Button
                size="sm"
                variant="secondary"
                onClick={onToggleEdit}
                className="bg-white/90 hover:bg-white"
              >
                <Edit3 className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </div>
          </div>

          {/* Always visible edit button for better accessibility */}
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={onToggleEdit}
              className="text-xs"
            >
              <Edit3 className="mr-1 h-3 w-3" />
              Edit Image
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Empty state - no image selected
  return (
    <Card className={cn("w-full border-2 border-dashed", className)}>
      <CardContent className="p-8">
        <div className="space-y-4 text-center">
          <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
          <div>
            <h3 className="font-medium text-gray-900">No image selected</h3>
            <p className="mt-1 text-gray-600 text-sm">
              Upload an image or enter a URL to get started
            </p>
          </div>
          <Button onClick={() => setShowUploader(true)}>
            <Upload className="mr-2 h-4 w-4" />
            Add Image
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
