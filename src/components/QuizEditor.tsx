import { Plus, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { m } from "@/paraglide/messages.js";
import { createQuiz, updateQuiz } from "@/services/instructor";
import { Quiz } from "@/types/quizzes";

interface QuizEditorProps {
  quizIndex: number;
  selectedQuiz?: Quiz;
  sectionId: string;
  cancel: () => void;
  onSave: () => void;
}

// Local quiz state interface for editing
interface QuizEditState {
  id?: string;
  section_id: string;
  question: string;
  answers: string[];
  correct_answer: number[];
  explanation: string;
  skippable: boolean;
  ordinal_index: number;
  multiple_choices?: boolean;
}

// Interface for the quiz data we send to the API (extends the base Quiz type)
interface QuizCreateUpdatePayload extends Omit<Quiz, "id" | "attempt"> {
  id?: string;
  correct_answer: number[];
  explanation: string;
}

export default function QuizEditor(props: QuizEditorProps) {
  const { quizIndex, selectedQuiz, sectionId, cancel, onSave } = props;

  const [quiz, setQuiz] = useState<QuizEditState>({
    section_id: sectionId,
    question: "",
    answers: ["", "", "", ""],
    correct_answer: [],
    explanation: "",
    skippable: false,
    ordinal_index: quizIndex,
    multiple_choices: true,
  });

  useEffect(() => {
    if (!selectedQuiz) return;

    // Map the Quiz type to our edit state
    setQuiz({
      id: selectedQuiz.id,
      section_id: selectedQuiz.section_id,
      question: selectedQuiz.question,
      answers: selectedQuiz.answers,
      correct_answer: selectedQuiz.correct_answer || [],
      explanation: selectedQuiz.explanation || "",
      skippable: selectedQuiz.skippable,
      ordinal_index: quizIndex,
      multiple_choices: selectedQuiz.multiple_choices,
    });
  }, [selectedQuiz, quizIndex]);

  const setQuestion = (e) => {
    setQuiz((prevState) => ({
      ...prevState,
      question: e.target.value,
    }));
  };
  const setExplanation = (e) => {
    setQuiz((prevState) => ({
      ...prevState,
      explanation: e.target.value,
    }));
  };

  const removeAnswer = (idx) => {
    setQuiz((prevState) => ({
      ...prevState,
      answers: prevState.answers.filter((_, index) => index !== idx),
      correct_answer: prevState.correct_answer
        .filter((answerIdx) => answerIdx !== idx)
        .map((answerIdx) => (answerIdx > idx ? answerIdx - 1 : answerIdx)),
    }));
  };

  const addAnswer = () => {
    setQuiz((prevState) => ({
      ...prevState,
      answers: prevState.answers.concat(""),
    }));
  };

  const changeCorrectAnswer = (checked: boolean, idx: number) => {
    const newCorrectAnswers = quiz.correct_answer.filter(
      (aIdx) => aIdx !== idx,
    );
    if (checked) {
      newCorrectAnswers.push(idx);
    }
    setQuiz((prevState) => ({
      ...prevState,
      correct_answer: newCorrectAnswers,
    }));
  };

  const changeAnswer = (e, idx) => {
    const newAnswers = [...quiz.answers];
    newAnswers[idx] = e.target.value;
    setQuiz((prevState) => ({ ...prevState, answers: newAnswers }));
  };

  const changeSkippable = (e) => {
    setQuiz((quiz) => ({ ...quiz, skippable: e.target.checked }));
  };

  const saveQuiz = async () => {
    try {
      if (!quiz.question || !quiz.answers || !quiz.correct_answer) {
        return toast.warning(m["quiz.invalidFields"]());
      }
      if (quiz.answers.length === 0 || quiz.correct_answer.length === 0) {
        return toast.warning(m["quiz.invalidAnswer"]());
      }
      for (let i = 0; i < quiz.answers.length; i++) {
        if (!quiz.answers[i]) {
          return toast.warning(m["quiz.invalidAnswerContent"]());
        }
      }

      // Create the quiz object in the correct format for the API
      const baseQuizData = {
        section_id:
          quiz.section_id as `${string}-${string}-${string}-${string}-${string}`,
        question: quiz.question,
        answers: quiz.answers,
        correct_answer: quiz.correct_answer,
        explanation: quiz.explanation,
        skippable: quiz.skippable,
        multiple_choices: quiz.multiple_choices || true,
      };

      if (!quiz.id) {
        await createQuiz({ quiz: baseQuizData as unknown as Quiz });
      } else {
        await updateQuiz({
          id: quiz.id as `${string}-${string}-${string}-${string}-${string}`,
          quiz: { ...baseQuizData, id: quiz.id } as unknown as Quiz,
        });
      }
      onSave?.();
      toast.success(m["quiz.savedQuiz"]());
    } catch (e) {
      console.error("Save quiz error:", e);
      toast.error(m["quiz.saveQuizFailed"]() + " " + e.toString());
    }
  };

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        {quiz && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-lg">
                #{quizIndex + 1}:{" "}
                {selectedQuiz
                  ? m["quiz.editQuiz"]()
                  : m["quiz.createNewQuiz"]()}
              </h3>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="skippable"
                  checked={quiz.skippable}
                  onCheckedChange={(checked) =>
                    setQuiz((prev) => ({
                      ...prev,
                      skippable: Boolean(checked),
                    }))
                  }
                />
                <Label htmlFor="skippable">{m["quiz.skippable"]()}</Label>
              </div>
            </div>

            <div>
              <Label htmlFor="question" className="font-medium text-sm">
                {m["quiz.question"]()}
              </Label>
              <Textarea
                id="question"
                rows={2}
                placeholder={m["quiz.questionPlaceholder"]()}
                value={quiz.question}
                onChange={(e) =>
                  setQuiz((prev) => ({ ...prev, question: e.target.value }))
                }
                className="mt-1"
              />
            </div>

            <div>
              <Label className="font-medium text-sm">
                {m["quiz.answers"]()}
              </Label>
              <div className="mt-2 grid gap-4 md:grid-cols-2">
                {quiz?.answers?.map((answer, idx) => (
                  <div key={idx} className="flex items-center space-x-2">
                    <Checkbox
                      id={`answer-${idx}`}
                      title={m["quiz.setCorrectAnswer"]()}
                      checked={quiz.correct_answer.includes(idx)}
                      onCheckedChange={(checked) =>
                        changeCorrectAnswer(Boolean(checked), idx)
                      }
                    />
                    <Textarea
                      rows={1}
                      placeholder={`Answer ${idx + 1}...`}
                      value={answer}
                      onChange={(e) => changeAnswer(e, idx)}
                      className="flex-1"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeAnswer(idx)}
                      title={m["quiz.removeAnswer"]()}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
              <Button variant="outline" onClick={addAnswer} className="mt-2">
                <Plus className="mr-2 h-4 w-4" />
                {m["quiz.addAnswer"]()}
              </Button>
            </div>

            <div>
              <Label htmlFor="explanation" className="font-medium text-sm">
                {m["quiz.explanation"]()}
              </Label>
              <Textarea
                id="explanation"
                rows={2}
                placeholder="Provide an explanation for the correct answer..."
                value={quiz.explanation}
                onChange={(e) =>
                  setQuiz((prev) => ({ ...prev, explanation: e.target.value }))
                }
                className="mt-1"
              />
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="destructive" onClick={cancel}>
                Cancel
              </Button>
              <Button variant="default" onClick={saveQuiz}>
                Save Quiz
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
