import { Building2, Palette, Upload } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { BrandColors } from "../../types/organization";

interface BrandingSettingsProps {
  onBrandingUpdate?: (colors: BrandColors) => void;
  onLogoUpdate?: (logoFile: File) => void;
}

/**
 * Component for managing organization branding settings
 * Includes logo upload and brand color customization
 */
export function BrandingSettings({
  onBrandingUpdate,
  onLogoUpdate,
}: BrandingSettingsProps) {
  const [brandColors, setBrandColors] = useState<BrandColors>({
    primary: "#164e63",
    secondary: "#f59e0b",
    accent: "#475569",
  });

  /**
   * Handle color change for a specific brand color
   */
  const handleColorChange = (colorType: keyof BrandColors, value: string) => {
    const newColors = { ...brandColors, [colorType]: value };
    setBrandColors(newColors);
  };

  /**
   * Apply brand color changes
   */
  const handleApplyColors = () => {
    onBrandingUpdate?.(brandColors);
  };

  /**
   * Reset colors to default
   */
  const handleResetColors = () => {
    const defaultColors = {
      primary: "#164e63",
      secondary: "#f59e0b",
      accent: "#475569",
    };
    setBrandColors(defaultColors);
  };

  /**
   * Handle logo file selection
   */
  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onLogoUpdate?.(file);
    }
  };

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
      {/* Logo Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Logo công ty
          </CardTitle>
          <CardDescription>
            Tải lên logo để hiển thị trên hệ thống
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Upload Area */}
          <div className="rounded-lg border-2 border-border border-dashed p-8 text-center">
            <Upload className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
            <p className="mb-2 text-muted-foreground text-sm">
              Kéo thả file hoặc click để chọn
            </p>
            <p className="text-muted-foreground text-xs">
              PNG, JPG, SVG (tối đa 2MB)
            </p>
            <label htmlFor="logo-upload">
              <Button
                variant="outline"
                className="mt-4 cursor-pointer bg-transparent"
              >
                Chọn file
              </Button>
              <input
                id="logo-upload"
                type="file"
                accept="image/*"
                onChange={handleLogoChange}
                className="hidden"
              />
            </label>
          </div>

          {/* Current Logo */}
          <div className="space-y-2">
            <Label>Logo hiện tại</Label>
            <div className="flex items-center gap-4 rounded-lg border p-4">
              <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-muted">
                <Building2 className="h-8 w-8 text-muted-foreground" />
              </div>
              <div className="flex-1">
                <p className="font-medium">company-logo.png</p>
                <p className="text-muted-foreground text-sm">256x256px, 45KB</p>
              </div>
              <label htmlFor="logo-replace">
                <Button variant="outline" size="sm" className="cursor-pointer">
                  Thay đổi
                </Button>
                <input
                  id="logo-replace"
                  type="file"
                  accept="image/*"
                  onChange={handleLogoChange}
                  className="hidden"
                />
              </label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Brand Colors Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Màu sắc thương hiệu
          </CardTitle>
          <CardDescription>
            Tùy chỉnh màu sắc giao diện hệ thống
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Color Inputs */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="primary-color">Màu chính</Label>
              <div className="flex items-center gap-2">
                <div
                  className="h-10 w-10 rounded border"
                  style={{ backgroundColor: brandColors.primary }}
                ></div>
                <Input
                  id="primary-color"
                  value={brandColors.primary}
                  onChange={(e) => handleColorChange("primary", e.target.value)}
                  className="flex-1"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="secondary-color">Màu phụ</Label>
              <div className="flex items-center gap-2">
                <div
                  className="h-10 w-10 rounded border"
                  style={{ backgroundColor: brandColors.secondary }}
                ></div>
                <Input
                  id="secondary-color"
                  value={brandColors.secondary}
                  onChange={(e) =>
                    handleColorChange("secondary", e.target.value)
                  }
                  className="flex-1"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="accent-color">Màu nhấn</Label>
              <div className="flex items-center gap-2">
                <div
                  className="h-10 w-10 rounded border"
                  style={{ backgroundColor: brandColors.accent }}
                ></div>
                <Input
                  id="accent-color"
                  value={brandColors.accent}
                  onChange={(e) => handleColorChange("accent", e.target.value)}
                  className="flex-1"
                />
              </div>
            </div>
          </div>

          {/* Color Preview */}
          <div className="border-t pt-4">
            <h4 className="mb-3 font-medium">Xem trước</h4>
            <div className="space-y-2">
              <Button
                className="w-full"
                style={{ backgroundColor: brandColors.primary, color: "white" }}
              >
                Nút chính
              </Button>
              <Button
                variant="outline"
                className="w-full bg-transparent"
                style={{
                  borderColor: brandColors.secondary,
                  color: brandColors.secondary,
                }}
              >
                Nút phụ
              </Button>
              <div
                className="rounded p-3"
                style={{ backgroundColor: brandColors.accent }}
              >
                <p className="text-sm text-white">
                  Văn bản mẫu với màu sắc mới
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={handleResetColors}>
              Đặt lại
            </Button>
            <Button onClick={handleApplyColors}>Áp dụng</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
