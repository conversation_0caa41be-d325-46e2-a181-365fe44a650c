import { Edit, Loader2, Shield, Trash2, Users } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { deleteGroup, useGroupsPermissions } from "@/services/admin";
import { PermissionDialog } from "./PermissionDialog";
import { PermissionRenderer } from "./PermissionRenderer";

export function PermissionManagement() {
  const {
    data: permissions = [],
    isLoading,
    error,
    refetch,
  } = useGroupsPermissions();

  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [selectedPermissionId, setSelectedPermissionId] = useState<
    string | null
  >(null);
  const [isDeleting, setIsDeleting] = useState(false);

  /**
   * Handle permission delete action
   */
  const handleDeletePermission = async (permissionId: string) => {
    try {
      setIsDeleting(true);
      await deleteGroup(permissionId);
      toast.success("Xóa quyền thành công");
      // Refresh the permissions list
      refetch();
    } catch (error) {
      console.error("Error deleting permission:", error);
      toast.error("Không thể xóa quyền", {
        description: "Đã có lỗi xảy ra. Vui lòng thử lại sau.",
      });
    } finally {
      setIsDeleting(false);
      setDeleteConfirmOpen(false);
      setSelectedPermissionId(null);
    }
  };

  /**
   * Open delete confirmation dialog
   */
  const confirmDelete = (permissionId: string) => {
    setSelectedPermissionId(permissionId);
    setDeleteConfirmOpen(true);
  };

  if (error) {
    toast.error("Không thể tải danh sách quyền", {
      description: "Đã có lỗi xảy ra khi tải dữ liệu. Vui lòng thử lại sau.",
    });
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Quản lý phân quyền
            </CardTitle>
            <CardDescription>
              Tạo và quản lý quyền hạn cho các nhóm trong tổ chức
            </CardDescription>
          </div>
          <PermissionDialog onSuccess={() => refetch()} />
        </div>
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-10">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">
              Đang tải dữ liệu...
            </span>
          </div>
        ) : permissions?.length === 0 ? (
          <div className="py-10 text-center">
            <p className="text-muted-foreground">Chưa có quyền nào được tạo</p>
            <p className="mt-1 text-muted-foreground text-sm">
              Nhấn nút "Thêm quyền mới" để bắt đầu
            </p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tên nhóm quyền</TableHead>
                <TableHead>Mô tả</TableHead>
                <TableHead>Quyền</TableHead>
                <TableHead className="text-right">Thao tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {permissions.map((permission) => (
                <TableRow key={permission.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{permission.name}</Badge>
                    </div>
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {permission.description}
                  </TableCell>
                  <TableCell>
                    <PermissionRenderer
                      permissions={permission.permissions.map((p) =>
                        p.replace("_", " "),
                      )}
                    />
                  </TableCell>
                  {permission.name !== "Super admin" && (
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <PermissionDialog
                          groupPermission={permission}
                          onSuccess={() => refetch()}
                          trigger={
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          }
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-destructive"
                          onClick={() => confirmDelete(permission.id)}
                          disabled={
                            isDeleting && selectedPermissionId === permission.id
                          }
                        >
                          {isDeleting &&
                          selectedPermissionId === permission.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      {/* Delete confirmation dialog */}
      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa quyền</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa quyền này? Hành động này không thể hoàn
              tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                selectedPermissionId &&
                handleDeletePermission(selectedPermissionId)
              }
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
              disabled={isDeleting}
            >
              {isDeleting ? "Đang xóa..." : "Xóa quyền"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
