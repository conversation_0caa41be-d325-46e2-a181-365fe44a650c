import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { PERMISSION_INFO } from "../../types/organization";

interface PermissionRendererProps {
  permissions: string[];
}

/**
 * Component to render permission badges with tooltip for hidden permissions
 * Shows up to 2 permissions directly, rest in a tooltip
 */
export function PermissionRenderer({
  permissions = [],
}: PermissionRendererProps) {
  if (!permissions || permissions.length === 0) {
    return null;
  }

  // If 3 or fewer permissions, show all directly
  if (permissions.length <= 3) {
    return (
      <div className="flex flex-wrap gap-1">
        {permissions.map((permission) => {
          const permissionInfo = PERMISSION_INFO[permission];
          return (
            <Badge key={permission} variant="outline">
              {permissionInfo?.name || permission}
            </Badge>
          );
        })}
      </div>
    );
  }

  // Show first 3 permissions + count of remaining
  const visiblePermissions = permissions.slice(0, 3);
  const hiddenCount = permissions.length - 3;
  const hiddenPermissions = permissions.slice(3);

  return (
    <div className="flex flex-wrap gap-1">
      {visiblePermissions.map((permission) => {
        const permissionInfo = PERMISSION_INFO[permission];
        return (
          <Badge key={permission} variant="outline">
            {permissionInfo?.name || permission}
          </Badge>
        );
      })}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge variant="outline" className="cursor-help text-xs">
              +{hiddenCount}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              {hiddenPermissions.map((permission) => {
                const permissionInfo = PERMISSION_INFO[permission];
                return (
                  <div key={permission} className="text-sm">
                    {permissionInfo?.name || permission}
                  </div>
                );
              })}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}
