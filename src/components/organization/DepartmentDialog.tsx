import { useForm } from "@tanstack/react-form";
import { Plus } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  createDepartment,
  updateDepartment,
  useDepartments,
} from "@/services/admin";
import { Department } from "../../types/organization";

interface DepartmentDialogProps {
  onSuccess?: () => void;
  department?: Department; // If provided, we're in edit mode
  trigger?: React.ReactNode; // Custom trigger element
}

// Form schema for TanStack Form
interface DepartmentFormSchema {
  name: string;
  description: string;
}

/**
 * Dialog component for adding or editing departments with permission assignment
 * Includes validation for unique department names
 */
export function DepartmentDialog({
  onSuccess,
  department,
  trigger,
}: DepartmentDialogProps) {
  const isEditMode = !!department;
  const [isOpen, setIsOpen] = useState(false);

  // Get departments list from API for validation
  const { data: departments = [] } = useDepartments();

  // Initialize TanStack Form
  const form = useForm({
    defaultValues: {
      name: department?.name || "",
      description: department?.description || "",
    } as DepartmentFormSchema,
    onSubmit: async ({ value }) => {
      await saveDepartment(value);
    },
    validators: {
      onSubmit: ({ value }) => {
        const errors: Record<string, string> = {};

        if (!value.name?.trim()) {
          errors.name = "Tên phòng ban là bắt buộc";
        }

        if (!value.description?.trim()) {
          errors.description = "Mô tả phòng ban là bắt buộc";
        }

        // Check if department name is unique (only for new departments)
        if (!isEditMode && !isDepartmentNameUnique(value.name)) {
          errors.name = "Tên phòng ban đã tồn tại";
        }

        return Object.keys(errors).length > 0 ? errors : undefined;
      },
    },
  });

  /**
   * Check if department name is unique
   */
  const isDepartmentNameUnique = (name: string) => {
    return !departments.some(
      (dept) =>
        dept.id !== department?.id &&
        dept.name.toLowerCase() === name.toLowerCase(),
    );
  };

  /**
   * Save department function
   */
  const saveDepartment = async (formData: DepartmentFormSchema) => {
    try {
      if (isEditMode && department?.id) {
        // Call API to update department
        await updateDepartment({
          id: department.id,
          description: formData.description,
        });

        // Show success message
        toast.success("Cập nhật phòng ban thành công!", {
          description: `Phòng ban "${formData.name}" đã được cập nhật thành công.`,
        });
      } else {
        // Call API to create new department
        await createDepartment({
          name: formData.name,
          description: formData.description,
        });

        // Show success message
        toast.success("Tạo phòng ban mới thành công!", {
          description: `Phòng ban "${formData.name}" đã được tạo thành công.`,
        });
      }

      // Notify parent component to refresh the list
      onSuccess?.();

      // Reset form and close dialog
      form.reset();
      setIsOpen(false);
    } catch (error) {
      console.error(
        `Failed to ${isEditMode ? "update" : "create"} department:`,
        error,
      );
      // Show error message
      toast.error(`Không thể ${isEditMode ? "cập nhật" : "tạo"} phòng ban`, {
        description: `Đã có lỗi xảy ra khi ${
          isEditMode ? "cập nhật" : "tạo"
        } phòng ban. Vui lòng thử lại sau.`,
      });
    }
  };

  /**
   * Handle dialog close - reset form
   */
  const handleClose = () => {
    if (!isEditMode) {
      form.reset();
    }
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="default" className="cursor-pointer bg-primary">
            <Plus className="mr-2 h-4 w-4" />
            {isEditMode ? "Sửa phòng ban" : "Thêm phòng ban"}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? "Sửa phòng ban" : "Thêm phòng ban mới"}
          </DialogTitle>
          <DialogDescription>
            {isEditMode
              ? "Cập nhật thông tin phòng ban và phân quyền truy cập"
              : "Tạo phòng ban mới và phân quyền truy cập"}
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit();
          }}
          className="space-y-4"
        >
          {/* Department name input */}
          <form.Field name="name">
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor="dept-name">Tên phòng ban</Label>
                <Input
                  id="dept-name"
                  value={field.state.value}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Nhập tên phòng ban"
                  disabled={isEditMode} // Cannot edit name in edit mode
                />
                {field.state.meta.errors && (
                  <p className="text-red-500 text-sm">
                    {field.state.meta.errors[0]}
                  </p>
                )}
              </div>
            )}
          </form.Field>

          {/* Department description input */}
          <form.Field name="description">
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor="dept-description">Mô tả</Label>
                <Textarea
                  id="dept-description"
                  value={field.state.value}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Nhập mô tả phòng ban"
                  rows={3}
                />
                {field.state.meta.errors && (
                  <p className="text-red-500 text-sm">
                    {field.state.meta.errors[0]}
                  </p>
                )}
              </div>
            )}
          </form.Field>
        </form>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Hủy
          </Button>
          <form.Subscribe
            selector={(state) => [state.canSubmit, state.isSubmitting]}
            children={([canSubmit, isSubmitting]) => (
              <Button
                type="submit"
                onClick={() => form.handleSubmit()}
                disabled={!canSubmit || isSubmitting}
                className="min-w-24"
              >
                {isSubmitting
                  ? "Đang xử lý..."
                  : isEditMode
                    ? "Cập nhật"
                    : "Tạo phòng ban"}
              </Button>
            )}
          />
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
