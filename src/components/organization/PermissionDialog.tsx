import { useForm } from "@tanstack/react-form";
import { Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  createGroup,
  updateGroup,
  useGroupsPermissions,
} from "@/services/admin";
import {
  GroupPermission,
  PERMISSION,
  PERMISSION_INFO,
} from "../../types/organization";

interface PermissionDialogProps {
  onSuccess?: () => void;
  groupPermission?: GroupPermission; // If provided, we're in edit mode
  trigger?: React.ReactNode; // Custom trigger element
}

// Form schema for TanStack Form
interface PermissionFormSchema {
  name: string;
  description: string;
  permissions: PERMISSION[];
}

/**
 * Dialog component for adding or editing permissions with permission assignment
 * Includes validation for unique permission names
 */
export function PermissionDialog({
  onSuccess,
  groupPermission,
  trigger,
}: PermissionDialogProps) {
  const isEditMode = !!groupPermission;
  const [isOpen, setIsOpen] = useState(false);

  // Get groups list from API
  const { data: groups = [] } = useGroupsPermissions();

  // Initialize TanStack Form
  const form = useForm({
    defaultValues: {
      name: groupPermission?.name || "",
      description: groupPermission?.description || "",
      permissions: groupPermission?.permissions || [],
    } as PermissionFormSchema,
    onSubmit: async ({ value }) => {
      await savePermission(value);
    },
    validators: {
      onSubmit: ({ value }) => {
        const errors: Record<string, string> = {};

        if (!value.name?.trim()) {
          errors.name = "Tên nhóm quyền là bắt buộc";
        }

        if (!value.description?.trim()) {
          errors.description = "Mô tả là bắt buộc";
        }

        // Check if name is unique (only for create mode)
        if (!isEditMode && value.name) {
          const isNameTaken = groups.some(
            (group) => group.name.toLowerCase() === value.name.toLowerCase(),
          );
          if (isNameTaken) {
            errors.name = "Tên nhóm quyền đã tồn tại";
          }
        }

        return Object.keys(errors).length > 0 ? errors : undefined;
      },
    },
  });

  // Update form when groupPermission changes
  useEffect(() => {
    if (groupPermission && isEditMode) {
      form.setFieldValue("name", groupPermission.name);
      form.setFieldValue("description", groupPermission.description || "");
      form.setFieldValue("permissions", groupPermission.permissions || []);
    }
  }, [groupPermission, isEditMode, form]);

  const savePermission = async (formData: PermissionFormSchema) => {
    try {
      const enumPermissions = formData.permissions.map(
        (p) => p as unknown as PERMISSION,
      );

      if (isEditMode && groupPermission?.id) {
        // Call API to update group
        await updateGroup({
          id: groupPermission.id,
          description: formData.description,
          permissions: enumPermissions,
        });

        // Show success message
        toast.success("Cập nhật nhóm quyền thành công!", {
          description: `Nhóm quyền "${formData.name}" đã được cập nhật với ${formData.permissions.length} quyền.`,
        });
      } else {
        // Call API to create new group
        await createGroup({
          name: formData.name,
          description: formData.description,
          permissions: enumPermissions,
        });

        // Show success message
        toast.success("Tạo nhóm quyền mới thành công!", {
          description: `Nhóm quyền "${formData.name}" đã được tạo với ${formData.permissions.length} quyền.`,
        });
      }

      // Notify parent component to refresh the list
      onSuccess?.();

      // Reset form and close dialog
      if (!isEditMode) {
        form.reset();
      }
      setIsOpen(false);
    } catch (error) {
      console.error(
        `Failed to ${isEditMode ? "update" : "create"} permission:`,
        error,
      );
      // Show error message
      toast.error(`Không thể ${isEditMode ? "cập nhật" : "tạo"} nhóm quyền`, {
        description: `Đã có lỗi xảy ra khi ${
          isEditMode ? "cập nhật" : "tạo"
        } nhóm quyền. Vui lòng thử lại sau.`,
      });
    }
  };

  /**
   * Handle permission toggle
   */
  const handlePermissionToggle = (
    permissionId: PERMISSION,
    checked: boolean,
  ) => {
    const currentPermissions = form.getFieldValue("permissions") || [];

    if (checked) {
      form.setFieldValue("permissions", [...currentPermissions, permissionId]);
    } else {
      form.setFieldValue(
        "permissions",
        currentPermissions.filter((p) => p !== permissionId),
      );
    }
  };

  /**
   * Handle dialog close
   */
  const handleClose = () => {
    if (!isEditMode) {
      form.reset();
    }
    setIsOpen(false);
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          handleClose();
        } else {
          setIsOpen(true);
        }
      }}
    >
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="default" className="cursor-pointer bg-primary">
            <Plus className="mr-2 h-4 w-4" />
            {isEditMode ? "Sửa quyền" : "Thêm quyền mới"}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? "Sửa nhóm quyền" : "Thêm nhóm quyền mới"}
          </DialogTitle>
          <DialogDescription>
            {isEditMode
              ? "Cập nhật thông tin nhóm quyền và phân quyền truy cập"
              : "Tạo nhóm quyền mới và phân quyền truy cập"}
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit();
          }}
        >
          <div className="space-y-4">
            {/* Group name input */}
            <form.Field
              name="name"
              validators={{
                onChange: ({ value }) =>
                  !value || value.trim().length < 3
                    ? "Tên nhóm quyền là bắt buộc (tối thiểu 3 ký tự)"
                    : undefined,
              }}
              children={(field) => (
                <div className="space-y-2">
                  <Label htmlFor="group-name">Tên nhóm quyền</Label>
                  <Input
                    id="group-name"
                    value={field.state.value}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Nhập tên nhóm quyền"
                    disabled={isEditMode} // Cannot edit name in edit mode
                    className={
                      field.state.meta.errors.length > 0
                        ? "border-destructive"
                        : ""
                    }
                  />
                  {field.state.meta.errors.length > 0 && (
                    <p className="text-red-500 text-sm">
                      {field.state.meta.errors[0]}
                    </p>
                  )}
                </div>
              )}
            />

            {/* Group description input */}
            <form.Field
              name="description"
              validators={{
                onChange: ({ value }) =>
                  !value || value.trim().length < 10
                    ? "Mô tả là bắt buộc (tối thiểu 10 ký tự)"
                    : undefined,
              }}
              children={(field) => (
                <div className="space-y-2">
                  <Label htmlFor="group-description">Mô tả</Label>
                  <Textarea
                    id="group-description"
                    value={field.state.value}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Nhập mô tả nhóm quyền"
                    rows={3}
                    className={
                      field.state.meta.errors.length > 0
                        ? "border-destructive"
                        : ""
                    }
                  />
                  {field.state.meta.errors.length > 0 && (
                    <p className="text-red-500 text-sm">
                      {field.state.meta.errors[0]}
                    </p>
                  )}
                </div>
              )}
            />

            {/* Permissions selection */}
            <form.Field
              name="permissions"
              children={(field) => (
                <div className="space-y-2">
                  <Label>Phân quyền</Label>
                  <div className="max-h-48 space-y-2 overflow-y-auto">
                    {Object.entries(PERMISSION_INFO).map(([id, permission]) => (
                      <div key={id} className="flex items-center space-x-2">
                        <Input
                          type="checkbox"
                          id={id}
                          className="mt-1 w-max"
                          checked={field.state.value.includes(id as PERMISSION)}
                          onChange={(e) =>
                            handlePermissionToggle(
                              id as PERMISSION,
                              e.target.checked,
                            )
                          }
                          size={12}
                        />
                        <div className="flex-1">
                          <Label htmlFor={id} className="font-medium">
                            {permission.name}
                          </Label>
                          <p className="text-muted-foreground text-xs">
                            {permission.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            />
          </div>

          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={handleClose} type="button">
              Hủy
            </Button>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button type="submit" disabled={!canSubmit || isSubmitting}>
                  {isSubmitting
                    ? "Đang xử lý..."
                    : isEditMode
                      ? "Cập nhật"
                      : "Tạo nhóm quyền"}
                </Button>
              )}
            />
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
