import { RoleInfo } from "@/types/users";

export const getRoleDisplay = (role: string) => {
  const roleMap: Record<string, RoleInfo> = {
    Learner: { label: "<PERSON><PERSON><PERSON> viên", color: "bg-blue-100 text-blue-800" },
    Instructor: {
      label: "Người tạo khóa",
      color: "bg-green-100 text-green-800",
    },
    "Org Admin": {
      label: "Quản trị viên",
      color: "bg-purple-100 text-purple-800",
    },
  };
  return roleMap[role] || { label: role, color: "bg-gray-100 text-gray-800" };
};
