import { CourseRequirement, CourseStatus } from "@/types/courses";

// Course status configuration
const COURSE_STATUS_CONFIG = {
  [CourseStatus.PUBLISHED]: {
    label: "Đã xuất bản",
    color: "bg-black text-white",
  },
  [CourseStatus.DRAFT]: {
    label: "Bản nháp",
    color: "bg-blue-100 text-blue-800",
  },
  [CourseStatus.ARCHIVED]: {
    label: "Đã lưu trữ",
    color: "bg-gray-100 text-gray-800",
  },
} as const;

// Course requirement configuration
const COURSE_REQUIREMENT_CONFIG = {
  [CourseRequirement.COMPANY]: {
    label: "Bắt buộc cả công ty",
    color: "bg-red-100 text-red-800 border-red-200",
  },
  [CourseRequirement.DEPARTMENT]: {
    label: "Bắt buộc theo phòng ban",
    color: "bg-orange-100 text-orange-800 border-orange-200",
  },
  [CourseRequirement.OPTIONAL]: {
    label: "T<PERSON><PERSON> chọn/khuyến khích",
    color: "bg-green-100 text-green-800 border-green-200",
  },
} as const;

// Publication status configuration
const PUBLICATION_STATUS_CONFIG = {
  published: {
    label: "Đã xuất bản",
    color: "bg-green-100 text-green-800",
  },
  unpublished: {
    label: "Chưa xuất bản",
    color: "bg-yellow-100 text-yellow-800",
  },
} as const;

// Helper functions for course status
export const getStatusColor = (status: string) => {
  return (
    COURSE_STATUS_CONFIG[status as CourseStatus]?.color ||
    "bg-gray-100 text-gray-800"
  );
};

export const getStatusText = (status: string) => {
  return COURSE_STATUS_CONFIG[status as CourseStatus]?.label || status;
};

// Helper functions for course requirement
export const getRequirementColor = (requirement: string) => {
  return (
    COURSE_REQUIREMENT_CONFIG[requirement as CourseRequirement]?.color ||
    "bg-gray-100 text-gray-800 border-gray-200"
  );
};

export const getRequirementText = (requirement: string) => {
  return (
    COURSE_REQUIREMENT_CONFIG[requirement as CourseRequirement]?.label ||
    requirement
  );
};

// Helper functions for publication status
export const getPublicationStatusColor = (isPublished: boolean) => {
  return isPublished
    ? PUBLICATION_STATUS_CONFIG.published.color
    : PUBLICATION_STATUS_CONFIG.unpublished.color;
};

export const getPublicationStatusText = (isPublished: boolean) => {
  return isPublished
    ? PUBLICATION_STATUS_CONFIG.published.label
    : PUBLICATION_STATUS_CONFIG.unpublished.label;
};

// Helper function to parse course description
export const parseCourseDescription = (description: string | undefined) => {
  try {
    return description ? JSON.parse(description) : { overview: "", goals: [] };
  } catch {
    // Fallback if description is not JSON
    return { overview: description || "", goals: [] };
  }
};
