export const qs = (
  params: Record<string, string | number | boolean | undefined | null>,
): string => {
  const filteredParams = Object.entries(params).filter(
    ([_, value]) => value !== undefined && value !== null,
  );

  if (filteredParams.length === 0) {
    return "";
  }

  const queryString = filteredParams
    .map(
      ([key, value]) =>
        `${encodeURIComponent(key)}=${encodeURIComponent(value as string | number | boolean)}`,
    )
    .join("&");

  return `?${queryString}`;
};
