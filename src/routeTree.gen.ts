/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as AuthRouteImport } from './routes/auth'
import { Route as DashboardRouteRouteImport } from './routes/dashboard/route'
import { Route as IndexRouteImport } from './routes/index'
import { Route as LessonIndexRouteImport } from './routes/lesson/index'
import { Route as DashboardIndexRouteImport } from './routes/dashboard/index'
import { Route as DemoVideoComponentRouteImport } from './routes/demo/video-component'
import { Route as DemoImageUploadRouteImport } from './routes/demo/image-upload'
import { Route as DemoImageComponentTestRouteImport } from './routes/demo/image-component-test'
import { Route as DashboardWikiIndexRouteImport } from './routes/dashboard/wiki/index'
import { Route as DashboardUsersIndexRouteImport } from './routes/dashboard/users/index'
import { Route as DashboardOrganizationIndexRouteImport } from './routes/dashboard/organization/index'
import { Route as DashboardCoursesIndexRouteImport } from './routes/dashboard/courses/index'
import { Route as DashboardAnalyticsIndexRouteImport } from './routes/dashboard/analytics/index'
import { Route as DashboardCoursesCreateCourseRouteImport } from './routes/dashboard/courses/create-course'
import { Route as DashboardUsersUserIdIndexRouteImport } from './routes/dashboard/users/$userId/index'
import { Route as DashboardCoursesCourseSlugIndexRouteImport } from './routes/dashboard/courses/$courseSlug/index'
import { Route as DashboardCoursesCourseSlugEditRouteImport } from './routes/dashboard/courses/$courseSlug/edit'
import { Route as DashboardCoursesCourseSlugLessonSlugRouteImport } from './routes/dashboard/courses/$courseSlug/$lessonSlug'

const AuthRoute = AuthRouteImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardRouteRoute = DashboardRouteRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const LessonIndexRoute = LessonIndexRouteImport.update({
  id: '/lesson/',
  path: '/lesson/',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardIndexRoute = DashboardIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => DashboardRouteRoute,
} as any)
const DemoVideoComponentRoute = DemoVideoComponentRouteImport.update({
  id: '/demo/video-component',
  path: '/demo/video-component',
  getParentRoute: () => rootRouteImport,
} as any)
const DemoImageUploadRoute = DemoImageUploadRouteImport.update({
  id: '/demo/image-upload',
  path: '/demo/image-upload',
  getParentRoute: () => rootRouteImport,
} as any)
const DemoImageComponentTestRoute = DemoImageComponentTestRouteImport.update({
  id: '/demo/image-component-test',
  path: '/demo/image-component-test',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardWikiIndexRoute = DashboardWikiIndexRouteImport.update({
  id: '/wiki/',
  path: '/wiki/',
  getParentRoute: () => DashboardRouteRoute,
} as any)
const DashboardUsersIndexRoute = DashboardUsersIndexRouteImport.update({
  id: '/users/',
  path: '/users/',
  getParentRoute: () => DashboardRouteRoute,
} as any)
const DashboardOrganizationIndexRoute =
  DashboardOrganizationIndexRouteImport.update({
    id: '/organization/',
    path: '/organization/',
    getParentRoute: () => DashboardRouteRoute,
  } as any)
const DashboardCoursesIndexRoute = DashboardCoursesIndexRouteImport.update({
  id: '/courses/',
  path: '/courses/',
  getParentRoute: () => DashboardRouteRoute,
} as any)
const DashboardAnalyticsIndexRoute = DashboardAnalyticsIndexRouteImport.update({
  id: '/analytics/',
  path: '/analytics/',
  getParentRoute: () => DashboardRouteRoute,
} as any)
const DashboardCoursesCreateCourseRoute =
  DashboardCoursesCreateCourseRouteImport.update({
    id: '/courses/create-course',
    path: '/courses/create-course',
    getParentRoute: () => DashboardRouteRoute,
  } as any)
const DashboardUsersUserIdIndexRoute =
  DashboardUsersUserIdIndexRouteImport.update({
    id: '/users/$userId/',
    path: '/users/$userId/',
    getParentRoute: () => DashboardRouteRoute,
  } as any)
const DashboardCoursesCourseSlugIndexRoute =
  DashboardCoursesCourseSlugIndexRouteImport.update({
    id: '/courses/$courseSlug/',
    path: '/courses/$courseSlug/',
    getParentRoute: () => DashboardRouteRoute,
  } as any)
const DashboardCoursesCourseSlugEditRoute =
  DashboardCoursesCourseSlugEditRouteImport.update({
    id: '/courses/$courseSlug/edit',
    path: '/courses/$courseSlug/edit',
    getParentRoute: () => DashboardRouteRoute,
  } as any)
const DashboardCoursesCourseSlugLessonSlugRoute =
  DashboardCoursesCourseSlugLessonSlugRouteImport.update({
    id: '/courses/$courseSlug/$lessonSlug',
    path: '/courses/$courseSlug/$lessonSlug',
    getParentRoute: () => DashboardRouteRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRouteRouteWithChildren
  '/auth': typeof AuthRoute
  '/demo/image-component-test': typeof DemoImageComponentTestRoute
  '/demo/image-upload': typeof DemoImageUploadRoute
  '/demo/video-component': typeof DemoVideoComponentRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/lesson': typeof LessonIndexRoute
  '/dashboard/courses/create-course': typeof DashboardCoursesCreateCourseRoute
  '/dashboard/analytics': typeof DashboardAnalyticsIndexRoute
  '/dashboard/courses': typeof DashboardCoursesIndexRoute
  '/dashboard/organization': typeof DashboardOrganizationIndexRoute
  '/dashboard/users': typeof DashboardUsersIndexRoute
  '/dashboard/wiki': typeof DashboardWikiIndexRoute
  '/dashboard/courses/$courseSlug/$lessonSlug': typeof DashboardCoursesCourseSlugLessonSlugRoute
  '/dashboard/courses/$courseSlug/edit': typeof DashboardCoursesCourseSlugEditRoute
  '/dashboard/courses/$courseSlug': typeof DashboardCoursesCourseSlugIndexRoute
  '/dashboard/users/$userId': typeof DashboardUsersUserIdIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/auth': typeof AuthRoute
  '/demo/image-component-test': typeof DemoImageComponentTestRoute
  '/demo/image-upload': typeof DemoImageUploadRoute
  '/demo/video-component': typeof DemoVideoComponentRoute
  '/dashboard': typeof DashboardIndexRoute
  '/lesson': typeof LessonIndexRoute
  '/dashboard/courses/create-course': typeof DashboardCoursesCreateCourseRoute
  '/dashboard/analytics': typeof DashboardAnalyticsIndexRoute
  '/dashboard/courses': typeof DashboardCoursesIndexRoute
  '/dashboard/organization': typeof DashboardOrganizationIndexRoute
  '/dashboard/users': typeof DashboardUsersIndexRoute
  '/dashboard/wiki': typeof DashboardWikiIndexRoute
  '/dashboard/courses/$courseSlug/$lessonSlug': typeof DashboardCoursesCourseSlugLessonSlugRoute
  '/dashboard/courses/$courseSlug/edit': typeof DashboardCoursesCourseSlugEditRoute
  '/dashboard/courses/$courseSlug': typeof DashboardCoursesCourseSlugIndexRoute
  '/dashboard/users/$userId': typeof DashboardUsersUserIdIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRouteRouteWithChildren
  '/auth': typeof AuthRoute
  '/demo/image-component-test': typeof DemoImageComponentTestRoute
  '/demo/image-upload': typeof DemoImageUploadRoute
  '/demo/video-component': typeof DemoVideoComponentRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/lesson/': typeof LessonIndexRoute
  '/dashboard/courses/create-course': typeof DashboardCoursesCreateCourseRoute
  '/dashboard/analytics/': typeof DashboardAnalyticsIndexRoute
  '/dashboard/courses/': typeof DashboardCoursesIndexRoute
  '/dashboard/organization/': typeof DashboardOrganizationIndexRoute
  '/dashboard/users/': typeof DashboardUsersIndexRoute
  '/dashboard/wiki/': typeof DashboardWikiIndexRoute
  '/dashboard/courses/$courseSlug/$lessonSlug': typeof DashboardCoursesCourseSlugLessonSlugRoute
  '/dashboard/courses/$courseSlug/edit': typeof DashboardCoursesCourseSlugEditRoute
  '/dashboard/courses/$courseSlug/': typeof DashboardCoursesCourseSlugIndexRoute
  '/dashboard/users/$userId/': typeof DashboardUsersUserIdIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/dashboard'
    | '/auth'
    | '/demo/image-component-test'
    | '/demo/image-upload'
    | '/demo/video-component'
    | '/dashboard/'
    | '/lesson'
    | '/dashboard/courses/create-course'
    | '/dashboard/analytics'
    | '/dashboard/courses'
    | '/dashboard/organization'
    | '/dashboard/users'
    | '/dashboard/wiki'
    | '/dashboard/courses/$courseSlug/$lessonSlug'
    | '/dashboard/courses/$courseSlug/edit'
    | '/dashboard/courses/$courseSlug'
    | '/dashboard/users/$userId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/auth'
    | '/demo/image-component-test'
    | '/demo/image-upload'
    | '/demo/video-component'
    | '/dashboard'
    | '/lesson'
    | '/dashboard/courses/create-course'
    | '/dashboard/analytics'
    | '/dashboard/courses'
    | '/dashboard/organization'
    | '/dashboard/users'
    | '/dashboard/wiki'
    | '/dashboard/courses/$courseSlug/$lessonSlug'
    | '/dashboard/courses/$courseSlug/edit'
    | '/dashboard/courses/$courseSlug'
    | '/dashboard/users/$userId'
  id:
    | '__root__'
    | '/'
    | '/dashboard'
    | '/auth'
    | '/demo/image-component-test'
    | '/demo/image-upload'
    | '/demo/video-component'
    | '/dashboard/'
    | '/lesson/'
    | '/dashboard/courses/create-course'
    | '/dashboard/analytics/'
    | '/dashboard/courses/'
    | '/dashboard/organization/'
    | '/dashboard/users/'
    | '/dashboard/wiki/'
    | '/dashboard/courses/$courseSlug/$lessonSlug'
    | '/dashboard/courses/$courseSlug/edit'
    | '/dashboard/courses/$courseSlug/'
    | '/dashboard/users/$userId/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DashboardRouteRoute: typeof DashboardRouteRouteWithChildren
  AuthRoute: typeof AuthRoute
  DemoImageComponentTestRoute: typeof DemoImageComponentTestRoute
  DemoImageUploadRoute: typeof DemoImageUploadRoute
  DemoVideoComponentRoute: typeof DemoVideoComponentRoute
  LessonIndexRoute: typeof LessonIndexRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/lesson/': {
      id: '/lesson/'
      path: '/lesson'
      fullPath: '/lesson'
      preLoaderRoute: typeof LessonIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard/': {
      id: '/dashboard/'
      path: '/'
      fullPath: '/dashboard/'
      preLoaderRoute: typeof DashboardIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/demo/video-component': {
      id: '/demo/video-component'
      path: '/demo/video-component'
      fullPath: '/demo/video-component'
      preLoaderRoute: typeof DemoVideoComponentRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/demo/image-upload': {
      id: '/demo/image-upload'
      path: '/demo/image-upload'
      fullPath: '/demo/image-upload'
      preLoaderRoute: typeof DemoImageUploadRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/demo/image-component-test': {
      id: '/demo/image-component-test'
      path: '/demo/image-component-test'
      fullPath: '/demo/image-component-test'
      preLoaderRoute: typeof DemoImageComponentTestRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard/wiki/': {
      id: '/dashboard/wiki/'
      path: '/wiki'
      fullPath: '/dashboard/wiki'
      preLoaderRoute: typeof DashboardWikiIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/users/': {
      id: '/dashboard/users/'
      path: '/users'
      fullPath: '/dashboard/users'
      preLoaderRoute: typeof DashboardUsersIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/organization/': {
      id: '/dashboard/organization/'
      path: '/organization'
      fullPath: '/dashboard/organization'
      preLoaderRoute: typeof DashboardOrganizationIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/courses/': {
      id: '/dashboard/courses/'
      path: '/courses'
      fullPath: '/dashboard/courses'
      preLoaderRoute: typeof DashboardCoursesIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/analytics/': {
      id: '/dashboard/analytics/'
      path: '/analytics'
      fullPath: '/dashboard/analytics'
      preLoaderRoute: typeof DashboardAnalyticsIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/courses/create-course': {
      id: '/dashboard/courses/create-course'
      path: '/courses/create-course'
      fullPath: '/dashboard/courses/create-course'
      preLoaderRoute: typeof DashboardCoursesCreateCourseRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/users/$userId/': {
      id: '/dashboard/users/$userId/'
      path: '/users/$userId'
      fullPath: '/dashboard/users/$userId'
      preLoaderRoute: typeof DashboardUsersUserIdIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/courses/$courseSlug/': {
      id: '/dashboard/courses/$courseSlug/'
      path: '/courses/$courseSlug'
      fullPath: '/dashboard/courses/$courseSlug'
      preLoaderRoute: typeof DashboardCoursesCourseSlugIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/courses/$courseSlug/edit': {
      id: '/dashboard/courses/$courseSlug/edit'
      path: '/courses/$courseSlug/edit'
      fullPath: '/dashboard/courses/$courseSlug/edit'
      preLoaderRoute: typeof DashboardCoursesCourseSlugEditRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/courses/$courseSlug/$lessonSlug': {
      id: '/dashboard/courses/$courseSlug/$lessonSlug'
      path: '/courses/$courseSlug/$lessonSlug'
      fullPath: '/dashboard/courses/$courseSlug/$lessonSlug'
      preLoaderRoute: typeof DashboardCoursesCourseSlugLessonSlugRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
  }
}

interface DashboardRouteRouteChildren {
  DashboardIndexRoute: typeof DashboardIndexRoute
  DashboardCoursesCreateCourseRoute: typeof DashboardCoursesCreateCourseRoute
  DashboardAnalyticsIndexRoute: typeof DashboardAnalyticsIndexRoute
  DashboardCoursesIndexRoute: typeof DashboardCoursesIndexRoute
  DashboardOrganizationIndexRoute: typeof DashboardOrganizationIndexRoute
  DashboardUsersIndexRoute: typeof DashboardUsersIndexRoute
  DashboardWikiIndexRoute: typeof DashboardWikiIndexRoute
  DashboardCoursesCourseSlugLessonSlugRoute: typeof DashboardCoursesCourseSlugLessonSlugRoute
  DashboardCoursesCourseSlugEditRoute: typeof DashboardCoursesCourseSlugEditRoute
  DashboardCoursesCourseSlugIndexRoute: typeof DashboardCoursesCourseSlugIndexRoute
  DashboardUsersUserIdIndexRoute: typeof DashboardUsersUserIdIndexRoute
}

const DashboardRouteRouteChildren: DashboardRouteRouteChildren = {
  DashboardIndexRoute: DashboardIndexRoute,
  DashboardCoursesCreateCourseRoute: DashboardCoursesCreateCourseRoute,
  DashboardAnalyticsIndexRoute: DashboardAnalyticsIndexRoute,
  DashboardCoursesIndexRoute: DashboardCoursesIndexRoute,
  DashboardOrganizationIndexRoute: DashboardOrganizationIndexRoute,
  DashboardUsersIndexRoute: DashboardUsersIndexRoute,
  DashboardWikiIndexRoute: DashboardWikiIndexRoute,
  DashboardCoursesCourseSlugLessonSlugRoute:
    DashboardCoursesCourseSlugLessonSlugRoute,
  DashboardCoursesCourseSlugEditRoute: DashboardCoursesCourseSlugEditRoute,
  DashboardCoursesCourseSlugIndexRoute: DashboardCoursesCourseSlugIndexRoute,
  DashboardUsersUserIdIndexRoute: DashboardUsersUserIdIndexRoute,
}

const DashboardRouteRouteWithChildren = DashboardRouteRoute._addFileChildren(
  DashboardRouteRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DashboardRouteRoute: DashboardRouteRouteWithChildren,
  AuthRoute: AuthRoute,
  DemoImageComponentTestRoute: DemoImageComponentTestRoute,
  DemoImageUploadRoute: DemoImageUploadRoute,
  DemoVideoComponentRoute: DemoVideoComponentRoute,
  LessonIndexRoute: LessonIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
