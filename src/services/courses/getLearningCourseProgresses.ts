import { UserCourse } from "@/types/courses";
import { Lesson, Section } from "@/types/lessons";

export const getLearningCourseProgresses = ({
  lessons,
  userCourse,
  isCourseCompleted,
}: {
  lessons: Lesson[] | undefined;
  userCourse: UserCourse | undefined;
  isCourseCompleted: boolean;
}) => {
  if (!lessons?.length || !userCourse) return 0;

  if (isCourseCompleted) return 100;

  let totalCompletedValue = 0;
  let hasAnyProgress = false;

  lessons.forEach((lesson) => {
    const progressStatus = userCourse.progresses[lesson.id];

    if (progressStatus === "COMPLETED") {
      totalCompletedValue += 1;
      hasAnyProgress = true;
    } else if (progressStatus && progressStatus !== "STARTED") {
      totalCompletedValue += 0.5;
      hasAnyProgress = true;
    }
  });

  if (hasAnyProgress) {
    return Math.floor((totalCompletedValue / lessons.length) * 100);
  }

  const currentLessonIndex = userCourse.continue_lesson
    ? lessons.findIndex((lesson) => lesson.id === userCourse.continue_lesson.id)
    : -1;

  const currentLesson = currentLessonIndex < 0 ? 0 : currentLessonIndex + 1;
  return Math.floor((currentLesson / lessons.length) * 100);
};

export const getLearningLessonProgresses = ({
  lesson,
  sections,
  currentSection,
}: {
  lesson?: Lesson;
  currentSection: Section | null;
  sections: Section[] | undefined;
}) => {
  if (currentSection === undefined && sections?.length) {
    return 100;
  }
  if (!sections || !sections.length || !lesson || !currentSection) {
    return 0;
  }

  const currentSectionId = currentSection.id;
  if (!currentSectionId) {
    return 0;
  }

  const completedSectionIndex = sections.findIndex((section) => {
    return currentSectionId === section.id;
  });

  if (completedSectionIndex === -1) {
    return 0;
  }

  return Math.round(((completedSectionIndex + 1) / sections.length) * 100);
};
