import { Lesson, Section } from "@/types/lessons";
import { getLearningLessonProgresses } from "./getLearningCourseProgresses";

describe("getLearningLessonProgresses", () => {
  const lesson = { id: "l1" } as unknown as Lesson;
  const sections = [
    { id: "s1" },
    { id: "s2" },
    { id: "s3" },
  ] as unknown as Section[];

  it("returns 0 if sections is undefined", () => {
    expect(
      getLearningLessonProgresses({
        lesson,
        sections: undefined,
        currentSection: sections[0],
      }),
    ).toBe(0);
  });

  it("returns 0 if sections is empty", () => {
    expect(
      getLearningLessonProgresses({
        lesson,
        sections: [],
        currentSection: sections[0],
      }),
    ).toBe(0);
  });

  it("returns 0 if lesson is undefined", () => {
    expect(
      getLearningLessonProgresses({
        lesson: undefined,
        sections,
        currentSection: sections[0],
      }),
    ).toBe(0);
  });

  it("returns 0 if currentSection is null", () => {
    expect(
      getLearningLessonProgresses({ lesson, sections, currentSection: null }),
    ).toBe(100);
  });

  it("returns 0 if currentSection.id not found in sections", () => {
    expect(
      getLearningLessonProgresses({
        lesson,
        sections,
        currentSection: {
          id: "asdsa-sadsad-asdasd-asdsad-sadsad",
        } as unknown as Section,
      }),
    ).toBe(0);
  });

  it("returns correct progress for first section", () => {
    expect(
      getLearningLessonProgresses({
        lesson,
        sections,
        currentSection: sections[0],
      }),
    ).toBe(33);
  });

  it("returns correct progress for second section", () => {
    expect(
      getLearningLessonProgresses({
        lesson,
        sections,
        currentSection: sections[1],
      }),
    ).toBe(67);
  });

  it("returns 100 for last section", () => {
    expect(
      getLearningLessonProgresses({
        lesson,
        sections,
        currentSection: sections[2],
      }),
    ).toBe(100);
  });

  it("returns 100 for single section", () => {
    expect(
      getLearningLessonProgresses({
        lesson,
        sections: [sections[0]],
        currentSection: sections[0],
      }),
    ).toBe(100);
  });
});
