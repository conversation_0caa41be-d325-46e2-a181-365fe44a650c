import { useQuery } from "@tanstack/react-query";
import { User } from "@/types/auth";
import { API_URL, req } from "@/utils/api";
import {
  COURSE_PERMISSION,
  Department,
  GroupPermission,
  PERMISSION,
} from "./../types/organization";

// group
export const getGroups = () => {
  return req<GroupPermission[]>(`${API_URL}/api/v1/admin/groups`, {
    withAuth: true,
  });
};

export const useGroupsPermissions = () => {
  return useQuery({
    queryKey: ["admin-groups"],
    queryFn: async () => {
      try {
        const groups = await getGroups();
        return groups || [];
      } catch (error) {
        console.error("Error fetching groups:", error);
        return [];
      }
    },
  });
};

export const createGroup = (params: Omit<GroupPermission, "id">) => {
  return req(`${API_URL}/api/v1/admin/groups`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const updateGroup = (
  params: Omit<GroupPermission, "users" | "name">,
) => {
  return req(`${API_URL}/api/v1/admin/groups/${params.id}`, {
    method: "PATCH",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const deleteGroup = (groupId: string) => {
  return req(`${API_URL}/api/v1/admin/groups/${groupId}`, {
    method: "DELETE",
    withAuth: true,
  });
};

export const addMembersToGroup = (params: {
  group_id: string;
  user_ids: string[];
}) => {
  return req(`${API_URL}/api/v1/admin/groups/${params.group_id}/members`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params.user_ids),
  });
};

export const updateGroupPermissionMembers = (params: {
  group_id: string;
  added_users: string[];
  removed_users: string[];
}) => {
  return req(`${API_URL}/api/v1/admin/groups/${params.group_id}/members`, {
    method: "PUT",
    withAuth: true,
    body: JSON.stringify({
      added_users: params.added_users,
      removed_users: params.removed_users,
    }),
  });
};

// department

export const getDepartments = () => {
  return req<Department[]>(`${API_URL}/api/v1/admin/departments`, {
    withAuth: true,
  });
};

export const useDepartments = () => {
  return useQuery({
    queryKey: ["admin-departments"],
    queryFn: async () => {
      try {
        const departments = await getDepartments();
        return departments || [];
      } catch (error) {
        console.error("Error fetching departments:", error);
        return [];
      }
    },
  });
};

export const createDepartment = (params: Omit<Department, "id" | "users">) => {
  return req(`${API_URL}/api/v1/admin/departments`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const updateDepartment = (
  params: Omit<Department, "users" | "name">,
) => {
  return req(`${API_URL}/api/v1/admin/departments/${params.id}`, {
    method: "PATCH",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const deleteDepartment = (departmentId: string) => {
  return req(`${API_URL}/api/v1/admin/departments/${departmentId}`, {
    method: "DELETE",
    withAuth: true,
  });
};

export const addMembersToDepartment = (params: {
  department_id: string;
  user_ids: string[];
}) => {
  return req(
    `${API_URL}/api/v1/admin/departments/${params.department_id}/members`,
    {
      method: "POST",
      withAuth: true,
      body: JSON.stringify(params.user_ids),
    },
  );
};

export const updateDepartmentMembers = (params: {
  department_id: string;
  added_users: string[];
  removed_users: string[];
}) => {
  return req(
    `${API_URL}/api/v1/admin/departments/${params.department_id}/members`,
    {
      method: "PUT",
      withAuth: true,
      body: JSON.stringify({
        added_users: params.added_users,
        removed_users: params.removed_users,
      }),
    },
  );
};

// user

export const importUsers = (params: { name: string; email: string }[]) => {
  return req(`${API_URL}/api/v1/admin/users/import`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const getUsers = () => {
  return req<User[]>(`${API_URL}/api/v1/admin/users`, {
    withAuth: true,
  });
};

export const useUsers = () => {
  return useQuery({
    queryKey: ["admin-users"],
    queryFn: async () => {
      try {
        const users = await getUsers();
        return users || [];
      } catch (error) {
        console.error("Error fetching users:", error);
        return [];
      }
    },
  });
};

export const deleteUser = (userIds: string[]) => {
  return req(`${API_URL}/api/v1/admin/users`, {
    method: "DELETE",
    withAuth: true,
    body: JSON.stringify(userIds),
  });
};

export const createUser = (params: { name: string; email: string }) => {
  return req<User[]>(`${API_URL}/api/v1/admin/users/import`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify([params]), // Wrap in array since import endpoint expects array
  });
};

// course

export const getCoursePermissions = (courseId: string) => {
  return req(`${API_URL}/api/v1/admin/courses/${courseId}/permissions`, {
    withAuth: true,
  });
};

export const useCoursePermissions = (courseId: string) => {
  return useQuery({
    queryKey: ["course-permissions", courseId],
    queryFn: () => getCoursePermissions(courseId),
    enabled: !!courseId,
  });
};

export const addCoursePermission = (
  params: {
    course_id: string;
    group_id: string;
    permission: COURSE_PERMISSION;
  }[],
) => {
  return req(`${API_URL}/api/v1/admin/courses/permissions/add`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};

export const removeCoursePermission = (
  params: {
    course_id: string;
    group_id: string;
    permission: COURSE_PERMISSION;
  }[],
) => {
  return req(`${API_URL}/api/v1/admin/courses/permissions/remove`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(params),
  });
};
