import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import { VideoComponent } from "@/components/lesson-editor/components";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export const Route = createFileRoute("/demo/video-component")({
  component: VideoComponentDemo,
});

function VideoComponentDemo() {
  const [content, setContent] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const sampleVideos = [
    {
      name: "YouTube Video",
      url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
      description: "Sample YouTube video",
    },
    {
      name: "Vimeo Video",
      url: "https://vimeo.com/148751763",
      description: "Sample Vimeo video",
    },
    {
      name: "Direct MP4",
      url: "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
      description: "Direct video file",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="mx-auto max-w-6xl space-y-6 p-6">
        <Card>
          <CardHeader>
            <CardTitle>Video Component Demo</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Controls */}
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h3 className="mb-4 font-semibold text-lg">Current State</h3>
                <div className="space-y-2 text-sm">
                  <p>
                    <strong>Content:</strong> {content || "(empty)"}
                  </p>
                  <p>
                    <strong>Is Editing:</strong> {isEditing ? "Yes" : "No"}
                  </p>
                  <p>
                    <strong>Is Preview Mode:</strong>{" "}
                    {isPreviewMode ? "Yes" : "No"}
                  </p>
                </div>

                <div className="mt-4 space-y-2">
                  <h4 className="font-medium">Controls:</h4>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      size="sm"
                      variant={isEditing ? "default" : "outline"}
                      onClick={() => setIsEditing(!isEditing)}
                    >
                      {isEditing ? "Stop Edit" : "Start Edit"}
                    </Button>
                    <Button
                      size="sm"
                      variant={isPreviewMode ? "default" : "outline"}
                      onClick={() => setIsPreviewMode(!isPreviewMode)}
                    >
                      {isPreviewMode ? "Exit Preview" : "Preview"}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setContent("")}
                    >
                      Clear
                    </Button>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="mb-4 font-semibold text-lg">Sample Videos</h3>
                <div className="space-y-2">
                  {sampleVideos.map((video, index) => (
                    <div key={index} className="rounded border p-3">
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-medium text-sm">{video.name}</h4>
                          <p className="mt-1 text-gray-600 text-xs">
                            {video.description}
                          </p>
                          <p className="mt-1 truncate text-gray-500 text-xs">
                            {video.url}
                          </p>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setContent(video.url)}
                        >
                          Load
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Component Display */}
            <div>
              <h3 className="mb-4 font-semibold text-lg">Video Component</h3>
              <div className="min-h-[400px] rounded-lg border bg-white p-4">
                <VideoComponent
                  content={content}
                  isPreviewMode={isPreviewMode}
                  isEditing={isEditing}
                  onUpdate={(newContent) => {
                    console.log("Video content updated:", newContent);
                    setContent(newContent);
                  }}
                  onToggleEdit={() => {
                    console.log("Toggle edit");
                    setIsEditing(!isEditing);
                  }}
                  onDelete={() => {
                    console.log("Delete component");
                    setContent("");
                  }}
                />
              </div>
            </div>

            {/* Features */}
            <div>
              <h3 className="mb-4 font-semibold text-lg">Features</h3>
              <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
                <div className="space-y-2">
                  <h4 className="font-medium">Supported Platforms:</h4>
                  <ul className="list-inside list-disc space-y-1 text-gray-600">
                    <li>YouTube (youtube.com, youtu.be)</li>
                    <li>Vimeo (vimeo.com)</li>
                    <li>Direct video files (MP4, WebM, etc.)</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Features:</h4>
                  <ul className="list-inside list-disc space-y-1 text-gray-600">
                    <li>URL input with Enter key support</li>
                    <li>Automatic platform detection</li>
                    <li>Responsive video player</li>
                    <li>Edit/Preview modes</li>
                    <li>Hover controls + fixed button</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Instructions */}
            <div className="rounded-lg bg-blue-50 p-4">
              <h4 className="mb-2 font-medium text-blue-900">How to test:</h4>
              <ol className="list-inside list-decimal space-y-1 text-blue-800 text-sm">
                <li>Click "Start Edit" to enter edit mode</li>
                <li>Use sample videos or enter your own URL</li>
                <li>Press Enter or click "Add Video" to confirm</li>
                <li>Toggle between Edit/Preview modes</li>
                <li>Test hover controls and fixed edit button</li>
              </ol>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
