import { useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { CourseEditor } from "@/components/courses/CourseEditor";
import { courseQueryOptions } from "@/services/courses/index";
import { CourseFormData } from "@/types/courses";

export const Route = createFileRoute("/dashboard/courses/$courseSlug/edit")({
  loader: async ({ params: { courseSlug }, context }) => {
    await context.queryClient.ensureQueryData(courseQueryOptions(courseSlug));
    return { courseSlug };
  },
  component: RouteComponent,
});

function RouteComponent() {
  const { courseSlug } = Route.useLoaderData();
  const { data: courseData } = useSuspenseQuery(courseQueryOptions(courseSlug));

  // Convert Course data to CourseFormData format for the editor
  const courseFormData: CourseFormData = {
    id: courseData.id,
    name: courseData.name || courseData.title,
    title: courseData.title,
    slug: courseData.slug,
    description: courseData.description,
    instructor_id: courseData.instructor_id,
    image_url: courseData.image_url,
    duration: courseData.duration,
    level: courseData.level,
    language: courseData.language,
    status: courseData.status,
    requirement: courseData.requirement,
    categories: courseData.categories,
    created_at: courseData.created_at,
    updated_at: courseData.updated_at,
  };

  return (
    <div>
      <CourseEditor selectedCourse={courseFormData} />
    </div>
  );
}
