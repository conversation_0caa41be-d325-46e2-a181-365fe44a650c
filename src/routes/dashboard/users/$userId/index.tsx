import { createFileRoute, <PERSON> } from "@tanstack/react-router";
import { useAtomValue } from "jotai";
import { ChevronRight, GraduationCap, User } from "lucide-react";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  UserCertificates,
  UserChartsOverview,
  UserCoursesTable,
  UserProfileCard,
  UserStatsCards,
} from "@/components/user-detail";
import { getUserByIdAtom } from "@/store/users/values";
import type { SkillData, UserCertificate, UserCourse } from "@/types/users";

export const Route = createFileRoute("/dashboard/users/$userId/")({
  component: UserDetailPage,
});

function UserDetailPage() {
  const { userId } = Route.useParams();

  // Get user data from jotai store using getUserByIdAtom
  const getUserById = useAtomValue(getUserByIdAtom);
  const userData = getUserById(userId);

  // Handle case where user is not found
  if (!userData) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="mb-2 font-bold text-2xl text-foreground">
            Không tìm thấy người dùng
          </h1>
          <p className="mb-4 text-muted-foreground">
            Người dùng với ID {userId} không tồn tại.
          </p>
          <Link to="/dashboard/users" className="text-primary hover:underline">
            Quay lại danh sách người dùng
          </Link>
        </div>
      </div>
    );
  }

  // Generate mock course data for the user
  const userCourses: UserCourse[] = [
    {
      id: 1,
      title: "React Fundamentals",
      category: "Development",
      enrollDate: "2024-03-01",
      completedDate: (userData.coursesCompleted || 0) > 0 ? "2024-03-15" : null,
      progress: (userData.coursesCompleted || 0) > 0 ? 100 : 75,
      status:
        (userData.coursesCompleted || 0) > 0 ? "completed" : "in-progress",
      score: (userData.coursesCompleted || 0) > 0 ? userData.averageScore : 0,
      xp: (userData.coursesCompleted || 0) > 0 ? 150 : 120,
      timeSpent: "12 giờ",
      lastAccess: "1 tuần trước",
      certificate:
        (userData.coursesCompleted || 0) > 0
          ? "React Developer Certificate"
          : null,
    },
    {
      id: 2,
      title: "JavaScript Mastery",
      category: "Development",
      enrollDate: "2024-03-05",
      completedDate: (userData.coursesCompleted || 0) > 1 ? "2024-03-20" : null,
      progress: (userData.coursesCompleted || 0) > 1 ? 100 : 60,
      status:
        (userData.coursesCompleted || 0) > 1 ? "completed" : "in-progress",
      score:
        (userData.coursesCompleted || 0) > 1
          ? (userData.averageScore || 0) - 5
          : 0,
      xp: (userData.coursesCompleted || 0) > 1 ? 180 : 90,
      timeSpent: "8 giờ",
      lastAccess: "2 giờ trước",
      certificate:
        (userData.coursesCompleted || 0) > 1
          ? "JavaScript Expert Certificate"
          : null,
    },
    {
      id: 3,
      title: "UI/UX Design Basics",
      category: "Design",
      enrollDate: "2024-03-10",
      completedDate: (userData.coursesCompleted || 0) > 2 ? "2024-04-01" : null,
      progress: (userData.coursesCompleted || 0) > 2 ? 100 : 45,
      status:
        (userData.coursesCompleted || 0) > 2 ? "completed" : "in-progress",
      score:
        (userData.coursesCompleted || 0) > 2
          ? (userData.averageScore || 0) - 10
          : 0,
      xp: (userData.coursesCompleted || 0) > 2 ? 120 : 65,
      timeSpent: "5 giờ",
      lastAccess: "1 ngày trước",
      certificate:
        (userData.coursesCompleted || 0) > 2
          ? "UI/UX Design Certificate"
          : null,
    },
  ];

  // Generate certificates from completed courses
  const userCertificates: UserCertificate[] = userCourses
    .filter((course) => course.certificate)
    .map((course, index) => ({
      id: index + 1,
      name: course.certificate!,
      course: course.title,
      issueDate: course.completedDate!,
      score: course.score || 0,
      validUntil: `2025-${course.completedDate!.split("-")[1]}-${course.completedDate!.split("-")[2]}`,
    }));

  // Calculate statistics
  const totalCompletedCourses = userCourses.filter(
    (course) => course.status === "completed",
  ).length;
  const totalXP = userCourses.reduce((sum, course) => sum + course.xp, 0);
  const totalScore = userCourses.reduce(
    (sum, course) => sum + (course.score || 0),
    0,
  );
  const totalStudyTime = userCourses.reduce((sum, course) => {
    const hours = Number.parseInt(course.timeSpent.split(" ")[0]);
    return sum + hours;
  }, 0);

  // Chart data
  const progressData = [
    { name: "Hoàn thành", value: totalCompletedCourses, color: "#10b981" },
    {
      name: "Đang học",
      value: userCourses.length - totalCompletedCourses,
      color: "#3b82f6",
    },
  ];

  const scoreData = userCourses
    .filter((course) => (course.score || 0) > 0)
    .map((course) => ({
      name:
        course.title.length > 15
          ? course.title.substring(0, 15) + "..."
          : course.title,
      score: course.score || 0,
    }));

  const skillData: SkillData[] = [
    { skill: "Development", value: userData.averageScore || 0 },
    { skill: "Design", value: (userData.averageScore || 0) - 10 },
    { skill: "Frontend", value: (userData.averageScore || 0) + 5 },
    { skill: "Backend", value: (userData.averageScore || 0) - 5 },
    { skill: "TypeScript", value: userData.averageScore || 0 },
    { skill: "React", value: (userData.averageScore || 0) + 10 },
  ];

  return (
    <div className="flex h-screen bg-background">
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* Header with breadcrumb */}
        <div className="border-b bg-background px-6 py-4">
          <nav className="mb-3 flex items-center space-x-2 text-muted-foreground text-sm">
            <Link
              to="/dashboard/users"
              className="transition-colors hover:text-foreground"
            >
              Quản lý người dùng
            </Link>
            <ChevronRight className="h-4 w-4" />
            <span className="font-medium text-foreground">{userData.name}</span>
          </nav>

          <div className="flex items-center gap-4">
            <div>
              <h1 className="font-bold text-2xl text-foreground">
                Chi tiết người dùng
              </h1>
              <p className="text-muted-foreground">
                Xem thông tin và tiến độ học tập của người dùng
              </p>
            </div>
          </div>
        </div>

        {/* Main content */}
        <main className="flex-1 overflow-y-auto p-6">
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Tổng quan
              </TabsTrigger>
              <TabsTrigger value="learning" className="flex items-center gap-2">
                <GraduationCap className="h-4 w-4" />
                Khóa học & Tiến độ
              </TabsTrigger>
              {/* <TabsTrigger value="skills" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Phân tích kỹ năng
              </TabsTrigger> */}
            </TabsList>

            {/* Tab Tổng quan */}
            <TabsContent value="overview" className="space-y-6">
              {/* User Profile Card */}
              <UserProfileCard userData={userData} />

              {/* Learning Statistics */}
              <UserStatsCards
                totalCompletedCourses={totalCompletedCourses}
                totalXP={totalXP}
                totalStudyTime={totalStudyTime}
                totalCertificates={userCertificates.length}
              />

              {/* Charts Overview */}
              <UserChartsOverview
                progressData={progressData}
                scoreData={scoreData}
                skillData={skillData}
              />
            </TabsContent>

            {/* Tab Learning Progress */}
            <TabsContent value="learning" className="space-y-6">
              {/* Course Progress Table */}
              <UserCoursesTable courses={userCourses} />

              {/* Certificates Section */}
              <UserCertificates certificates={userCertificates} />
            </TabsContent>

            {/* Tab Skills Analysis */}
            {/* <TabsContent value="skills" className="space-y-6"> */}
            {/* Strengths and Weaknesses */}
            {/* <UserSkillsOverview averageScore={userData.averageScore || 0} /> */}

            {/* Detailed Skills Analysis */}
            {/* <UserSkillsAnalysis skillData={skillData} /> */}

            {/* Skills Progress Table */}
            {/* <UserSkillsTable averageScore={userData.averageScore || 0} /> */}

            {/* Skills Progress Trend */}
            {/* <UserSkillsTrend averageScore={userData.averageScore || 0} /> */}
            {/* </TabsContent> */}
          </Tabs>
        </main>
      </div>
    </div>
  );
}
