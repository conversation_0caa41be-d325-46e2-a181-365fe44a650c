import { createFileRoute } from "@tanstack/react-router";
import SidebarMenu from "@/components/wiki/sidebar";
import WikiEditor from "@/components/wiki/wiki-editor";

export const Route = createFileRoute("/dashboard/wiki/")({
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <div className="flex h-full">
      <div className="sticky top-2 h-full max-h-[calc(100vh-9rem)] w-full max-w-80 flex-shrink-0 overflow-y-auto p-4 pr-2 ">
        <SidebarMenu />
      </div>
      <div className="h-full border-gray-300 border-r"></div>
      <div className="h-full flex-1 pl-2">
        <WikiEditor />
      </div>
    </div>
  );
}
