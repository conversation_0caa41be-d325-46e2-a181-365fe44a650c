import { createFileRoute, Outlet } from "@tanstack/react-router";
import data from "@/app/dashboard/data.json";
import { AppSidebar } from "@/components/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { requireAuth } from "@/utils/auth-guard";

export const Route = createFileRoute("/dashboard")({
  beforeLoad: requireAuth,
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset style={{ maxWidth: "calc(100% - var(--sidebar-width))" }}>
        <Outlet />
      </SidebarInset>
    </SidebarProvider>
  );
}
