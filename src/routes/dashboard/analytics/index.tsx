import { createFileRoute } from "@tanstack/react-router";
import {
  CourseStats,
  DepartmentTable,
  LearnerStats,
  TopCoursesCard,
  TopPerformersCard,
} from "@/components/analytics";

// Sample data based on the example.tsx structure
import {
  departmentStats,
  learningTrendData,
  popularCoursesData,
  topPerformersData,
} from "@/components/analytics/data";

export const Route = createFileRoute("/dashboard/analytics/")({
  component: RouteComponent,
});

/**
 * Main analytics page component
 * Displays comprehensive learning analytics with multiple charts and metrics
 */
function RouteComponent() {
  // Handle navigation to course details
  const handleCourseClick = (courseId: string) => {
    console.log("Navigate to course:", courseId);
    // TODO: Implement navigation using router
  };

  return (
    <div>
      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 px-4 py-4 md:gap-6 md:px-6 md:py-6">
            {/* Learner statistics section */}
            <LearnerStats
              learningTrendData={learningTrendData}
              departmentStats={departmentStats}
            />

            {/* Course statistics section */}
            <CourseStats learningTrendData={learningTrendData} />

            {/* Department performance table */}
            <DepartmentTable departmentStats={departmentStats} />

            {/* Top courses and performers grid */}
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <TopCoursesCard coursesData={popularCoursesData} />
              <TopPerformersCard performersData={topPerformersData} />
            </div>

            {/* Recent activities */}
            {/* <RecentActivities activities={recentActivitiesData} /> */}
          </div>
        </div>
      </div>
    </div>
  );
}
