import { createFileRoute, redirect } from "@tanstack/react-router";
import logoImg from "@/assets/images/aicademy-logo.png";
import { LoginMenu } from "@/components/auth";
import { serverFnAuthHint } from "@/server/authHint";
import { requireAuth } from "@/utils/auth-guard";

export const Route = createFileRoute("/auth")({
  beforeLoad: async (context) => {
    const authHint = await serverFnAuthHint();

    if (authHint?.length === 2 && authHint[1] * 1000 > Date.now()) {
      throw redirect({ to: "/dashboard" });
    }
  },
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-100 dark:from-gray-900 dark:via-gray-800 dark:to-indigo-900">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="w-full max-w-md">
          {/* Background Card */}
          <div className="space-y-6 rounded-2xl border border-white/20 bg-white/80 p-8 shadow-2xl backdrop-blur-lg">
            {/* Header */}
            <div className="space-y-2 text-center">
              <img src={logoImg} alt="" />
              <h2 className="text-center font-medium text-2xl">Đăng nhập</h2>
            </div>

            {/* Login Form */}
            <LoginMenu />
          </div>

          {/* Footer */}
          <p className="mt-8 text-center text-gray-500 text-sm">
            © 2024 AiCademy. Tất cả quyền được bảo lưu.
          </p>
        </div>
      </div>
    </div>
  );
}
