import { createServerFn } from "@tanstack/react-start";
import { getHeaders } from "@tanstack/react-start/server";
import { parse } from "cookie-es";
import { UUID } from "@/types/app";

export const serverFnAuthHint = createServerFn().handler(async () => {
  const authHint = await getAuthHint();
  return authHint;
});

const getAuthHint = (): AuthHint | undefined => {
  const headers = getHeaders();
  const cookies = parse(headers.cookie || "");
  if (!cookies["auth:hint"]) {
    return;
  }

  const parts = cookies["auth:hint"].split(":");
  if (parts.length !== 2) {
    return;
  }

  return [parts[0] as UUID, Number(parts[1])];
};

export type AuthHint = [UUID, number];
